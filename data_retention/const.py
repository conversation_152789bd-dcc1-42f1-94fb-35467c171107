import os

RAC_TRANSFORM_COLLECTION_NAME_METADATA_KEY = "RAC_TRANSFORM_COLLECTION_NAME"
RAC_COLLECTION_NAME_METADATA_KEY = "RAC_COLLECTION_NAME"
QUERY_COLLECTION_NAME = os.getenv("QUERY_COLLECTION_NAME")
RAC_TRANSFORM_COLLECTION_SUFFIX = os.getenv("RAC_TRANSFORM_COLLECTION_SUFFIX")
RAC_COLLECTION_NAME_METADATA_KEY = "RAC_COLLECTION_NAME"
PROJECT_ID = os.getenv("PROJECT_ID")
PLATFORM_MONGODB_URL = os.getenv("PLATFORM_MONGODB_URL")

ORGANIZATION_VAULT_BASE_URL = os.getenv("ORGANIZATION_VAULT_BASE_URL")

CAPABILITIES_DATABASE = os.getenv("CAPABILITIES_DATABASE")
DATA_RETENTION_TOPIC_ID = os.getenv("DATA_RETENTION_TOPIC_ID")


QUERY_PROJECTION_ATTRIBUTES = {
    "name": 1,
    "meta_data": 1,
    "source.data_source_id": 1,
    "user_id": 1,
    "RAC_COLLECTION_NAME": 1,
    "RAC_TRANSFORM_COLLECTION_NAME": 1,
}

INDEXES_TO_BE_DELETED = ["id_1"]

RETENTION_DAYS = 30
RETENTION_DAYS_FOR_YOUTUBE = 7

COMMON_FIELDS = [
    "Unique_Story_ID",
    "cluster_sentiment",
    "EMOJIS_Unique_Count",
    "Lemitized",
    "theme_summary",
    "cluster_summary",
    "Stories",
    "date_utc",
    "cluster_sentiment_reasoning",
    "Hashtag_Position",
    "Hashtag_Unique",
    "date_utc_str",
    "Unique_Cluster_ID",
    "encapsulation_marker",
    "story_summary",
    "Phrase",
    "Hashtag_Unique_Count",
    "date",
    "_id",
    "cluster_id",
    "EMOJIS_Unique",
    "like_count",
    "Record_type",
    "theme_sentiment",
    "story_sentiment_reasoning",
    "story_sentiment",
    "Theme_sentiment_reasoning",
    "Reply_Count",
    "Repost_Count",
    "View_Count",
    "EMOJIS",
    "Keyword",
    "snippet_totalReplyCount",
]


YOUTUBE_FIELDS = [
    "reply_count",
    "Hashtag",
    "video_id",
    "like_count",
    "Themes",
    "replies",
    "video_info_statistics_viewCount",
    "video_info_statistics_likeCount",
    "video_info_statistics_commentCount",
]
