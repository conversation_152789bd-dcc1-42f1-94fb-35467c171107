import json
import base64
from bson import ObjectId
from pymongo import <PERSON>goClient
from bson.objectid import ObjectId
from datetime import datetime, timedelta
import pytz
from google.cloud import bigquery
from cloudevents.http import CloudEvent
import functions_framework
from google.cloud import storage


from utils.logger import logger
from utils.mongo_db import (
    get_mongodb_client,
    get_mongodb_db,
    get_mongodb_collection,
)
from utils.common_utils import (
    get_query_config,
)
import traceback
from utils.utilities import (
    DataSourceId,
    OrganizationAccountInfo,
    MetaContainer,
)
from utils.common_utils import delete_indexes


from const import (
    COMMON_FIELDS,
    RETENTION_DAYS,
    RETENTION_DAYS_FOR_YOUTUBE,
    YOUTUBE_FIELDS,
    QUERY_COLLECTION_NAME,
    RAC_COLLECTION_NAME_METADATA_KEY,
    QUERY_PROJECTION_ATTRIBUTES,
    RAC_TRANSFORM_COLLECTION_NAME_METADATA_KEY,
    RAC_TRANSFORM_COLLECTION_SUFFIX,
    PROJECT_ID,
    CAPABILITIES_DATABASE,
    PLATFORM_MONGODB_URL,
    INDEXES_TO_BE_DELETED,
    DATA_RETENTION_TOPIC_ID,
)


def format_time(td):
    """
    Format timedelta into a human-readable string representation of days, hours, minutes, and seconds.

    Parameters:
    - td (timedelta): The timedelta object to be formatted.

    Returns:
    - date(str): A formatted string representing the timedelta in the format "X days, HH:MM:SS".

    """
    return f"{td.days} days, {td.seconds//3600}:{(td.seconds//60)%60:02}:{td.seconds%60:02}"


def delete_documents_in_collection(organization_db, collection_names, document_ids):
    """
    Deletes specific documents from multiple MongoDB collections using document IDs.

    Parameters:
    - organization_db (Database): The MongoDB database instance containing the collections.
    - collection_names (list): List of collection names to delete documents from.
    - document_ids (list): List of document IDs to delete.

    Exception Handling:
    - Logs an error if an exception occurs during the document deletion process.
    - Raises an HTTPException with status code 500 if the document deletion fails.
    """

    try:
        # Ensure document_ids are ObjectIds
        object_ids = [ObjectId(id) for id in document_ids]
        query = {"query_id": {"$in": object_ids}}

        for collection_name in collection_names:
            collection = organization_db[collection_name]
            # Check if the collection exists
            if collection is None:
                logger.warning(
                    f"Collection '{collection_name}' not found in the database."
                )
                continue

            # Delete documents based on the query
            result = collection.delete_many(query)

            if result.deleted_count > 0:
                logger.info(
                    f"Successfully deleted {result.deleted_count} document(s) from '{collection_name}' collection."
                )
            else:
                logger.warning(
                    f"No documents found to delete in '{collection_name}' collection."
                )

    except Exception as e:
        logger.error(f"Error occurred while deleting documents: {e}")
        raise Exception(
            f"Failed to delete documents from {collection_names} due to error: {e}"
        )


def delete_documents_in_diagnostic_collection(
    capabilities_db, collection_name, document_ids, org_ids
):
    """
    Deletes documents with specific query and organization IDs from a diagnostic collection.

    Parameters:
    - db (Database): The MongoDB database instance containing the collection.
    - collection_name (str): The name of the collection to delete documents from.
    - document_ids (list): List of query IDs for the documents to delete.
    - org_ids (list): List of organization IDs for the documents to delete.

    Exception Handling:
    - Logs an error if an exception occurs during the document deletion process.
    - Raises an HTTPException with status code 500 if the document deletion fails.
    """

    try:
        collection = capabilities_db[collection_name]
        object_ids = [ObjectId(id) for id in document_ids]
        org_object_ids = [ObjectId(id) for id in org_ids]
        query = {
            "organization_id": {"$in": org_object_ids},
            "query_id": {"$in": object_ids},
        }

        result = collection.delete_many(query)
        logger.info(
            f"Deleted {result.deleted_count} documents from '{collection_name}' collection."
        )

        if result.deleted_count > 0:
            logger.info(
                f"Deleted {result.deleted_count} documents from '{collection.name}' collection."
            )
        else:
            logger.warning(
                f"No documents found to delete in '{collection.name}' collection."
            )
    except Exception as e:
        logger.error(
            f"Error occurred while deleting documents in '{collection_name}': {e}"
        )


def update_unwanted_fields(
    rac_transform_collection, YOUTUBE_FIELD, COMMON_FIELD, data_source_id
):
    """
    Updates unwanted fields in documents of a collection to null or unset based on the fields to retain.

    Parameters:
    - rac_transform_collection (Collection): The MongoDB collection where documents need to be updated.
    - YOUTUBE_FIELD (list): List of fields to retain for YouTube comments.
    - COMMON_FIELD (list): List of common fields to retain.
    - data_source_id (str): The ID of the data source.

    Returns:
    - result (UpdateResult | None): The result of the update operation, or None if no fields were updated.
    """
    pipeline = [{"$project": {"_id": 0}}, {"$limit": 1000}]
    all_fields = set()
    for doc in rac_transform_collection.aggregate(pipeline):
        all_fields.update(doc.keys())

    if data_source_id == DataSourceId.YT_COMMENTS.value:
        combined_fields = set(YOUTUBE_FIELD).union(COMMON_FIELD)
        fields_to_nullify = all_fields - combined_fields
    else:
        fields_to_nullify = all_fields - set(COMMON_FIELD)
    if fields_to_nullify:
        return rac_transform_collection.update_many(
            {}, {"$set": {field: None for field in fields_to_nullify}}
        )

    return None


def update_bigquery_table(organization_db_name, rac_transform_collection_name):
    """
    Updates a BigQuery table by setting fields not in the retention list to NULL.

    Parameters:
    - organization_db_name (str): The name of the organization’s database in BigQuery.
    - rac_transform_collection_name (str): The name of the collection to update in BigQuery.
    Exception Handling:
    - Logs any errors that occur during the BigQuery update process.

    Returns:
    - None

    """
    client = bigquery.Client()
    table_id = f"{PROJECT_ID}.{organization_db_name}.{rac_transform_collection_name}"
    # Get schema to identify columns
    schema = client.get_table(table_id).schema
    all_fields = [field.name for field in schema]

    # Identify fields that are not in fields_to_retain
    fields_to_nullify = [field for field in all_fields if field not in FIELDS_TO_RETAIN]

    # Construct the SQL to update fields not retained to NULL
    set_clause = ", ".join([f"{field} = NULL" for field in fields_to_nullify])
    update_query = f"""
        UPDATE `{table_id}`
        SET {set_clause}
        WHERE TRUE
    """
    try:
        client.query(update_query).result()
        logger.info(f"BigQuery table '{table_id}' updated successfully.")
    except Exception as e:
        logger.info(f"An error occurred while updating BigQuery: {e}")


def delete_query_folder_from_storage(
    query_archive_bucket_name, organization_id, user_id, data_source_id, query_id
):
    """
    Deletes a specific query folder and its contents from Google Cloud Storage.

    Parameters:
    - query_archive_bucket_name (str): The name of the Google Cloud Storage bucket.
    - organization_id (str): The ID of the organization owning the data.
    - user_id (str): The ID of the user who initiated the query.
    - data_source_id (str): The ID of the data source.
    - query_id (str): The ID of the query whose folder to delete.

    Exception Handling:
    - Logs a warning if an error occurs during the deletion process.

    Returns:
    - None
    """
    try:
        # Initialize the storage client
        storage_client = storage.Client()

        # Construct the folder path (prefix for objects inside that folder)
        bucket_path = f"{organization_id}/{user_id}/{data_source_id}/{query_id}"
        full_bucket_path = f"gs://{query_archive_bucket_name}/{bucket_path}"

        # Get the bucket dynamically based on the organization-specific bucket name
        bucket = storage_client.get_bucket(query_archive_bucket_name)

        # List all objects under the folder (prefix) and its subfolders
        blobs = bucket.list_blobs(prefix=bucket_path)

        # Delete each object under the folder
        deleted_count = 0
        for blob in blobs:
            logger.info(f"Deleting {blob.name}")
            blob.delete()
            deleted_count += 1

        if deleted_count > 0:
            logger.info(f"All objects under {full_bucket_path} have been deleted.")
        else:
            logger.info(f"No objects found under {full_bucket_path}.")

        # After deleting all objects, the query_id folder is effectively deleted.
        logger.info(f"The folder '{bucket_path}' has been effectively deleted.")

    except Exception as e:
        logger.warning(f"An error occurred while deleting from storage: {e}")


def update_meta_date_exceeded(query_collection, query_id):
    """
    Updates the metadata of a document by setting 'meta_data.IS_RETENTION_EXPIRED' to True.

    Parameters:
    - query_collection (Collection): The MongoDB collection containing the query document.
    - query_id (str): The ID of the query document to update.

    Exception Handling:
    - Logs an error if an exception occurs during the metadata update.

    Returns:
    - result (UpdateResult | None): The result of the update operation, or None if no update was made.
    """
    try:
        # Add the 'date_exceeded: true' field to the meta data for the given query_id
        result = query_collection.update_one(
            {"_id": ObjectId(query_id)},
            {
                "$set": {"meta_data.IS_RETENTION_EXPIRED": True}
            },  # Add the IS_RETENTION_EXPIRED field to the meta data
        )
        return result
    except Exception as e:
        logger.info(f"Error updating meta: {e}")
        return None


@functions_framework.cloud_event
def main(cloud_event: CloudEvent):
    """
    Cloud Function triggered by a message on a Cloud Pub/Sub topic to manage data retention via nullify the raw data while keeping the analysis and updates for resultinh MongoDB queries.
    This function processes the payload from a Cloud Pub/Sub message, retrieves relevant MongoDB collections and documents, checks the data retention threshold, and performs necessary updates to MongoDB documents and BigQuery tables.

    Steps:
    - Decode the Cloud Pub/Sub message to extract the payload.
    - Initialize and set up the metadata and necessary MongoDB connections for the organization.
    - Retrieve the MongoDB query collection and iterate over the queries.
    - Check if any queries have exceeded the retention threshold.
    - For each query:
        - Fetch the query configuration and metadata.
        - Retrieve the RAC transform collection and process the query data.
        - If retention threshold is exceeded, update document fields, modify meta-data, and update BigQuery tables.
        - Perform necessary clean-up by deleting documents and folders associated with the query.
        - If retention threshold is not exceeded, log the remaining time until the next update.

    Parameters:
    - cloud_event (CloudEvent): The CloudEvent object representing the incoming event from Cloud Pub/Sub.

    Returns:
    - None: The function processes data without returning any value.

    Exception Handling:
    - Exception: If any error occurs during the execution, the function logs the error and provides diagnostic details.
    """
    try:
        payload = json.loads(
            base64.b64decode(cloud_event.data["message"]["data"]).decode("utf-8")
        )
        meta_container = MetaContainer()
        logger.info("Message received successfully: %s", payload)
        meta_container.set_payload_info(payload)
        organization_id = payload["organization_id"]
        data_source_id = payload["data_source_id"]

        org_account_info = OrganizationAccountInfo(organization_id)
        mongodb_url = org_account_info.mongodb_url

        organization_db_name = org_account_info.organization_db_name
        mongo_db_client = get_mongodb_client(mongodb_url)
        organization_db = get_mongodb_db(mongo_db_client, organization_db_name)
        query_collection = get_mongodb_collection(
            organization_db,
            QUERY_COLLECTION_NAME,
        )

        query_archive_bucket_name = f"query_archive_bucket_{organization_id}"
        mongo_client = MongoClient(PLATFORM_MONGODB_URL)
        capabilities_db = mongo_client[CAPABILITIES_DATABASE]

        threshold_time = timedelta(days=RETENTION_DAYS, hours=0, minutes=0)
        if data_source_id == DataSourceId.YT_COMMENTS.value:
            threshold_time = timedelta(
                days=RETENTION_DAYS_FOR_YOUTUBE, hours=0, minutes=0
            )

        # ----iterate over each org and then over each query
        query_collection = get_mongodb_collection(
            organization_db, QUERY_COLLECTION_NAME
        )

        queries = query_collection.find(
            {
                "meta_data.IS_RETENTION_EXPIRED": {"$ne": True},
                "meta_data": {"$exists": True},
            }
        )
        if not query_collection.count_documents(
            {
                "meta_data.IS_RETENTION_EXPIRED": {"$ne": True},
                "meta_data": {"$exists": True},
            }
        ):
            logger.info(
                f"No queries have exceeded the retention period for organization {organization_id}. Skipping."
            )
            return

        for query in queries:
            query_id = query.get("_id")
            user_id = query.get("user_id")

            query_config = get_query_config(
                query_collection, query_id, QUERY_PROJECTION_ATTRIBUTES
            )
            query_result = get_query_config(query_collection, query_id, {"_id": 0})
            data_source_id = str(query_result["source"]["data_source_id"])
            if not query_config:
                logger.warning("No configuration found for query_id: '%s'", query_id)
                return
            query_meta_data = query_config["meta_data"]
            query_meta_data = (
                query_meta_data[0]
                if isinstance(query_meta_data, list)
                else query_meta_data
            )

            rac_collection_name = query_meta_data.get(RAC_COLLECTION_NAME_METADATA_KEY)
            rac_transform_collection_name = query_meta_data.get(
                RAC_TRANSFORM_COLLECTION_NAME_METADATA_KEY
            )
            if not rac_transform_collection_name:
                # Set the collection name in query metadata
                rac_transform_collection_name = (
                    f"{rac_collection_name}_{RAC_TRANSFORM_COLLECTION_SUFFIX}"
                )
            rac_transform_collection = get_mongodb_collection(
                organization_db, rac_transform_collection_name
            )
            if rac_transform_collection is None:
                logger.info(
                    f"Skipping query {query_id} as rac_transform_collection is None."
                )
                continue
            document_ids = [query_id]
            org_ids = [organization_id]
            document = query_collection.find_one({"_id": ObjectId(query_id)})

            if not document:
                logger.info("Document not found.")
                return

            created_at = document.get("created_at")
            if created_at is None:
                logger.info("The 'created_at' field is not available in the document.")
                return

            # Ensure created_at is timezone-aware
            created_at = (
                created_at if created_at.tzinfo else pytz.UTC.localize(created_at)
            )
            time_diff = datetime.now(pytz.UTC) - created_at

            if time_diff >= threshold_time:
                logger.info("Threshold exceeded. Proceeding to update fields.")
                result = update_unwanted_fields(
                    rac_transform_collection,
                    YOUTUBE_FIELDS,
                    COMMON_FIELDS,
                    data_source_id,
                )

                # Check if the update was successful
                if result and result.modified_count > 0:
                    logger.info(
                        f"Fields updated for {result.modified_count} document(s)."
                    )
                else:
                    logger.info(f"No fields to update for document _id {query_id}.")

                meta_result = update_meta_date_exceeded(query_collection, query_id)
                if meta_result and meta_result.modified_count > 0:
                    logger.info(
                        f"Meta field 'IS_RETENTION_EXPIRED' updated for document _id {query_id}."
                    )
                else:
                    logger.info(
                        f"Failed to update meta field for document _id {query_id}."
                    )

                update_bigquery_table(
                    organization_db_name, rac_transform_collection_name
                )

                delete_documents_in_collection(
                    organization_db,
                    ["encapsulation_marker"],
                    document_ids,
                )

                delete_documents_in_diagnostic_collection(
                    capabilities_db, "diagnostic", document_ids, org_ids
                )

                delete_query_folder_from_storage(
                    query_archive_bucket_name,
                    organization_id,
                    user_id,
                    data_source_id,
                    query_id,
                )
                delete_indexes(rac_transform_collection, INDEXES_TO_BE_DELETED)

                logger.info(f"Data retention for query {query_id} completed.")

            else:
                logger.info(
                    f"Remaining time to update fields: {format_time(threshold_time - time_diff)}"
                )

    except Exception as e:
        traceback.print_exc()
        logger.info(f"An error occurred: {e}")
