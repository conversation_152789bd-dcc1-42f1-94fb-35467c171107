"""
Cloud Function triggered by a Pub/Sub message to send notifications via email.

This function processes messages from a Cloud Pub/Sub topic, extracts relevant 
details such as query ID, organization ID, and consumer type, and sends email 
notifications based on pre-configured templates. The function performs the following steps:

1. Retrieves the notification configuration and template from MongoDB.
2. Sends emails to configured recipients, with personalized content based on message 
    type and consumer type.
3. Logs email details in a MongoDB collection.
4. Updates the notification status to indicate success or failure based on email 
    sending results.
"""

from typing import Dict, List
from datetime import datetime, timezone
import json
import requests
import base64
from bson import ObjectId
import functions_framework
from pymongo.collection import Collection
from cloudevents.http import CloudEvent
from sendgrid import SendGridAPIClient
from sendgrid.helpers.mail import Mail, To
from notification.const import (
    SENDGRID_API_KEY,
    SENDGRID_SENDER_EMAIL,
    CAPABILTIES_APP_URL,
    CAPABILTIES_DB_NAME,
    CAPABILTIES_MONGO_URL,
    CONTACT_URL,
)
from utils.mongo_db import get_mongodb_db, get_mongodb_collection, get_mongodb_client
from utils.logger import logger
from utils.utilities import MessageType, MongoDBCollection


def get_notification_config(
    collection: Collection,
    projection_attributes: Dict,
    message_type: str,
    consumer_type: str,
) -> List | None:
    """
    Retrieve notification configuration document from the MongoDB collection.

    Parameters:
    - collection (pymongo.collection.Collection): The MongoDB query collection.
    - projection_attributes (dict): The projection attributes to include or exclude from the result.
    - message_type (str): message type of the notification i.e success of failure
    - consumer_type (str): consumer type for which we have to notify

    Returns:
    - list[dict] or None: The notification configuration document if found, None otherwise.
    """
    try:
        # Retrieve document from the collection based on the message_type, and consumer_type
        records = collection.aggregate(
            [
                {
                    "$match": {
                        "message_type": message_type,
                        "consumer_type": consumer_type,
                    }
                },
                {
                    "$lookup": {
                        "from": MongoDBCollection.NOTIFICATION_TEMPLATE.value,
                        "localField": "notification_template_id",
                        "foreignField": "_id",
                        "as": "notification_template",
                    }
                },
                {"$unwind": {"path": "$notification_template"}},
                {"$project": projection_attributes},
            ]
        )
        return list(records)

    except Exception as e:
        message = f"An error occurred {e} "
        logger.error(message)
        return None


def get_field_value_by_id(
    collection: Collection, record_id: str, field: str, projection: dict
) -> str | None:
    """
    Fetches the value of a specified field from a MongoDB document using record id.

    Parameters:
    - collection (pymongo.collection.Collection): The MongoDB collection object.
    - record_id (str): The ID of the document to fetch.
    - field (str): The specific field whose value should be returned.
    - projection (dict): Dictionary specifying the fields to include or exclude in the result.

    Returns:
    - The value of the specified field.

    Exception Handling:
    - Exception: Raised if an error occurs while getting field value.
    """
    try:
        document = collection.find_one({"_id": ObjectId(record_id)}, projection)
        return document[field]

    except Exception as e:
        message = f"Failed to fetch '{field}' for '{record_id}' record ID: {e}"
        raise Exception(message) from e


def send_email(to_email: str, subject: str, body: str) -> bool:
    """
    Send an email using the SendGrid API.

    Parameters:
        to_email (str): The email address of the recipient.
        subject (str): The subject of the email.
        body (str): The body content of the email.

    Raises:
        Exception: If an error occurs while sending email.

    Returns:
        bool: True if the email is sent successfully, False otherwise.
    """
    try:
        if subject is not None and body is not None:
            message = Mail(
                from_email=SENDGRID_SENDER_EMAIL,
                to_emails=To(to_email),
                subject=subject,
                html_content=body,
            )

            sg = SendGridAPIClient(SENDGRID_API_KEY)
            response = sg.send(message)

            logger.info(
                "Email sent to %s. Status code: %d", to_email, response.status_code
            )

        else:
            logger.warning("Email template data is missing. Skipping email sending.")
            return False

        return True
    except Exception as e:
        logger.error("Error sending email: %s", e)
        return False


def store_email_logs(
    notification_email_log_collection: Collection,
    notification_id: int,
    notification_config_id: int,
    email: str,
    subject: str,
    body: str,
) -> None:
    """
    Store email logs in the notification_email_log table.

    Parameters:
        notification_email_log_collection (Collection): notification email log collection object
        notification_id (int): The ID of the last notification stored in the notification table.
        notification_config_id (int): The ID stored in the notification_config table
            for each record.
        email (str): The email address of the recipient.
        subject (str): The subject of the email.
        body (str): The body content of the email.

    Raises:
        Exception: If an error occurs while storing the email log.
    """
    try:
        record_data = {
            "notification_id": notification_id,
            "notification_config_id": notification_config_id,
            "receiver": email,
            "subject": subject,
            "body": body,
            "created_at": datetime.now(timezone.utc),
        }
        notification_email_log_id = notification_email_log_collection.insert_one(
            record_data
        ).inserted_id

        logger.info(
            "Successfully inserted data into notification_email_log table with ID: %s",
            notification_email_log_id,
        )

    except Exception as e:
        logger.error("Error storing email log: %s", e)


def post_notification_data(org_id, user_id, notification_ids, secret_key, api_url):
    """
    Sends a POST request with org_id, user_id, notification_ids, and secret_key.

    Args:
        org_id (str): Organization ID.
        user_id (str): User ID.
        notification_ids (list): List of notification IDs.
        secret_key (str): Secret key for authorization or validation.
        api_url (str): The URL of the API to send the request to.

    Returns:
        Response object from the API call.
    """
    # Prepare the payload
    payload = {
        'organization_id': str(org_id),
        'user_id': str(user_id),
        'notification_ids': [str(notification_ids)],
        'secret_key': secret_key
    }

    # Send the POST request
    response = requests.post(api_url, json=payload)

    return response


# Triggered from a message on a Cloud Pub/Sub topic.
@functions_framework.cloud_event
def main(cloud_event: CloudEvent):
    """
    Process and handle messages from Pub/Sub.

    This function is designed to handle incoming messages from a Pub/Sub topic.
    It extracts relevant information from the message payload, inserts the data
    into a MySQL notification table, retrieves configuration details from
    notification_config and notification_template tables, sends emails based on
    the configuration, store email logs and updates the notification status accordingly.

    Parameters:
        message (Pub/Sub Message): The message received from Pub/Sub.

    Raises:
        Exception: If an error occurs during the message processing.

    Notes:
        - The function assumes a certain structure in the message payload,
            including keys like 'ProducerType', 'MessageType', 'Message', and 'AdditionalData'.
        - It uses functions like 'send_email','store_email_logs', and
            'update_notification_status' to perform specific tasks.
        - Email sending status is tracked, and the notification status is updated to
            1 only if all emails are sent successfully.
    """
    mongodb_client = None
    try:
        payload = json.loads(
            base64.b64decode(cloud_event.data["message"]["data"]).decode("utf-8")
        )
        logger.info("Message received successfully: %s", payload)

        query_id = payload["query_id"]
        organization_id = payload["organization_id"]
        data_source_id = payload["data_source_id"]
        user_id = payload["user_id"]
        consumer_type = payload["consumer_type"]
        message_type = payload["message_type"]
        message_content = payload["message"]
        additional_data = payload["additional_data"]

        record_data = {
            "query_id": ObjectId(query_id),
            "organization_id": ObjectId(organization_id),
            "data_source_id": ObjectId(data_source_id),
            "user_id": ObjectId(user_id),
            "consumer_type": consumer_type,
            "message_type": message_type,
            "message": message_content,
            "additional_data": additional_data,
            "status": 0,
            "created_at": datetime.now(timezone.utc),
        }

        mongodb_client = get_mongodb_client(CAPABILTIES_MONGO_URL)
        capabilties_db = get_mongodb_db(
            client=mongodb_client, db_name=CAPABILTIES_DB_NAME
        )

        master_data_source_collection = get_mongodb_collection(
            capabilties_db, MongoDBCollection.MASTER_DATA_SOURCE.value
        )

        notification_collection = get_mongodb_collection(
            capabilties_db, MongoDBCollection.NOTIFICATION.value
        )

        notification_config_collection = get_mongodb_collection(
            capabilties_db, MongoDBCollection.NOTIFICATION_CONFIG.value
        )

        notification_email_log_collection = get_mongodb_collection(
            capabilties_db, MongoDBCollection.NOTIFICATION_EMAIL_LOG.value
        )

        user_info_collection = get_mongodb_collection(
            capabilties_db, MongoDBCollection.USER_INFO_COLLECTION.value
        )

        last_notification_id = notification_collection.insert_one(
            record_data
        ).inserted_id

        post_notification_data(organization_id,user_id,last_notification_id,SECRET_KEY,NOTIFICATION_API_URL)

        config_results = get_notification_config(
            notification_config_collection,
            {
                "_id": 1,
                "email": 1,
                "notification_template.subject": 1,
                "notification_template.body": 1,
            },
            message_type,
            consumer_type,
        )
        # Send emails and store logs
        if config_results:
            # Flag to track email sending status
            all_emails_sent = True

            for config_result in config_results:
                notification_config_id = config_result["_id"]
                email = config_result["email"]
                subject = config_result["notification_template"]["subject"]
                body = config_result["notification_template"]["body"]

                # Retrieve the data source name and consumer type to dynamically populate the email subject
                if data_source_id is not None:
                    data_source_name = get_field_value_by_id(
                        master_data_source_collection,
                        data_source_id,
                        "name",
                        {"_id": 0, "name": 1},
                    )
                    consumer_type = " ".join(
                        word.title() for word in consumer_type.split("_")
                    )
                    subject = subject.format(
                        data_source_name=data_source_name.upper(),
                        consumer_type=consumer_type,
                    )

                # Retrieve the user's email based on their user ID to send an email for INFO message type
                if (
                    message_type == MessageType.INFO.value
                    and message_content is not None
                    and config_result.get("email") is None
                ):
                    email = get_field_value_by_id(
                        user_info_collection, user_id, "email", {"_id": 0, "email": 1}
                    )
                    config_result.update({"email": email})
                    body = body.format(
                        message_content=message_content,
                        app_url=CAPABILTIES_APP_URL,
                        contact_url=CONTACT_URL,
                        year=datetime.now().year,
                    )

                elif (
                    query_id is not None
                    and organization_id is not None
                    and data_source_id is not None
                    and consumer_type is not None
                    and message_content is not None
                ):
                    body = body.format(
                        query_id=query_id,
                        organization_id=organization_id,
                        data_source_id=data_source_id,
                        consumer_type=consumer_type,
                        message_content=message_content,
                    )

                if email:
                    if not send_email(email, subject, body):
                        # Set the flag to False if sending email fails for any recipient
                        all_emails_sent = False

                    store_email_logs(
                        notification_email_log_collection,
                        last_notification_id,
                        notification_config_id,
                        email,
                        subject,
                        body,
                    )

                else:
                    logger.warning("Empty email field found in config.")

            # Update Status column to 1 if all emails are sent successfully
            if all_emails_sent:
                notification_collection.update_one(
                    {"_id": last_notification_id}, {"$set": {"status": 1}}
                )
            else:
                logger.warning(
                    "Not updating Status to 1 for notification ID: %s",
                    last_notification_id,
                )

        else:
            logger.warning(
                "No config found for message_type: %s, and consumer_type: %s",
                message_type,
                consumer_type,
            )

    except Exception as e:
        logger.error("Error while notifying: %s", e)

    finally:
        if mongodb_client is not None:
            mongodb_client.close()
