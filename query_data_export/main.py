from datetime import datetime, <PERSON><PERSON><PERSON>
import json
import base64
import time
from bson import ObjectId
from cloudevents.http import CloudEvent
import functions_framework
import traceback
from data_upload import (
    combine_chunks_and_upload,
    upload_chunks_data_to_gcp,
)
from utils.mongo_db import (
    get_mongodb_client,
    get_mongodb_db,
    get_mongodb_collection,
)
from utils.common_utils import (
    get_query_config,
    update_query_metadata,
)
from utils.logger import logger
from utils.utilities import (
    DiagnosticActionType,
    DiagnosticStatus,
    OrganizationAccountInfo,
    MetaContainer,
)
from const import (
    DATA_EXPORT_MESSAGE_TEMPLATE,
    QUERY_COLLECTION_NAME,
    CONSUMER_TYPE,
    QUERY_PROJECTION_ATTRIBUTES,
    LINK_EXPIRATION_TIME,
    RAC_TRANSFORM_COLLECTION_NAME_METADATA_KEY,
    QUERY_DATA_EXPORT_OFFSET_METADATA_KEY,
    RAC_TRANSFORM_VOLUME_METADATA_KEY,
)


meta_container = MetaContainer()


@functions_framework.cloud_event
def main(cloud_event: CloudEvent):
    try:
        payload = json.loads(
            base64.b64decode(cloud_event.data["message"]["data"]).decode("utf-8")
        )
        logger.info("Message received successfully: %s", payload)

        payload["publisher_type"] = CONSUMER_TYPE
        meta_container.set_payload_info(payload)
        organization_id = payload["organization_id"]
        user_id = payload["user_id"]
        data_source_id = payload["data_source_id"]
        query_id = payload["query_id"]
        graph_type = payload["graph_type"]
        chart_type = payload["chart_type"]
        sort_by = payload["sort_by"]
        sort_order = payload["sort_order"]
        time_suffix = payload["time_suffix"]
        search_query = payload["search_query"]

        # Convert the date filters to datetime objects
        if search_query:
            match_stage = payload["pipeline"][1]["$match"]
        else:
            match_stage = payload["pipeline"][0]["$match"]

        match_stage["date"]["$gte"] = datetime.fromisoformat(
            match_stage["date"]["$gte"]
        )
        match_stage["date"]["$lte"] = datetime.fromisoformat(
            match_stage["date"]["$lte"]
        )
        
        # Convert _id to ObjectId
        if "_id" in match_stage:
            match_stage["_id"] = ObjectId(match_stage["_id"])

        # Convert question_ids to ObjectId
        qid_in = match_stage.get("question_ids", {}).get("$in")
        if qid_in:
            match_stage["question_ids"]["$in"] = list(map(ObjectId, qid_in))

        # Fetch organization account information
        org_account_info = OrganizationAccountInfo(organization_id)
        data_export_bucket_name = org_account_info.data_export_bucket_name

        mongodb_url = org_account_info.mongodb_url
        organization_db_name = org_account_info.organization_db_name

        mongo_db_client = get_mongodb_client(mongodb_url)
        organization_db = get_mongodb_db(mongo_db_client, organization_db_name)

        query_collection = get_mongodb_collection(
            organization_db, QUERY_COLLECTION_NAME
        )
        query_config = get_query_config(
            query_collection, query_id, QUERY_PROJECTION_ATTRIBUTES
        )
        if not query_config:
            logger.warning("No configuration found for query_id: '%s'", query_id)
            return

        query_name = query_config["name"].replace("/", "-")

        query_meta_data = query_config["meta_data"]
        query_meta_data = (
            query_meta_data[0] if isinstance(query_meta_data, list) else query_meta_data
        )
        total_documents = query_meta_data.get(RAC_TRANSFORM_VOLUME_METADATA_KEY)

        rac_transform_collection_name = query_meta_data[
            RAC_TRANSFORM_COLLECTION_NAME_METADATA_KEY
        ]

        if rac_transform_collection_name is None:
            logger.error("RAC Transform collection is missing.")
            return

        rac_transform_collection = get_mongodb_collection(
            organization_db, rac_transform_collection_name
        )

        bucket_path = f"{organization_id}/{user_id}/{data_source_id}/{query_id}/{rac_transform_collection_name}/{rac_transform_collection_name}_{chart_type}"

        offset = query_meta_data.get(QUERY_DATA_EXPORT_OFFSET_METADATA_KEY, 0)

        offset_metadata_key = QUERY_DATA_EXPORT_OFFSET_METADATA_KEY

        upload_chunks_data_to_gcp(
            payload,
            rac_transform_collection,
            chart_type,
            graph_type,
            sort_by,
            sort_order,
            bucket_path,
            data_export_bucket_name,
            time_suffix,
            offset,
            offset_metadata_key,
            total_documents,
            query_collection,
            query_id,
            search_query,
        )

        # Combine all the csv files on the bucket having the same rac_transform_collection_name and time_suffix
        files_path = f"{organization_id}/{user_id}/{data_source_id}/{query_id}/{rac_transform_collection_name}"

        destination_path = f"{organization_id}/{user_id}/{data_source_id}/{query_id}/{rac_transform_collection_name}/{query_name}_{chart_type}_{time_suffix}.csv"

        destination_blob = combine_chunks_and_upload(
            data_export_bucket_name,
            files_path,
            rac_transform_collection_name,
            time_suffix,
            destination_path,
        )

        signed_url = destination_blob.generate_signed_url(
            expiration=timedelta(hours=int(LINK_EXPIRATION_TIME)),
            method="GET",
            version="v4",
        )

        message = DATA_EXPORT_MESSAGE_TEMPLATE.format(
            query_name=query_name,
            query_id=query_id,
            chart_type=chart_type,
            signed_url=signed_url,
            link_exp_hours=int(LINK_EXPIRATION_TIME),
        )
        offset = 0
        update_query_metadata(
            query_collection,
            query_id,
            meta_data_key=QUERY_DATA_EXPORT_OFFSET_METADATA_KEY,
            value=offset,
        )

        if signed_url:
            # convert the datetime objects back to string
            match_stage["date"]["$gte"] = match_stage["date"]["$gte"].isoformat()
            match_stage["date"]["$lte"] = match_stage["date"]["$lte"].isoformat()
            
            # Convert _id and question_ids back to string
            if "_id" in match_stage:
                match_stage["_id"] = str(match_stage["_id"])

            qid_in = match_stage.get("question_ids", {}).get("$in")
            if qid_in:
                match_stage["question_ids"]["$in"] = list(map(str, qid_in))

            meta_container.send_diagnostic(
                DiagnosticActionType.INFO.value,
                DiagnosticStatus.INFO.value,
                message,
            )

    except Exception as e:
        traceback.print_exc()
        logger.error("Failed to process payload: %s", str(e))
