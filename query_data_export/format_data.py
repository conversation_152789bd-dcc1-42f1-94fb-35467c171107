import json
import pandas as pd
from bson import ObjectId
import datetime
from utils.logger import logger
from const import DEFAULT_SORT_ORDER


def format_conversation_by_time(conversation_by_time_df):
    """
    This method formats the conversation by time results to get the aggregated and formatted data.

    Parameters:
    - conversation_by_time_df (DataFrame): The initial df that is used to group the data

    Returns:
    - DataFrame: The final aggregated df with all the data

    Exception Handling:
    - None
    """
    if conversation_by_time_df.empty:
        return []
    # Extract the date from the _id column and create a new date column
    conversation_by_time_df["date"] = conversation_by_time_df["_id"].apply(
        lambda x: x["date"]
    )
    # Drop the `_id` column
    conversation_by_time_df.drop("_id", axis=1, inplace=True)
    return conversation_by_time_df


def format_sentiment_by_time_result(sentiments_by_time_df):
    """
    This method processes the sentiment by time results to get the aggregated and formatted data.

    Parameters:
    - sentiments_by_time_df (DataFrame): The initial df that is used for aggregation
    
    Returns:
    - DataFrame: The final df that contains the aggregated and formatted sentiment by time data

    Exception Handling:
    - None
    """
    if not sentiments_by_time_df.empty:
        # Extract the date and sentiment columns from the _id 
        sentiments_by_time_df["date"] = sentiments_by_time_df["_id"].apply(
            lambda x: x["date"]
        )
        sentiments_by_time_df["sentiment"] = sentiments_by_time_df["_id"].apply(
            lambda x: x["sentiment"]
        )
        sentiments_by_time_df = sentiments_by_time_df.drop(columns=["_id"])
        # Group the data by date and sentiment columns
        sentiments_by_time_df = (
            sentiments_by_time_df.groupby(["date", "sentiment"])["count"]
            .sum()
            .reset_index()
        )
        # Flatten the data by date and sentiment columns
        sentiments_by_time_df = (
            sentiments_by_time_df.pivot(
                index="date", columns="sentiment", values="count"
            )
            .reset_index()
            .fillna(0)
        )
    return sentiments_by_time_df


def format_video_metrics(video_metrics_df, graph_type, sort_by, sort_order):
    """
    This method processes the video metrics results for getting the aggregated and sorted data

    Parameters:
    - video_metrics_df (DataFrame): The initial df that contains the video metrics data
    - graph_type (str): The graph type, sentiments by video or sentiments by creator
    - sort_by (str): The sorting field for sorting the data
    - sort_order (str): The sorting order for sorting the data

    Returns:
    - DataFrame: The final df that contains the aggregated, formatted and sorted data

    Exception Handling:
    - None
    """
    if not video_metrics_df.empty:
        if graph_type == "video":
            video_metrics_df["video"] = video_metrics_df["_id"].apply(lambda x: x["video"])
            video_metrics_df["id"] = video_metrics_df["_id"].apply(lambda x: x["id"])  # Extract video id
        elif graph_type == "creator":
            video_metrics_df["creator"] = video_metrics_df["_id"].apply(lambda x: x["creator"])
        video_metrics_df["sentiment"] = video_metrics_df["_id"].apply(lambda x: x["sentiment"])

        # Drop the '_id' column after normalization
        video_metrics_df = video_metrics_df.drop(columns=["_id"])

        # Group by the graph_type and flatten the data
        match graph_type:
            case "video":
                final_df = (
                    video_metrics_df.groupby(["id", "video", "sentiment"])
                    .sum(numeric_only=True)
                    .reset_index()
                    .pivot(index=["id", "video"], columns="sentiment", values="count")
                    .fillna(0)
                    .reset_index()
                )
            case "creator":
                final_df = (
                    video_metrics_df.groupby(["creator", "sentiment"])
                    .sum(numeric_only=True)
                    .reset_index()
                    .pivot(index="creator", columns="sentiment", values="count")
                    .fillna(0)
                    .reset_index()
                )

        # Add a total_count column
        final_df["total_count"] = final_df.sum(axis=1, numeric_only=True)

        # Sort the data according to the parameters
        if sort_by == "likes" or sort_by == "comment" or sort_by == "reply":
            final_df = final_df.sort_values(
                by="total_count", ascending=(sort_order == "asc")
            )
        elif sort_by == "video_title" or sort_by == "artist_name":
            final_df = final_df.sort_values(
                by="video" if graph_type == "video" else "creator",
                ascending=(sort_order == "asc"),
            )

    return final_df


def format_sentiment_counts(sentiment_counts_df, sort_by, sort_order):
    """
    This method formats and sorts the data on the basis of sorting fields.

    Parameters:
    - sentiment_counts_df (DataFrame): The dataframe object containing the initial counts of the sentiments.
    - sort_by (str): The sorting field for sorting the data
    - sort_order (str): The sort order for sorting the data, 'asc or desc'

    Returns:
    - DataFrame: The final df containing all the sorted and formatted data

    Exception Handling: 
    - None
    """
    if not sentiment_counts_df.empty:
        sentiment_counts_df = sentiment_counts_df.rename(columns={"_id": "sentiment"})
        if sort_by and sort_order:
            sentiment_counts_df = sentiment_counts_df.sort_values(
                by=sort_by, ascending=True if sort_order == "asc" else False
            )
        else:
            sentiment_counts_df = sentiment_counts_df.sort_values(
                by="sentiment",
                key=lambda x: x.map(
                    {val: idx for idx, val in enumerate(DEFAULT_SORT_ORDER)}
                ),
            )
    return sentiment_counts_df
