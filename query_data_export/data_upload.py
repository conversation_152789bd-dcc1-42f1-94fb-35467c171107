import io
import sys
import pandas as pd
from datetime import datetime
from const import (
    BASE_LOOPBACK_THRESHOLD,
    CHUNK_SIZE,
    QUERY_DATA_EXPORT_TOPIC_ID,
    SERVICE_AC,
)
from format_data import (
    format_conversation_by_time,
    format_sentiment_by_time_result,
    format_sentiment_counts,
    format_video_metrics,
)
from utils import logger
from utils.common_utils import (
    fetch_collection_aggregated_data,
    update_offset_and_publish,
)
from utils.file_util import FileUtils
from utils.utilities import FileExtension
from google.oauth2 import service_account
from google.cloud import storage


def upload_chunks_data_to_gcp(
    payload,
    rac_transform_collection,
    chart_type,
    graph_type,
    sort_by,
    sort_order,
    bucket_path,
    bucket_name,
    time_suffix,
    offset,
    offset_metadata_key,
    total_documents,
    query_collection,
    query_id,
):
    """
    This method fetches the data in chunks from the mongodb database using the pipeline, and stores it in the GCP bucket for that organisation.

    Parameters: 
    - payload (dict): The payload containing all the required data for the uploading 
    rac_transform_collection (Collection): The Transform collection of the query 
    chart_type (str): The chart type of the chart
    graph_type (str): The graph type for the charts containing more than one graph types
    sort_by (str): The sorting field to sort the data
    sort_order (str): The sorting order of the data
    bucket_path (str): The path in which the files need to be uploaded
    bucket_name (str): The name of the bucket of that organisation
    time_suffix (str): The time suffix of the file when the user clicked the export button
    offset (int): The offset for getting the data in chunks
    offset_metadata_key (str): The metadata key used for the loopback for chunks
    total_documents (int): The total number of documents in the transform collection
    query_collection (Collection): The query collection containing all the queries' metadata
    query_id (str): The id of the query being referred to

    Returns:
    - None

    Exception Handling:
    - None
    """
    logger.info(f"Starting loop with offset: {offset}")
    loopback_threshold = offset + int(BASE_LOOPBACK_THRESHOLD)

    while offset < loopback_threshold:

        logger.info(f"Before fetch, offset is: {offset}")
        pipeline = payload["pipeline"]

        batch_df = fetch_collection_aggregated_data(
            rac_transform_collection, pipeline[:], offset, CHUNK_SIZE
        )

        batch_count = int(len(batch_df))

        if batch_count == 0:
            logger.warning("No more records to process. Exiting loop.")
            break

        match chart_type:
            case "pie_chart":
                batch_df = format_sentiment_counts(batch_df, sort_by, sort_order)

            case "conversation_over_time":
                batch_df = format_conversation_by_time(batch_df)

            case "sentiment_over_time":
                batch_df = format_sentiment_by_time_result(batch_df)

            case "video_metrics":
                batch_df = format_video_metrics(
                    batch_df,
                    graph_type,
                    sort_by,
                    sort_order,
                )

        chunk_size = sys.getsizeof(batch_df) / (1024 * 1024)
        logger.info(f"Calculated chunk size: {chunk_size} MB")

        chunk_bucket_path = bucket_path + f"_{offset}_{time_suffix}.csv"
        full_bucket_path = f"gs://{bucket_name}/{chunk_bucket_path}"

        logger.info(f"Generated Bucket Path: {full_bucket_path}")

        add_header = True

        if offset > 0:
            add_header = False

        FileUtils.insert_data_to_gcsfs(
            full_bucket_path,
            batch_df,
            FileExtension.CSV.value,
            add_header=add_header,
        )

        # Convert the datetime object to string before sending loopback message
        match_stage = payload["pipeline"][0]["$match"]
        match_stage["date"]["$gte"] = match_stage["date"]["$gte"].isoformat()
        match_stage["date"]["$lte"] = match_stage["date"]["$lte"].isoformat()

        offset = update_offset_and_publish(
            offset,
            offset_metadata_key,
            total_documents,
            loopback_threshold,
            payload,
            batch_count,
            query_collection,
            query_id,
            QUERY_DATA_EXPORT_TOPIC_ID,
        )
        logger.info(f"After fetch, offset remains: {offset}")

        # Convert back to datetime objects if the loopback threshold is not surpassed
        match_stage["date"]["$gte"] = datetime.fromisoformat(
            match_stage["date"]["$gte"]
        )
        match_stage["date"]["$lte"] = datetime.fromisoformat(
            match_stage["date"]["$lte"]
        )

        del batch_df


def combine_chunks_and_upload(
    data_export_bucket_name,
    files_path,
    rac_transform_collection_name,
    time_suffix,
    destination_path,
):
    """
    This method combines the chunks formed by uploading the data into a single csv file.

    Parameters:
    - data_export_bucket_name (str): The name of the bucket to upload the file
    - files_path (str): The path of the intermediate files that need to be combined
    - rac_transform_collection_name (str): The Transform collection name for the query
    - time_suffix (str): The isoformatted string timestamp for the file name
    - destination_path (str): The path name of the destination file containing all the data

    Returns:
    - google.cloud.storage.blob.Blob: The blob object of the final csv file created

    Exception Handling:
    - None
    """
    credentials = service_account.Credentials.from_service_account_file(SERVICE_AC)

    storage_client = storage.Client(credentials=credentials)
    bucket = storage_client.bucket(data_export_bucket_name)

    # # List all files matching the operation_name
    blobs = bucket.list_blobs(prefix=files_path)

    csv_blobs = []
    for blob in blobs:
        if (
            blob.name.endswith(".csv")
            and f"{rac_transform_collection_name}_" in blob.name
            and f"{time_suffix}" in blob.name
        ):
            csv_blobs.append(blob.name)

    # Upload combined file to GCP
    destination_blob = bucket.blob(destination_path)

    destination_blob = combine_chunk_files(
        bucket,
        csv_blobs,
        files_path,
        destination_path,
        rac_transform_collection_name,
        time_suffix,
    )

    destination_blob.reload()  # Refresh blob metadata
    final_size = destination_blob.size  # Size in bytes
    logger.info(
        f"Combined file saved to {destination_path} with size: {final_size / (1024 * 1024):.2f} MB"
    )

    return destination_blob


def combine_chunk_files(
    bucket,
    source_files,
    files_path,
    destination_path,
    rac_transform_collection_name,
    time_suffix,

):
    """
    Combine the chunk files using gcp compose method without storing them in memory

    Parameters:
    - source_files (list): List of the source file names containing chunks data
    - files_path (str): The bucket path of the source files
    - destination_path (str): The bucket path of the file where the data will be stored in GCP
    - rac_transform_collection_name (str): The name of teh Tranform collection for the file name
    - time_suffix (str): The isoformatted string timestamp to add to the end fo the file name

    Returns:
    - google.cloud.storage.blob.Blob: The blob that refers to the final csv file on the gcp bucket

    Exception Handling:
    - None
    """
    destination_blob = bucket.blob(destination_path)
    if len(source_files) < 2:
        source_blob = bucket.blob(source_files[0])
        destination_blob = process_blob_to_csv_and_upload(bucket, source_blob, destination_path)
    else:
        while len(source_files) > 32:
            # Combine the first 32 files into an intermediate file
            intermediate_blob_name = f"{files_path}/intermediate_{len(source_files)}_{rac_transform_collection_name}_{time_suffix}.csv"
            intermediate_blob = bucket.blob(intermediate_blob_name)
            intermediate_blob.compose(
                [bucket.blob(file_name) for file_name in source_files[:32]]
            )

            # Delete the source blobs after combining
            for file_name in source_files[:32]:
                bucket.blob(file_name).delete()
                logger.info(f"Deleted intermediate file: {file_name}")
            # Replace the first 32 files with the intermediate file
            source_files = [intermediate_blob_name] + source_files[32:]

        # Combine the remaining files into the final destination blob
        if len(source_files) > 1:
            destination_blob.compose(
                [bucket.blob(file_name) for file_name in source_files]
            )

    # Delete the final intermediate file
    if len(source_files) == 1 and source_files[0] != destination_path:
        bucket.blob(source_files[0]).delete()
        logger.info(f"Deleted final intermediate file: {source_files[0]}")

    logger.info(f"Combining all files into {destination_path}.")

    return destination_blob


def process_blob_to_csv_and_upload(bucket, source_blob, destination_path):
    """
    Converts the blob to a DataFrame, then uploads the DataFrame as a CSV file to the destination path.

    Parametesr:
        bucket_name (str): Name of the source GCS bucket.
        source_file_name (str): Name of the blob in the source bucket.
        destination_bucket_name (str): Name of the destination GCS bucket.
        destination_file_name (str): Name of the file to save in the destination bucket.

    Returns:
    - None

    Exception Handling:
    - None
    """
    # Download blob data as text and load it into a DataFrame
    blob_data = source_blob.download_as_text()  # Download blob as text
    df = pd.read_csv(io.StringIO(blob_data))  # Load into a pandas DataFrame

    # Convert the DataFrame to CSV in-memory
    csv_buffer = io.StringIO()
    df.to_csv(csv_buffer, index=False)  # Write DataFrame to in-memory CSV
    csv_buffer.seek(0)  # Reset the buffer position

    # Upload the CSV directly to the destination bucket
    destination_blob = bucket.blob(destination_path)
    destination_blob.upload_from_string(csv_buffer.getvalue(), content_type="text/csv")

    return destination_blob