import os

PROJECTION = {"source.data_source_name": 1, "meta_data": 1}
QUERY_COLLECTION = os.environ.get("QUERY_COLLECTION")

PROJECT_ID = os.getenv("PROJECT_ID","tellagence-platform")
SUBSCRIPTION_ID = os.getenv("SUBSCRIPTION_ID","similarity-vm-sub")
SEQUENCE_COORDINATOR_TOPIC_ID = os.environ.get("SEQUENCE_COORDINATOR_TOPIC_ID")

ENCAPSULATION_MARKER_COLLECTION_NAME_METADATA_KEY = (
    "ENCAPSULATION_MARKER_COLLECTION_NAME"
)

# Define a threshold for similarity (0.7 cosine similarity)
SIMILARITY_THRESHOLD = os.getenv("SIMILARITY_THRESHOLD")

CONSUMER_TYPE_SIMILARITY = os.getenv("CONSUMER_TYPE_SIMILARITY")