# -*- coding: utf-8 -*-
"""
Created on Mon Mar 18 12:48:40 2024

@author: nitin
"""

import time
from bson import ObjectId
import numpy as np
import pandas as pd
from collections import Counter
from pymongo import UpdateOne
from sentence_transformers import SentenceTransformer
from hdbscan import (
    HDBSCAN,
)  # from cuml.cluster import HDBSCAN # RAPIDS requires a Linux host. cuml part of rapids
from umap import UMAP  # from cuml.manifold import UMAP
from nltk.tokenize import word_tokenize

# from cuml.preprocessing import normalize
from sklearn.feature_extraction.text import CountVectorizer
from bertopic.vectorizers import ClassTfidfTransformer
from bertopic import (
    BERTopic,
)  # https://maartengr.github.io/BERTopic/algorithm/algorithm.html

from consumer_scripts.Discover.const import (
    ENCAPSULATION_MARKER_COLLECTION_NAME_METADATA_KEY,
    CONSUMER_TYPE_DISCOVER,
    SEQUENCE_COORDINATOR_TOPIC_ID,
    PROJECT_ID,
)
from utils.mongo_db import get_mongodb_collection
from utils.logger import logger
from utils.utilities import (
    EncapsulationMarkerStatus,
    EncapsulationMarkerStatusColumn,
    MetaContainer,
    DiagnosticActionType,
    DiagnosticStatus,
)
from utils.capabilities_utils import CapabilitiesUtils
from utils.pubsub_publisher import (
    publish_pubsub_message,
)

#
## Speeding up UMAP
#
from sklearn.decomposition import PCA


def rescale(x, inplace=False):
    """Rescale an embedding so optimization will not have convergence issues."""
    if not inplace:
        x = np.array(x, copy=True)
    x /= np.std(x[:, 0]) * 10000
    return x


def discover(Data1, query_id):
    if len(Data1) <= 50:
        Data1["Themes"] = 0
        Data1["Stories"] = 0
        return Data1
    elif len(Data1) <= 100 and len(Data1) > 50:
        min_count = 0
    elif len(Data1) <= 1000 and len(Data1) > 100:
        min_count = 0
    elif len(Data1) <= 10000 and len(Data1) > 1000:
        min_count = 10
    elif len(Data1) <= 100000 and len(Data1) > 10000:
        min_count = 15
    elif len(Data1) <= 1000000 and len(Data1) > 100000:
        min_count = 20
    elif len(Data1) <= 5000000 and len(Data1) > 1000000:
        min_count = 25
    elif len(Data1) <= 10000000 and len(Data1) > 5000000:
        min_count = 30
    elif len(Data1) > 10000000:
        min_count = 35  # min value

    # {}
    ### Hashtag Frequency
    logger.info("At stage: Hashtag frequency for %s", query_id)

    #
    Token1 = Data1["Hashtag"].astype(str)
    Total_Hashtag_Count = Counter()
    for line in Token1:
        for x in line.split():
            if "#" in x:
                Total_Hashtag_Count[x] += 1
    # removing min count
    if len(Total_Hashtag_Count) > 0:
        for x in list(Total_Hashtag_Count.keys()):
            if Total_Hashtag_Count[x] <= min_count:
                del Total_Hashtag_Count[x]
    else:
        Hashtag = pd.DataFrame()
    ##next loop after removal of min count
    if len(Total_Hashtag_Count) > 0:
        Total_Hashtag_Count = sorted(
            Total_Hashtag_Count.items(), key=lambda pair: pair[1], reverse=True
        )  # sorting converts to list
        Total_Hashtag_Count = [x for x in Total_Hashtag_Count if x[0] != ("#")]
        Hashtag = pd.DataFrame(Total_Hashtag_Count)  # list of tuples to dataframe
        Hashtag = Hashtag.rename(columns={0: "Word", 1: "Frequency"})
        Hashtag["Type"] = "Hashtag"
    else:
        Hashtag = pd.DataFrame()
    #
    ### Keyword Frequency
    #
    logger.info("At stage: Keyword frequency for %s", query_id)

    Data1["Keyword"] = Data1["Keyword"].astype(str)
    Data1["Lemitized"] = Data1["Lemitized"].astype(str)

    Token2 = Data1["Keyword"].apply(
        word_tokenize
    )  # .to_list() for later to identify directional co-occurance
    Total_KW_Count = Counter()
    for text in Token2:
        for x in text:
            Total_KW_Count[x] += 1

    # removing min count
    for x in list(Total_KW_Count.keys()):
        if Total_KW_Count[x] < min_count:
            del Total_KW_Count[x]

    Total_KW_Count = sorted(
        Total_KW_Count.items(), key=lambda pair: pair[1], reverse=True
    )  # sorting converts to list

    Total_KW_Count = [x for x in Total_KW_Count if len(x[0]) > 2]

    KW = pd.DataFrame(Total_KW_Count)  # list of tuples to dataframe
    KW = KW.rename(columns={0: "Word", 1: "Frequency"})  # Freq is occurance frequency

    KW["Type"] = "KW"

    del [Token1, Token2, x, Total_KW_Count, Total_Hashtag_Count, text, line]
    #
    ### Subsampling (Mikolov et al., 2013a)
    #
    logger.info("At stage: Subsampling for %s", query_id)

    Word_Index = pd.concat([KW, Hashtag], ignore_index=True)  # ,Emoji
    Word_Index = Word_Index.sort_values(by=["Frequency"], ascending=False)
    Word_Index = Word_Index.reset_index(drop=True)
    Word_Index["Prob"] = Word_Index["Frequency"] / len(Data1)
    # 1 - np.sqrt(0.00001/Word_Index['Frequency'])
    Word_Index = Word_Index[Word_Index["Prob"] < 0.5]
    Word_Index = Word_Index.reset_index(drop=True)

    VoCab = Word_Index["Word"]

    del [Word_Index, KW, Hashtag, min_count]
    #
    ### BERT TOPIC MODEL - Hyperparameter Tuning https://medium.com/grabngoinfo/hyperparameter-tuning-for-bertopic-model-in-python-104445778347
    #
    logger.info("At stage: Embedding for %s", query_id)

    embedding_model = SentenceTransformer("all-MiniLM-L6-v2")  # Extract embeddings
    embeddings = embedding_model.encode(Data1["Lemitized"], show_progress_bar=False)
    # embeddings = normalize(embeddings) # while using cuml...normalize them first to force a cosine-related distance metric in UMAP:
    # Initialize and rescale PCA embeddings - Start UMAP from PCA embeddings to speed up UMAP and avoid convergence issues
    pca_embeddings = rescale(
        PCA(n_components=5).fit_transform(embeddings)
    )  # starting point to UMPA
    umap_model = UMAP(
        n_neighbors=15,
        n_components=5,
        min_dist=0.0,
        metric="cosine",
        init=pca_embeddings,
        random_state=100,
    )  # Reduce dimensionality
    hdbscan_model = HDBSCAN(
        min_cluster_size=100,
        min_samples=5,
        metric="euclidean",
        cluster_selection_method="eom",
        prediction_data=True,
    )  # Cluster reduced embeddings min_df=min_count
    vectorizer_model = CountVectorizer(
        stop_words="english",
        token_pattern=r"\b\w\w+\b|(?<!\w)@\w+|(?<!\w)#\w+",
        vocabulary=VoCab,
    )  # Tokenize topics
    ctfidf_model = ClassTfidfTransformer(
        reduce_frequent_words=True
    )  # Create topic representation
    #
    ### Putting all hyper-parameters together
    #
    topic_model = BERTopic(
        embedding_model=embedding_model,
        umap_model=umap_model,
        hdbscan_model=hdbscan_model,
        vectorizer_model=vectorizer_model,
        ctfidf_model=ctfidf_model,
        top_n_words=4,
        nr_topics="auto",
        low_memory=True,
        calculate_probabilities=False,
    )

    #
    ### Themes
    #
    logger.info("At stage: Themes for %s", query_id)
    logger.info(f"Data1 length is: {len(Data1)} records")
    logger.info(f"Embeddings shape: {embeddings.shape}")

    if len(Data1) <= 1500:
        try:
            topics, probs = topic_model.fit_transform(Data1["Lemitized"], embeddings)  #
            Themes = topic_model.get_topic_info()
        except IndexError as e:
            Data1["Themes"] = 0
            Data1["Stories"] = 0
            return Data1
        except TypeError as e:
            Data1["Themes"] = 0
            Data1["Stories"] = 0
            return Data1
    else:
        topics, probs = topic_model.fit_transform(Data1["Lemitized"], embeddings)  #
        Themes = topic_model.get_topic_info()

    #
    ### Outlier Reduction
    #
    logger.info("At stage: Outlier reduction for %s", query_id)

    if Themes.loc[0, "Topic"] == -1:
        topics_New = topic_model.reduce_outliers(
            Data1["Lemitized"],
            topics,
            probabilities=probs,
            threshold=0.05,
            strategy="c-tf-idf",
        )
        topic_model.update_topics(Data1["Lemitized"], topics=topics_New)
        Themes_New = topic_model.get_topic_info()
        # Theme_Tweets_New=topic_model.get_document_info(Data1['Lemitized'])
    else:
        topics_New = topics
        Themes_New = Themes
    del [topics, probs, Themes]
    #
    ### Topic Reduction
    #
    logger.info("At stage: Topic reduction for %s", query_id)

    if len(Themes_New) > 11:
        topic_model.reduce_topics(Data1["Lemitized"], nr_topics=15)
        Themes_New_R = topic_model.get_topic_info()
        topics_New_R = topic_model.topics_
        # Theme_Tweets_New=topic_model.get_document_info(Data1['Lemitized'])
    else:
        topics_New_R = topics_New
        Themes_New_R = Themes_New
        # Theme_Tweets_New=Theme_Tweets

    # Theme_Tweets_New_R=topic_model.get_document_info(Data1['Lemitized'])
    # Keywords10_Theme_weight = topic_model.topic_representations_
    Data1["Themes"] = topics_New_R

    del [
        embedding_model,
        embeddings,
        pca_embeddings,
        umap_model,
        hdbscan_model,
        vectorizer_model,
        ctfidf_model,
        topic_model,
        VoCab,
        topics_New_R,
        topics_New,
        Themes_New,
    ]

    #
    ### Stories topic_model,, Keywords10_Theme_weight
    #
    for x2 in range(len(Themes_New_R)):  # x2=0
        My_DF2 = Data1[Data1["Themes"] == x2].reset_index()

        if My_DF2.empty:
            continue

        #
        ### Hyperparameter Tuning
        #
        logger.info("At stage: Hyperparameter Tuning for %s", query_id)

        My_DF2["Lemitized"] = My_DF2["Lemitized"].fillna("").astype(str)
        lemitized = My_DF2["Lemitized"].tolist()

        embedding_model1 = SentenceTransformer("all-MiniLM-L6-v2")  # Extract embeddings
        embeddings1 = embedding_model1.encode(lemitized, show_progress_bar=False)

        # Initialize and rescale PCA embeddings - Start UMAP from PCA embeddings to speed up UMAP and avoid convergence issues
        pca_embeddings1 = rescale(
            PCA(n_components=5).fit_transform(embeddings1)
        )  # starting point to UMPA
        umap_model1 = UMAP(
            n_neighbors=15,
            n_components=5,
            min_dist=0.0,
            metric="cosine",
            init=pca_embeddings1,
            random_state=100,
        )  # Reduce dimensionality
        hdbscan_model1 = HDBSCAN(
            min_cluster_size=25,
            min_samples=5,
            metric="euclidean",
            cluster_selection_method="eom",
            prediction_data=True,
        )  # Cluster reduced embeddings min_df=min_count
        vectorizer_model1 = CountVectorizer()  # Tokenize topics
        ctfidf_model1 = ClassTfidfTransformer(
            reduce_frequent_words=True
        )  # Create topic representation
        #
        ### Putting all hyper-parameters together
        #
        topic_model1 = BERTopic(
            embedding_model=embedding_model1,
            umap_model=umap_model1,
            hdbscan_model=hdbscan_model1,
            vectorizer_model=vectorizer_model1,
            ctfidf_model=ctfidf_model1,
            top_n_words=4,
            nr_topics=12,
            low_memory=True,
            calculate_probabilities=False,
        )
        #
        #
        ### Stories
        #
        logger.info("At stage: Stories for %s", query_id)

        try:
            topics1, probs1 = topic_model1.fit_transform(lemitized, embeddings1)
        except Exception as e:
            My_DF2["Stories"] = -1
            logger.info(f"Skipping the story due to error in fitting topic model: {e}")
            continue

        Stories = topic_model1.get_topic_info()
        # Stories_Tweets=topic_model1.get_document_info(My_DF2['Lemitized'])
        #
        ### Outlier Reduction
        #
        if Stories.loc[0, "Topic"] == -1 and len(Stories) != 1:
            topics_New1 = topic_model1.reduce_outliers(
                My_DF2["Lemitized"],
                topics1,
                probabilities=probs1,
                threshold=0.05,
                strategy="c-tf-idf",
            )
            topic_model1.update_topics(My_DF2["Lemitized"], topics=topics_New1)
            Stories_New = topic_model1.get_topic_info()
            # Theme_Tweets_New=topic_model.get_document_info(My_DF1['Lemitized'])
        else:
            topics_New1 = topics1
            Stories_New = Stories
        # del [topics1,probs1,Stories]
        #
        ### Topic Reduction
        #
        logger.info("At stage: Stories topic reduction for %s", query_id)

        if len(Stories_New) > 11:
            topic_model1.reduce_topics(My_DF2["Lemitized"], nr_topics=10)
            Stories_New_R = topic_model1.get_topic_info()
            topics_New_R1 = topic_model1.topics_
            # Theme_Tweets_New=topic_model.get_document_info(My_DF1['Lemitized'])
        else:
            topics_New_R1 = topics_New1
            Stories_New_R = Stories_New
            # Theme_Tweets_New=Theme_Tweets
        # del [topics_New1,Stories_New,topics1,probs1,Stories]
        # Stories_Tweets_New_R=topic_model.get_document_info(My_DF2['Lemitized'])
        Keywords10_Stories_weight = topic_model1.topic_representations_
        #
        ### Inputing Story Data
        #
        ind = My_DF2["index"].tolist()
        Data1.loc[ind, "Stories"] = topics_New_R1
        del [
            My_DF2,
            embedding_model1,
            embeddings1,
            pca_embeddings1,
            umap_model1,
            hdbscan_model1,
            vectorizer_model1,
            ctfidf_model1,
            topic_model1,
            topics1,
            probs1,
            Stories,
            topics_New1,
            Stories_New,
            topics_New_R1,
            Stories_New_R,
            Keywords10_Stories_weight,
            ind,
        ]
    del [
        x2,
        Themes_New_R,
    ]  # Data,
    logger.info("At stage: Completed discover function for %s", query_id)

    return Data1


#
### IMPORTING DATA FILES
#
def trigger_discover(payload):
    """
    This is main triggering function of the discover and steps that it
    Steps:
    - Extract the payload and get the query_id and encapsulation marker
    - Set the configuration for the capabilities utils
    - Toggle the diagnostics to pending
    - Set the meta container and rac collection
    - Fetch the records and load it the data frame called data_df
    - Get the min_count from the number of records
    - Get the hastag and keywords from the data_df
    - Get the Vocab from the data_df , Hashtag and KW
    - Define the embedding model and get the embeddings and topic model for the themes
    - Generate topics , probs and themes
    - Perform outlier reduction and topic reduction and get new topic , themes and keywords
    - Filter the dataframe for the stories and perform same operation as for the theme
    - Filter data frame by the index and update the story summary
    - Updated theme and stories numbers in rac collection
    - Check if all the encapuslation marker are completed and toggle the diagnostics to completed
    - Publish message to capabilities router

    Parameters:
    - payload : Pubsub payload for discover

    Returns:
    - None
    """
    capabilities_utils = None
    try:
        query_id, encapsulation_marker, encapsulation_payload = (
            payload.get("query_id"),
            payload.get("encapsulation_marker"),
            payload.get("encapsulation"),
        )
        start_time = time.perf_counter()

        meta_container = MetaContainer()
        capabilities_utils = CapabilitiesUtils()
        capabilities_utils.set_configuration(payload)
        meta_container = capabilities_utils.meta_container
        rac_collection = capabilities_utils.rac_collection
        data_df = pd.DataFrame(capabilities_utils.fetch_data())
        db = capabilities_utils.organization_db

        message = f"Capabilities Discover script started successfully for query_id {query_id} and encapsulation_marker {encapsulation_marker}"
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.PENDING.value,
            message,
            consumer_type=CONSUMER_TYPE_DISCOVER,
        )

        encapsulation_marker_collection_name = meta_container.meta_data[
            ENCAPSULATION_MARKER_COLLECTION_NAME_METADATA_KEY
        ]

        encapsulation_marker_collection = get_mongodb_collection(
            db, encapsulation_marker_collection_name
        )

        logger.info("Encapsulation marker fetched for query_id %s", query_id)

        encapsulation_marker_collection.update_one(
            {"_id": ObjectId(encapsulation_payload["_id"])},
            {
                "$set": {
                    EncapsulationMarkerStatusColumn.DISCOVER_STATUS.value: EncapsulationMarkerStatus.PENDING.value
                }
            },
        )
        logger.info(
            "Update one for query_id %s and calling discover main function", query_id
        )

        data_df = discover(data_df, query_id)
        data_df["Stories"] = data_df["Stories"].fillna(-1)

        #
        ### Output
        #
        # data_df["Stories"] = data_df["Stories"].astype(int)
        # records = data_df.to_dict(orient="records")
        logger.info(
            "Discover function end, Starting bulk write for query_id %s", query_id
        )

        records = data_df[["_id", "Themes", "Stories"]].to_dict(orient="records")
        updated_fields = []
        for record in records:
            updated_fields.append(
                UpdateOne(
                    {"_id": record["_id"]},
                    {
                        "$set": {
                            "Themes": record["Themes"],
                            "Stories": record["Stories"],
                        }
                    },
                )
            )
        if updated_fields:
            rac_collection.bulk_write(updated_fields)

        encapsulation_marker_collection.update_one(
            {"_id": ObjectId(encapsulation_payload["_id"])},
            {
                "$set": {
                    EncapsulationMarkerStatusColumn.DISCOVER_STATUS.value: EncapsulationMarkerStatus.COMPLETED.value
                }
            },
        )

        logger.info("Checking bulk write and update completition for %s", query_id)

        is_all_encapsulation_markers_completed = (
            encapsulation_marker_collection.count_documents(
                {
                    "query_id": ObjectId(query_id),
                    EncapsulationMarkerStatusColumn.DISCOVER_STATUS.value: {
                        "$ne": EncapsulationMarkerStatus.COMPLETED.value
                    },
                }
            )
            == 0
        )

        if is_all_encapsulation_markers_completed:
            message = f"Capabilities Discover script started successfully for query_id {query_id} and encapsulation_marker {encapsulation_marker}"
            meta_container.send_diagnostic(
                DiagnosticActionType.UPDATE.value,
                DiagnosticStatus.COMPLETED.value,
                message,
                consumer_type=CONSUMER_TYPE_DISCOVER,
            )

        # capabilities_utils.publish_message()
        payload["publisher_type"] = CONSUMER_TYPE_DISCOVER
        publish_pubsub_message(PROJECT_ID, SEQUENCE_COORDINATOR_TOPIC_ID, payload)

        end_time = time.perf_counter()
        total_time = end_time - start_time

        logger.info(
            f"Time taken for the discover to process for query_id {query_id} and encapsulation_marker {encapsulation_marker} is :{total_time}"
        )
    except Exception as e:
        message = f"An error occured while processing discover :{e}"
        logger.exception(message)
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.FAILED.value,
            message,
            consumer_type=CONSUMER_TYPE_DISCOVER,
            error=str(type(e)),
        )
