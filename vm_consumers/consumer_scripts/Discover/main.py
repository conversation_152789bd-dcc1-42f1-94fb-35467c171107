# from concurrent.futures import TimeoutError
# from google.cloud import pubsub_v1
# import json
# from const import PROJECT_ID, SUBSCRIPTION_ID
# from discover import trigger_discover
# from utils.logger import logger

#TODO: De-comment the lines below to process vm consumers using PubSub messages

# subscriber = pubsub_v1.SubscriberClient()
# subscription_path = subscriber.subscription_path(PROJECT_ID, SUBSCRIPTION_ID)


# def callback(message: pubsub_v1.subscriber.message.Message) -> None:
#     logger.info(f"Received data of the message {message.data}")
#     logger.info(f"With delivery attempts: {message.delivery_attempt}.")
#     decoded = message.data.decode("utf-8")
#     payload = json.loads(decoded)
#     message.ack()
#     trigger_discover(payload)


# streaming_pull_future = subscriber.subscribe(subscription_path, callback=callback)
# logger.info(f"Listening for messages on {subscription_path}..\n")

# # Wrap subscriber in a 'with' block to automatically call close() when done.
# with subscriber:
#     # When `timeout` is not set, result() will block indefinitely,
#     # unless an exception is encountered first.
#     try:
#         streaming_pull_future.result()
#     except TimeoutError:
#         streaming_pull_future.cancel()  # Trigger the shutdown.
#         streaming_pull_future.result()  # Block until the shutdown is complete.
