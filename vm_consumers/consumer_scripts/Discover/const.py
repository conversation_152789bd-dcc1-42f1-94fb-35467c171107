import os

PROJECT_ID = os.getenv("PROJECT_ID", "tellagence-platform")
SUBSCRIPTION_ID = os.getenv("SUBSCRIPTION_ID", "discover-subscription")
SEQUENCE_COORDINATOR_TOPIC_ID = os.getenv("SEQUENCE_COORDINATOR_TOPIC_ID")

ENCAPSULATION_MARKER_COLLECTION_NAME_METADATA_KEY = (
    "ENCAPSULATION_MARKER_COLLECTION_NAME"
)

MAX_STORY_COUNT = 11
MAX_THEME_COUNT = 11

CONSUMER_TYPE_DISCOVER = os.getenv("CONSUMER_TYPE_DISCOVER")