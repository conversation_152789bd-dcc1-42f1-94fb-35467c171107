"""
This module processes a cloud event to execute the capabilities categorization tool process.
It handles the categorization, embeddings, and clustering of data, updates encapsulation markers,
performs BigQuery operations, and communicates with the sequence coordinator Pub/Sub topic.
Diagnostic updates are sent throughout the process to ensure traceability and error handling.
"""

import time
from typing import Dict
from bson import ObjectId
from consumer_scripts.Categorization_tool.bigquery import perform_bigquery_operations
from consumer_scripts.Categorization_tool.categorization import perform_categorization
from consumer_scripts.Categorization_tool.clustering import perform_clustering
from consumer_scripts.Categorization_tool.const import (
    CLIENT_SPECIFIED_CATEGORIES_METADATA_KEY,
    EMBEDDING_MODEL_NAME_METADATA_KEY,
    PROJECT_ID,
    QUERY_COLLECTION_NAME,
    ENCAPSULATION_MARKER_COLLECTION_NAME_METADATA_KEY,
    MODEL_NAME_METADATA_KEY,
    RAC_COLLECTION_NAME_METADATA_KEY,
    QUERY_PROJECTION_ATTRIBUTES,
    ORG_CREDENTIALS_COLLECTION,
    RAC_TRANSFORM_COLLECTION_NAME_METADATA_KEY,
    SEQUENCE_COORDINATOR_TOPIC_ID,
    CONSUMER_TYPE_CATEGORIZATION,
)
from utils.common_utils import (
    check_query_status,
    get_query_config,
    toggle_marker_status,
)
from utils.pubsub_publisher import publish_pubsub_message
from utils.langchain_utils import LangChainUtility
from utils.logger import logger
from utils.mongo_db import get_mongodb_client, get_mongodb_collection, get_mongodb_db
from utils.utilities import (
    DiagnosticActionType,
    DiagnosticStatus,
    EncapsulationMarkerStatus,
    EncapsulationMarkerStatusColumn,
    MetaContainer,
    OrganizationAccountInfo,
)


def trigger_categorization(payload: Dict) -> tuple | None:
    """
    Processes a cloud event to execute the capabilities categorization tool process.

    Parameters:
    - payload (dict): The decoded and parsed content of the incoming event message.

    Steps:
    1. Logs the message payload.
    2. Set metadata and payload information in the meta container.
    3. Retrieve organization account details and initialize the MongoDB client.
    4. Fetch query configuration and associated metadata.
    5. Iterate through encapsulation markers for the query and:
       - Update encapsulation marker status to 'PENDING'.
       - Perform categorization.
       - Perform embeddings.
       - Perform clustering.
       - Update encapsulation marker status to 'COMPLETED'.
    6. If all encapsulation markers are completed, perform BigQuery operations, and
        send a message to the sequence coordinator Pub/Sub topic.
    7. Send diagnostic updates for success or failure.

    Returns:
    - tuple: A success message and the corresponding HTTP status code.

    Exception Handling:
    - Exception: If any error occurs during the categorization process, the error
        message is sent to the diagnostic engine, and updates marker status to 'FAILED'
    """
    mongo_db_client = None
    try:
        langchain_utility = LangChainUtility()
        meta_container = MetaContainer()
        logger.info("Message received successfully: %s", payload)
        meta_container.set_payload_info(payload)

        data_source_id = payload["data_source_id"]
        organization_id = payload["organization_id"]
        query_id = payload["query_id"]
        encapsulation_marker_id = None

        message = f"Capabilities categorization tool script started successfully for query_id: {query_id}"
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.PENDING.value,
            message,
            consumer_type=CONSUMER_TYPE_CATEGORIZATION,
        )

        start_time = time.perf_counter()

        org_account_info = OrganizationAccountInfo(organization_id)
        mongodb_url = org_account_info.mongodb_url
        organization_db_name = org_account_info.organization_db_name

        mongo_db_client = get_mongodb_client(mongodb_url)
        organization_db = get_mongodb_db(mongo_db_client, organization_db_name)
        query_collection = get_mongodb_collection(
            organization_db, QUERY_COLLECTION_NAME
        )

        query_config = get_query_config(
            query_collection, query_id, QUERY_PROJECTION_ATTRIBUTES
        )
        if not query_config:
            logger.warning("No configuration found for query_id: '%s'", query_id)
            return

        query_meta_data = query_config["meta_data"]
        query_meta_data = (
            query_meta_data[0] if isinstance(query_meta_data, list) else query_meta_data
        )
        meta_container.set_meta_data(query_meta_data)

        # Extract client-specified categories for particular organization
        source_meta_data = query_config.get("source", {}).get("meta_data", {})
        client_specified_categories = source_meta_data.get(
            CLIENT_SPECIFIED_CATEGORIES_METADATA_KEY
        )

        encapsulation_marker_collection_name = query_meta_data[
            ENCAPSULATION_MARKER_COLLECTION_NAME_METADATA_KEY
        ]
        rac_collection_name = query_meta_data[RAC_COLLECTION_NAME_METADATA_KEY]
        rac_transform_collection_name = query_meta_data[
            RAC_TRANSFORM_COLLECTION_NAME_METADATA_KEY
        ]
        encapsulation_marker_collection = get_mongodb_collection(
            organization_db, encapsulation_marker_collection_name
        )
        rac_collection = get_mongodb_collection(organization_db, rac_collection_name)
        rac_transform_collection = get_mongodb_collection(
            organization_db, rac_transform_collection_name
        )

        org_credentials_collection = get_mongodb_collection(
            organization_db, ORG_CREDENTIALS_COLLECTION
        )
        # Set configuration for the langchain utility
        langchain_utility.set_configuration(
            query_meta_data[MODEL_NAME_METADATA_KEY],
            org_credentials_collection,
            query_meta_data[EMBEDDING_MODEL_NAME_METADATA_KEY],
            consumer_type=CONSUMER_TYPE_CATEGORIZATION,
        )

        # Get the encapsulation makers for particular query
        encapsulation_markers = encapsulation_marker_collection.find(
            {
                "query_id": ObjectId(query_id),
                EncapsulationMarkerStatusColumn.CATEGORIZATION_TOOL_STATUS.value: {
                    "$ne": EncapsulationMarkerStatus.COMPLETED.value
                },
            }
        )

        # Loop through each marker serially
        for encapsulation in encapsulation_markers:
            encapsulation_marker = encapsulation["encapsulation_marker"]
            encapsulation_marker_id = encapsulation["_id"]

            # Update the encapsulation marker status to 'PENDING' for categorization processing
            toggle_marker_status(
                encapsulation_marker_collection,
                encapsulation_marker_id,
                query_id,
                EncapsulationMarkerStatusColumn.CATEGORIZATION_TOOL_STATUS.value,
                EncapsulationMarkerStatus.PENDING.value,
            )

            # Perform categorization process
            categorization_and_embedding_start_time = time.perf_counter()
            perform_categorization(
                query_id,
                data_source_id,
                encapsulation_marker,
                encapsulation_marker_id,
                client_specified_categories,
                encapsulation_marker_collection,
                rac_collection,
                langchain_utility,
                meta_container,
            )
            categorization_and_embedding_end_time = time.perf_counter()
            categorization_and_embedding_total_time = (
                categorization_and_embedding_end_time
                - categorization_and_embedding_start_time
            )

            # Perform clustering process
            clustering_start_time = time.perf_counter()
            perform_clustering(
                data_source_id, encapsulation_marker, rac_collection, langchain_utility
            )
            clustering_end_time = time.perf_counter()
            clustering_total_time = clustering_end_time - clustering_start_time

            # Mark the process as 'COMPLETED' for particular marker after successful
            # categorization, embedding, and clustering.
            toggle_marker_status(
                encapsulation_marker_collection,
                encapsulation_marker_id,
                query_id,
                EncapsulationMarkerStatusColumn.CATEGORIZATION_TOOL_STATUS.value,
                EncapsulationMarkerStatus.COMPLETED.value,
            )

        # Check if all encapsulation markers have been completed
        if check_query_status(
            encapsulation_marker_collection,
            query_id,
            EncapsulationMarkerStatusColumn.CATEGORIZATION_TOOL_STATUS.value,
            EncapsulationMarkerStatus.COMPLETED.value,
        ):
            # Perform big query operations
            perform_bigquery_operations(
                data_source_id,
                query_collection,
                rac_collection,
                rac_transform_collection,
                organization_db_name,
                meta_container,
            )

            # Trigger the sequence coordinator by publishing a message to
            # the specified Pub/Sub topic
            payload["publisher_type"] = CONSUMER_TYPE_CATEGORIZATION
            publish_pubsub_message(PROJECT_ID, SEQUENCE_COORDINATOR_TOPIC_ID, payload)
            end_time = time.perf_counter()
            total_time = end_time - start_time
            message = (
                f"Capabilities categorization tool script completed successfully:\n"
                f"  • Query ID: {query_id}\n"
                f"  • Total time taken: {total_time:.2f}s\n"
                f"  • Categorization & embedding time taken: {categorization_and_embedding_total_time:.2f}s\n"
                f"  • Clustering time taken: {clustering_total_time:.2f}s"
            )
            meta_container.send_diagnostic(
                DiagnosticActionType.UPDATE.value,
                DiagnosticStatus.COMPLETED.value,
                message,
                consumer_type=CONSUMER_TYPE_CATEGORIZATION,
            )

        return "Success", 200

    except Exception as e:
        message = f"An error occurred during capabilities categorization tool: {e}"

        logger.exception(message)

        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.FAILED.value,
            message,
            e,
            consumer_type=CONSUMER_TYPE_CATEGORIZATION,
        )
        toggle_marker_status(
            encapsulation_marker_collection,
            encapsulation_marker_id,
            query_id,
            EncapsulationMarkerStatusColumn.CATEGORIZATION_TOOL_STATUS.value,
            EncapsulationMarkerStatus.FAILED.value,
        )

    finally:
        if mongo_db_client is not None:
            mongo_db_client.close()
