"""
This module processes and generates embeddings for text data using LangChainUtility and 
Dask for parallel processing. The functions are designed to handle large datasets 
efficiently by splitting text into manageable batches and computing embeddings in parallel. 
The embeddings are then normalized and added to the provided DataFrame.
"""

import os
from typing import List
from dask import delayed, compute
import dask.dataframe as dd
import pandas as pd
import numpy as np
from consumer_scripts.Categorization_tool.const import (
    DEFAULT_CATEGORIZATION,
    MAX_INPUT_TEXTS,
    TASK_TYPE,
    ZERO_VECTOR_SIZE,
    CategorizationColumns,
)
from utils.langchain_utils import LangChainUtility
from utils.logger import logger


def generate_embeddings(
    texts: List[str], langchain_utility: LangChainUtility
) -> np.ndarray:
    """
    Generate embeddings for a list of input texts.

    Parameters:
    - texts (List[str]): A list of strings for which embeddings need to be generated.
    - langchain_utility (LangChainUtility): LangChainUtility class Instance.

    Returns:
    - np.ndarray: A numpy array containing the embeddings for the input texts.

    Exception Handling:
    - Exception: Raised if an error occurs while generating embeddings for the texts.
    """
    try:
        embeddings = langchain_utility.embed_texts(texts, TASK_TYPE)
        # Convert to np.ndarray for consistency
        return np.array(embeddings)

    except Exception as e:
        logger.error("Error generating embeddings for texts: %s", e)
        raise


def normalize(embeddings):
    """
    Normalize the input embeddings to unit vectors.

    Parameters:
    - embeddings (numpy.ndarray or None): The input embeddings to be normalized.
    If None, a zero vector of size 768 is returned.

    Returns:
    - list: The normalized embeddings (unit vector) as a list. If the embeddings are all zeros,
    the original embeddings are returned as a list.

    Exception Handling:
    - None
    """
    if embeddings is None:
        embeddings = np.zeros(ZERO_VECTOR_SIZE)

    embeddings = np.array(embeddings)  # Ensure input is a NumPy array
    norm = np.linalg.norm(embeddings)
    if norm == 0:  # Check if the norm is zero
        return embeddings.tolist()

    normalized_embedding = embeddings / norm
    return normalized_embedding.tolist()


# Convert each partition to a delayed function
@delayed
def process_embedding(
    partitioned_df: pd.DataFrame,
    categorization_columns: CategorizationColumns,
    langchain_utility: LangChainUtility,
):
    """
    Process embedding data using batches and generate embeddings for the provided texts.
    - Split the data based on the token count and maximum input text length,
        and process it in batches for embedding.

    Parameters:
    - partitioned_df (pd.DataFrame): Input Pandas DataFrame containing the
        partitioned data to be embedded, specifically the column with
        combined topics for text embedding.
    - categorization_columns (CategorizationColumns): An object containing
        column names for categorization explanation, topics, sub-topics, combined topic,
        notable entities, embedding and other related fields.
    - langchain_utility (LangChainUtility): LangChainUtility class Instance.

    Returns:
    - pd.DataFrame: Processed Pandas DataFrame containing the original data with added
        categorization embedding column.

    Exception Handling:
    - None
    """
    texts = partitioned_df[categorization_columns.combined_topic].tolist()
    token_count = langchain_utility.get_num_tokens(texts)

    # Calculate the number of the split from token count and or the maximum number of the texts per batch
    n = max(
        token_count // langchain_utility.embedding_max_token_count + 1,
        len(texts) // MAX_INPUT_TEXTS + 1,
    )

    # Create the batches for embedding
    batches = np.array_split(texts, n)

    # Generate the embedding for each batch
    processed_batches = [
        generate_embeddings(batch.tolist(), langchain_utility)
        for batch in batches
        if batch.tolist()
    ]

    # Concat to get the embeddings for all the text
    embeddings = np.concatenate(processed_batches)
    partitioned_df[categorization_columns.embedding] = embeddings.tolist()

    return partitioned_df


def process_embeddings_with_dask(
    categorization_df: pd.DataFrame,
    categorization_columns: CategorizationColumns,
    encapsulation_marker: str,
    langchain_utility: LangChainUtility,
):
    """
    Process embeddings for a given categorization DataFrame using Dask to
    optimize for larger datasets.

    - The function generates embeddings by using 'combined_topic' column,
    filters out rows with the value "Unable to categorize", and processes embeddings in parallel using Dask.

    Parameters:
    - categorization_df (pd.DataFrame): The input DataFrame containing data with 'topic' and 'subtopics'
    columns for which embeddings will be generated.
    - categorization_columns (CategorizationColumns): An object containing column names for
        categorization explanation,
    topics, sub-topics, combined topic, notable entities, embedding and other related fields.
    - langchain_utility (LangChainUtility): LangChainUtility class Instance.

    Returns:
    - pd.DataFrame: The original DataFrame with updated embeddings after processing.

    Exception Handling:
    - Exception: Raised if an error occurs during process (e.g., while generating embeddings
        or during Dask operations), logs an error message and raises the exception.
    """
    try:
        # Get combined topic column name
        combined_topic_col = categorization_columns.combined_topic

        # Filter the DataFrame where combined topic does not contain "Unable to categorize"
        filtered_categorization_df = categorization_df[
            ~categorization_df[combined_topic_col].str.contains(DEFAULT_CATEGORIZATION)
        ]

        # Update the DataFrame to retain only the 'combined_topic_col' column
        filtered_categorization_df = filtered_categorization_df[[combined_topic_col]]

        # Drop duplicate combined topics
        filtered_categorization_df_trunc = filtered_categorization_df.drop_duplicates()

        # Convert DataFrame to Dask DataFrame with partitions based on CPU count
        ddf = dd.from_pandas(
            filtered_categorization_df_trunc, npartitions=os.cpu_count()
        )

        # Apply process embedding function to each partition and compute the result
        delayed_partitions = [
            process_embedding(part, categorization_columns, langchain_utility)
            for part in ddf.to_delayed()
        ]
        computed_partitions = compute(*delayed_partitions, scheduler="threads")
        filtered_categorization_df_trunc = pd.concat(computed_partitions)

        original_index = (
            filtered_categorization_df.index
        )  # Save the original index of left df
        # Map the embeddings onto the original dataframe (dataframe before dropping duplicates)
        filtered_categorization_df = pd.merge(
            filtered_categorization_df,
            filtered_categorization_df_trunc,
            on=[combined_topic_col],
            how="left",
        )
        filtered_categorization_df.index = original_index  # Restore the original index

        # Updating the 'categorization_embedding' column in the original df with embeddings
        categorization_df.loc[
            filtered_categorization_df.index, categorization_columns.embedding
        ] = filtered_categorization_df[categorization_columns.embedding]

        # Normalize the embeddings
        categorization_df[categorization_columns.embedding] = categorization_df[
            categorization_columns.embedding
        ].apply(normalize)

        return categorization_df

    except Exception as e:
        logger.error(
            "Error while processing embeddings for '%s' encapsulation_marker: %s",
            encapsulation_marker,
            e,
        )
        raise
