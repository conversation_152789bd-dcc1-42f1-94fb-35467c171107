"""
This module contains constants used in files of categorization tool consumer.
"""

import os
from dataclasses import dataclass
from typing import Union
from utils.utilities import DataSourceId, SummaryContext

ZERO_VECTOR_SIZE = 768
ORG_CREDENTIALS_COLLECTION = os.getenv("ORG_CREDENTIALS_COLLECTION")
QUERY_COLLECTION_NAME = os.getenv("QUERY_COLLECTION_NAME")
CONSUMER_TYPE_CATEGORIZATION = os.getenv("CONSUMER_TYPE_CATEGORIZATION")
PROJECT_ID = os.getenv("PROJECT_ID")
SUBSCRIPTION_ID = os.getenv("SUBSCRIPTION_ID")
SEQUENCE_COORDINATOR_TOPIC_ID = os.getenv("SEQUENCE_COORDINATOR_TOPIC_ID")

CATEGORIZATION_BQ_CHUNK_SIZE = os.getenv("CATEGORIZATION_BQ_CHUNK_SIZE")
CATEGORIZATION_PROMPT_CHUNK_SIZE = os.getenv("CATEGORIZATION_PROMPT_CHUNK_SIZE")
MAX_PARSE_RETRIES = os.getenv("MAX_PARSE_RETRIES")
MAX_RECORD_COUNT = os.getenv("MAX_RECORD_COUNT")
MAX_UNIQUE_GROUPS = os.getenv("MAX_UNIQUE_GROUPS")
PARSE_RETRY_DELAY = os.getenv("PARSE_RETRY_DELAY")

CATEGORIZATION_BQ_OFFSET_METADATA_KEY = "CATEGORIZATION_BQ_OFFSET"
EMBEDDING_MODEL_NAME_METADATA_KEY = "EMBEDDING_MODEL_NAME"
ENCAPSULATION_MARKER_COLLECTION_NAME_METADATA_KEY = (
    "ENCAPSULATION_MARKER_COLLECTION_NAME"
)
CLIENT_SPECIFIED_CATEGORIES_METADATA_KEY = "CLIENT_SPECIFIED_CATEGORIES"
RAC_VOLUME_METADATA_KEY = "RAC_VOLUME"
RAC_COLLECTION_NAME_METADATA_KEY = "RAC_COLLECTION_NAME"
RAC_TRANSFORM_COLLECTION_NAME_METADATA_KEY = "RAC_TRANSFORM_COLLECTION_NAME"
MODEL_NAME_METADATA_KEY = "MODEL_NAME"
QUERY_PROJECTION_ATTRIBUTES = {
    f"meta_data.{CATEGORIZATION_BQ_OFFSET_METADATA_KEY}": 1,
    f"meta_data.{ENCAPSULATION_MARKER_COLLECTION_NAME_METADATA_KEY}": 1,
    f"meta_data.{EMBEDDING_MODEL_NAME_METADATA_KEY}": 1,
    f"meta_data.{MODEL_NAME_METADATA_KEY}": 1,
    f"meta_data.{RAC_COLLECTION_NAME_METADATA_KEY}": 1,
    f"meta_data.{RAC_TRANSFORM_COLLECTION_NAME_METADATA_KEY}": 1,
    f"source.meta_data.{CLIENT_SPECIFIED_CATEGORIES_METADATA_KEY}": 1,
}
RAW_TEXT_COL = "BODY1"
TEMP_TABLE_SUFFIX = "temp"
VIDEO_ID_COL = "video_id"

# For embedding generation
MAX_INPUT_TEXTS = 250
TASK_TYPE = "CLUSTERING"

DEFAULT_EXPLANATION = "No explanation generated"
DEFAULT_CATEGORIZATION = "Unable to categorize"
DEFAULT_CLUSTER_NAME = "Unnamed Cluster"

DEFAULT_CATEGORIES = [
    "Adoption and usage of products",
    "Love/hate of products or campaigns",
    "Feature requests",
    "Support issues (i.e. product access, outages)",
    "Product deprecations/migrations",
    "Comparisons to competitors",
    "Ad experience",
    "Algorithm / recommendations",
    "Pricing",
    "Partnerships",
    "Features (video quality, multiview, etc.)",
    "Events (ex. VidCon, Creator Collective, etc)",
]


@dataclass
class BaseCategorizationColumns:
    """
    Base dataclass representing common column names for categorization across all data sources.

    Attributes:
    - embedding (str): Column name for the embedding.
    - topic (str): Column name for the main topic.
    - sub_topics (str): Column name for sub-topics.
    - categorization_explanation (str): Column name for categorization explanation.
    - notable_entities (str): Column name for notable entities.
    - subject (str): Column name for the subject.
    - object (str): Column name for the object.
    - combined_topic (str): Column name for combined topics.
    - cluster_prediction (str): Column name for cluster predictions.
    - cluster_name (str): Column name for cluster names.
    - is_categorization_processed (str): Column name for categorization processed.
    """

    embedding: str
    topic: str
    sub_topics: str
    categorization_explanation: str
    notable_entities: str
    subject: str
    object: str
    combined_topic: str
    cluster_prediction: str
    cluster_name: str
    is_categorization_processed: str


@dataclass
class YouTubeCategorizationColumns(BaseCategorizationColumns):
    """YouTube specific categorization columns."""

    high_level_category: str
    youtube_category: str


@dataclass
class FileUploadCategorizationColumns(BaseCategorizationColumns):
    """File upload specific categorization columns."""

    client_specified_category: str


# Type alias for categorization columns that supports both YouTube and file upload data sources
CategorizationColumns = Union[
    YouTubeCategorizationColumns, FileUploadCategorizationColumns
]


def get_categorization_columns(
    context: str, data_source_id: str
) -> CategorizationColumns:
    """
    Generates a CategorizationColumns object with column names based on the provided context and data source Id.

    Parameters:
    - context (str): The processing context which can be one of "CLUSTER", "STORY", or "THEME".
    - data_source_id (str): The identifier of the data source.

    Returns:
    - CategorizationColumns: An object containing column names for categorization-related fields.
    """
    base_name = f"{SummaryContext[context].value}_categorization"
    base_columns = {
        "embedding": f"{base_name}_embedding",
        "topic": f"{base_name}_topic",
        "sub_topics": f"{base_name}_sub_topics",
        "categorization_explanation": f"{base_name}_explanation",
        "notable_entities": f"{base_name}_notable_entities",
        "subject": f"{base_name}_subject",
        "object": f"{base_name}_object",
        "combined_topic": f"{base_name}_combined_topic",
        "cluster_prediction": f"{base_name}_cluster_prediction",
        "cluster_name": f"{base_name}_cluster_name",
        "is_categorization_processed": f"is_{base_name}_processed",
    }

    match data_source_id:
        case DataSourceId.YT_COMMENTS.value:
            return YouTubeCategorizationColumns(
                **base_columns,
                high_level_category=f"{base_name}_high_level_category",
                youtube_category=f"{base_name}_youtube_category",
            )
        case _:
            return FileUploadCategorizationColumns(
                **base_columns,
                client_specified_category=f"{base_name}_client_specified_category",
            )


# ------------------------------------- Categorization Generation Prompts ----------------------------------------------------------

CATEGORIZATION_BATCH_PROMPT_YT_COMMENTS = """
# OBJECTIVE #
You are a skilled analyst tasked with classifying comments on a YouTube video.


#STEPS#
1. Identify and list the primary topic for given texts in one word or phrase.
2. If there are subtopics or secondary themes mentioned in the text, list them as well. If the text discusses multiple topics, provide a list of these topics and describe their relevance.
3. Consider the context and tone of the text to determine the most appropriate topics. Take into account keywords, phrases, or specific terms that relate to the topics.
4. If any notable entities (people, places, brands, products, etc.) are mentioned in the text that play a role in the topics, mention them and their associations.
5. Additionally, identify the subject and object of the comment and provide the following categorization:
      A. high_level_category: 
      i. Comments where the subject or object of the sentence is YouTube or YT, Category 1 should be set to "YouTube-related comment".
      ii. For comments that are not "YouTube-related" use the following criteria for Category 1:
          a. Content-related - Comments pertaining to the content of the video or contain references of the content in the video transcript
          b. Creator-related - Comments directed to the content creator, individual or channel that posted the content.
      
      B. youtube_category:
      Map the primary topic identified in Step 1. above to one of the following categories:
      {categories}


#CONTEXT:#
Here is some context regarding the comments: 
Summary of the comments: {summary}
YouTube video title: {video_title}
YouTube video description: {video_description}
YouTube video transcript: {transcript}


#OUTPUT STRUCTURE:#
The response must be in strict JSON format as an array of objects. Ensure that:
  1. The JSON is properly formatted and error-free.
  2. All objects include all specified keys, even if the value is empty or null.
  3. Strings are enclosed in double quotes ("), and arrays use proper brackets ([]).
  4. Values follow the correct data types (e.g., numbers for <record_number>, strings for <subject>).

Required JSON Structure:
[{{
  "record_number": <number>,
  "topic": "<string>",
  "sub_topics": ["<string>", "<string>", ...],
  "explanation": "<string>",
  "notable_entities": ["<string>", "<string>", ...],
  "subject": "<string>",
  "object": "<string>",
  "high_level_category": "<string>",
  "youtube_category": "<string>"
}}]


#EXAMPLES:#
Here's are a few examples:

Example 1: 
comment: This video has literally lit up a light in my brain. So much stuff is making sense. I had so many unconnected dots.
Response: 
[{{
  "record_number": 1,
  "topic": "Understanding and Insight",
  "sub_topics": ["Cognitive Clarity", "Connection of Ideas"],
  "explanation": "The text expresses a feeling of newfound understanding and clarity, suggesting that the video has helped the author connect previously unconnected ideas.",
  "notable_entities": ["Video"],
  "subject": "This video",
  "object": "A light in my brain",
  "high_level_category": "Content-related",
  "youtube_category": "{example_category}"
}}]

Example 2:
comment: Yes!  Thanks 3b1b, this is really intuitive compared to most overly-jargon filled papers.
Response:
[{{
  "record_number": 2,
  "topic": "Educational Content Appreciation",
  "sub_topics": ["Intuitive Explanation", "Jargon-Free Language"],
  "explanation": "The comment expresses positive feedback on the video, highlighting its clarity and accessibility compared to other academic materials.",
  "notable_entities": ["3b1b", "Papers"],
  "subject": "3b1b",
  "object": "this (referring to the intuitive explanation)",
  "high_level_category": "Content-related",
  "youtube_category": "{example_category}"
}}]

Example 3:
comment: Any update on the next chapter coming out? :)
Response:
[{{
  "record_number": 3,
  "topic": "Video Release Schedule",
  "sub_topics": ["Next Chapter", "Release Date"],
  "explanation": "The comment inquires about the release date of the next chapter in the video series.",
  "notable_entities": ["Next Chapter"],
  "subject": "You (implied)",
  "object": "update",
  "high_level_category": "Creator-related",
  "youtube_category": "{example_category}"
}}]

Example 4:
comment: YT, why so many ads! :(
Response:
[{{
  "record_number": 4,
  "topic": "Ads on YouTube",
  "sub_topics": ["YT", "Advertisements"],
  "explanation": "The text expresses dissatisfaction with the YouTube experience due to the number of advertisements shown during the video.",
  "notable_entities": ["YT", "Ads"],
  "subject": "YT (YouTube)",
  "object": "ads",
  "high_level_category": "YouTube-related",
  "youtube_category": "{example_category}"
}}]

Example 5:
comment: Love your content, but you may want to address an interesting issue in one of your examples - the whodunit novel where the last word is the identity of the murderer.  You aren't saying that the attention block can reason are you? It would only be successful if there was some text that said something like 'John was next to the victim when he died', that context could be parroted back, but deeper reasoning like the killer was left handed, female and spoke french and only one character had all of those attributes. Or are you?
Response:
[{{
  "record_number": 5,
  "topic": "Ability of transformers to reason",
  "sub_topics": ["Attention block", "Novel"],
  "explanation": "The text expresses appreciation towards the content as well as a doubt whether attention blocks have the ability to reason.",
  "notable_entities": ["Whodunit Novel"],
  "subject": "You",
  "object": "an interesting issue",
  "high_level_category": "Content-related",
  "youtube_category": "{example_category}"
}}]


Here are some do's and don't's:
# DO's #
1. The output structure should exactly match the structure provided in the examples
2. If the comment is not suitable for classification or you do not know the answer, respond with "Unable to categorize". Do not provide any reason or explanation.
3. Ensure that your labeling is clear, concise, and reflects the most significant topics or categories found in the text.

# DON'Ts #
1. Do not respond with your own suggestions or recommendations or feedback.
2. Do not include any additional symbols like * in the output
3. Do not change the structure of the output. 

Here are the comments you need to categorize:
Texts: {combined_texts}
Response: 
"""


# ------------------------------------- Categorization Generation Prompts For FileUpload----------------------------------------------------------

CATEGORIZATION_BATCH_PROMPT_FILE_UPLOAD = """
# OBJECTIVE #
You are a skilled analyst tasked with classifying social media text.

#STEPS#
1. Identify and list the primary topic for given texts in one word or phrase.
2. If there are subtopics or secondary themes mentioned in the text, list them as well. If the text discusses multiple topics, provide a list of these topics and describe their relevance.
3. Consider the context and tone of the text to determine the most appropriate topics. Take into account keywords, phrases, or specific terms that relate to the topics.
4. If any notable entities (people, places, brands, products, etc.) are mentioned in the text that play a role in the topics, mention them and their associations.
5. Additionally, identify the subject and object of the text and provide the following categorization:
      A. Client specified Category:
      Map the primary topic identified in Step 1. above to one of the following categories:
      {categories}


#CONTEXT:#
Here is some context regarding the texts: 
Summary of the texts: {summary}


#OUTPUT STRUCTURE:#
The response must be in strict JSON format as an array of objects. Ensure that:
  1. The JSON is properly formatted and error-free.
  2. All objects include all specified keys, even if the value is empty or null.
  3. Strings are enclosed in double quotes ("), and arrays use proper brackets ([]).
  4. Values follow the correct data types (e.g., numbers for <record_number>, strings for <subject>).

Required JSON Structure:
[{{
  "record_number": <number>,
  "topic": "<string>",
  "sub_topics": ["<string>", "<string>", ...],
  "explanation": "<string>",
  "notable_entities": ["<string>", "<string>", ...],
  "subject": "<string>",
  "object": "<string>",
  "client_specified_category": "<string>"
}}]


#EXAMPLES:#
Here's are a few examples:

Example 1: 
Text: This video has literally lit up a light in my brain. So much stuff is making sense. I had so many unconnected dots.
Response: 
[{{
  "record_number": 1,
  "topic": "Understanding and Insight",
  "sub_topics": ["Cognitive Clarity", "Connection of Ideas"],
  "explanation": "The text expresses a feeling of newfound understanding and clarity, suggesting that the video has helped the author connect previously unconnected ideas.",
  "notable_entities": ["Video"],
  "subject": "This video",
  "object": "A light in my brain",
  "client_specified_category": "{example_category}"
}}]

Example 2:
Text: Yes!  Thanks 3b1b, this is really intuitive compared to most overly-jargon filled papers.
Response:
[{{
  "record_number": 2,
  "topic": "Educational Content Appreciation",
  "sub_topics": ["Intuitive Explanation", "Jargon-Free Language"],
  "explanation": "The comment expresses positive feedback on the video, highlighting its clarity and accessibility compared to other academic materials.",
  "notable_entities": ["3b1b", "Papers"],
  "subject": "3b1b",
  "object": "this (referring to the intuitive explanation)",
  "client_specified_category": "{example_category}"
}}]

Example 3:
Text: Any update on the next chapter coming out? :)
Response:
[{{
  "record_number": 3,
  "topic": "Video Release Schedule",
  "sub_topics": ["Next Chapter", "Release Date"],
  "explanation": "The comment inquires about the release date of the next chapter in the video series.",
  "notable_entities": ["Next Chapter"],
  "subject": "You (implied)",
  "object": "update",
  "client_specified_category": "{example_category}"
}}]   

Example 4:
Text: YT, why so many ads! :(
Response:
[{{
  "record_number": 4,
  "topic": "Ads on YouTube",
  "sub_topics": ["YT", "Advertisements"],
  "explanation": "The text expresses dissatisfaction with the YouTube experience due to the number of advertisements shown during the video.",
  "notable_entities": ["YT", "Ads"],
  "subject": "YT (YouTube)",
  "object": "ads",
  "client_specified_category": "{example_category}"
}}]      

Example 5:
Text: Love your content, but you may want to address an interesting issue in one of your examples - the whodunit novel where the last word is the identity of the murderer.  You aren't saying that the attention block can reason are you? It would only be successful if there was some text that said something like 'John was next to the victim when he died', that context could be parroted back, but deeper reasoning like the killer was left handed, female and spoke french and only one character had all of those attributes. Or are you?
Response:
[{{
  "record_number": 5,
  "topic": "Ability of transformers to reason",
  "sub_topics": ["Attention block", "Novel"],
  "explanation": "The text expresses appreciation towards the content as well as a doubt whether attention blocks have the ability to reason.",
  "notable_entities": ["Whodunit Novel"],
  "subject": "You",
  "object": "an interesting issue",
  "client_specified_category": "{example_category}"
}}]


Here are some do's and don't's:
# DO's #
1. The output structure should exactly match the structure provided in the examples
2. If the text is not suitable for classification or you do not know the answer, respond with "Unable to categorize". Do not provide any reason or explanation.
3. Ensure that your labeling is clear, concise, and reflects the most significant topics or categories found in the text.

# DON'Ts #
1. Do not respond with your own suggestions or recommendations or feedback.
2. Do not include any additional symbols like * in the output
3. Do not change the structure of the output. 

Here are the texts you need to categorize:
Texts: {combined_texts}
Response: 
"""

CLUSTER_NAME_PROMPT = """
You are given the following data points in a cluster: {cluster_data}. 
Analyse the semantic meaning of each data point and provide an accurate and intelligible cluster name for this cluster. 
Output should be a cluster name only. Do not include any unnecessary symbols or special characters like ## or **.
"""
