import json
import time
import base64
from bson import ObjectId
from consumer_scripts.Summarization.context import Cluster, Story, Theme
from consumer_scripts.Summarization.summary import get_map_reduce_chain
from utils.common_utils import get_query_config, trigger_execution_via_http
from utils.langchain_utils import LangChainUtility
from utils.logger import logger
from utils.mongo_db import get_mongodb_client, get_mongodb_collection, get_mongodb_db
from utils.pubsub_publisher import publish_pubsub_message
from utils.utilities import (
    EncapsulationMarkerStatus,
    MetaContainer,
    DiagnosticActionType,
    DiagnosticStatus,
    OrganizationAccountInfo,
    SummaryContext,
    VmConsumers,
)
from consumer_scripts.Summarization.helper import add_unique_ids
from consumer_scripts.Summarization.const import (
    ENCAPSULATION_MARKER_COLLECTION,
    PROJECT_ID,
    PROJECTION,
    QUERY_COLLECTION,
    SEQUENCE_COORDINATOR_TOPIC_ID,
    ORG_CREDENTIALS_COLLECTION,
    CONSUMER_TYPE_SUMMARY,
)


def trigger_summarization(payload):
    """
    This main function where execution start
    - Steps
      - Parse the payload and extract the query_id , organization_id , encapsulation_marker ,context
      - send the message to the diagnostic for pending status
      - Setup the database and collection object with Organization id and vault
      - check for the context and set context_ids
      - check if context exists
        - get the context data for an encapulation marker
        - create dask delay task with context data
      - Process the task with auto scheduler
      - else if the context is not set then add the Unique ids
      - check if the context was theme and if all the process are completed
      - if all the process id completed then send the message to capabilities sequence coordinator and toggle the diagnostic to completed
      - else set the context and loopback

    Parmeters:
    - cloud_event (CloudEvent): The CloudEvent object representing the incoming event.

    Returns:
    - Dict with status and message
        - status: 200 if success or 400 if any error occurs
        - message: message of success or error

    Exception:
     - If any error occurs during the summarization process it will log and send it diagnostic.

    """
    client = None
    try:
        # parse the payload and extract query_id and organization_id from payload
        logger.info("Message received successfully: %s", payload)

        meta_container = MetaContainer()
        lanchain_util = LangChainUtility()
        meta_container.set_payload_info(payload)

        query_id, organization_id, encapsulation_marker = (
            payload.get("query_id"),
            payload.get("organization_id"),
            payload.get("encapsulation_marker"),
        )

        message = f"Capabilities Summarization script started successfully for query_id: {query_id}"
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.PENDING.value,
            message,
            consumer_type=CONSUMER_TYPE_SUMMARY,
        )

        start_time = time.perf_counter()

        # Fetch organization account information
        org_info = OrganizationAccountInfo(organization_id)
        mongodb_connection_string = org_info.mongodb_url
        db_name = org_info.organization_db_name

        # Establish MongoDB connection
        client = get_mongodb_client(mongodb_connection_string)
        db = get_mongodb_db(client, db_name)

        query_collection = get_mongodb_collection(db, QUERY_COLLECTION)

        query_config = get_query_config(query_collection, query_id, PROJECTION)

        if query_config is None:
            message = f"Query not found : {payload}"
            logger.warning(message)
            return {"status": 404, "message": message}, 404

        meta_container.set_meta_data(query_config["meta_data"])

        pod_ip = meta_container.meta_data.get("POD_IP")

        rac_collection_name = meta_container.meta_data.get("RAC_COLLECTION_NAME")

        rac_collection = get_mongodb_collection(db, rac_collection_name)
        llm_config_collection = get_mongodb_collection(db, ORG_CREDENTIALS_COLLECTION)

        encapsulation_marker_collection = get_mongodb_collection(
            db, ENCAPSULATION_MARKER_COLLECTION
        )

        model_name = meta_container.meta_data.get("MODEL_NAME")
        lanchain_util.set_configuration(
            model_name, llm_config_collection, consumer_type=CONSUMER_TYPE_SUMMARY
        )

        # set the map reduce chain
        get_map_reduce_chain(lanchain_util)
        context = encapsulation_marker_collection.find_one(
            {
                "query_id": ObjectId(query_id),
                "encapsulation_marker": encapsulation_marker,
            }
        ).get("summarization_context")

        context_obj = None
        match context:
            case SummaryContext.CLUSTER.value:
                context_obj = Cluster(payload, context, lanchain_util)
            case SummaryContext.STORY.value:
                context_obj = Story(payload, context, lanchain_util)
            case SummaryContext.THEME.value:
                context_obj = Theme(payload, context, lanchain_util)
            case SummaryContext.ALL_PROCESSED.value:
                logger.info("All context processed for query_id %s", query_id)
            case _:
                logger.info("No context found the query_id %s", query_id)

        if context != SummaryContext.ALL_PROCESSED.value and context is not None:
            pipeline = context_obj.get_context_data()
            records = rac_collection.aggregate(pipeline)
            updated_fields = context_obj.process_data(records)
            if updated_fields:
                rac_collection.bulk_write(updated_fields, ordered=False)

            if updated_fields:
                rac_collection.bulk_write(list(updated_fields))

            context = context_obj.get_next_context(rac_collection)

        if context is None:
            encapsulation_marker_collection.update_one(
                {
                    "query_id": ObjectId(query_id),
                    "encapsulation_marker": encapsulation_marker,
                },
                {
                    "$set": {
                        "summarization_status": EncapsulationMarkerStatus.PENDING.value
                    }
                },
            )
            add_unique_ids(rac_collection, encapsulation_marker)
            context = SummaryContext.CLUSTER.value

        encapsulation_marker_collection.update_one(
            {
                "query_id": ObjectId(query_id),
                "encapsulation_marker": encapsulation_marker,
            },
            {"$set": {"summarization_context": context}},
        )

        is_all_processed = context == SummaryContext.ALL_PROCESSED.value

        if is_all_processed:
            payload["publisher_type"] = CONSUMER_TYPE_SUMMARY
            publish_pubsub_message(PROJECT_ID, SEQUENCE_COORDINATOR_TOPIC_ID, payload)
            encapsulation_marker_collection.update_one(
                {
                    "query_id": ObjectId(query_id),
                    "encapsulation_marker": encapsulation_marker,
                },
                {
                    "$set": {
                        "summarization_status": EncapsulationMarkerStatus.COMPLETED.value,
                        "summarization_context": context,
                    }
                },
            )
            is_all_encapsulation_markers_completed = (
                encapsulation_marker_collection.count_documents(
                    {
                        "query_id": ObjectId(query_id),
                        "summarization_status": {
                            "$ne": EncapsulationMarkerStatus.COMPLETED.value
                        },
                    }
                )
                == 0
            )
            if is_all_encapsulation_markers_completed:
                meta_container.send_diagnostic(
                    DiagnosticActionType.UPDATE.value,
                    DiagnosticStatus.COMPLETED.value,
                    message,
                    consumer_type=CONSUMER_TYPE_SUMMARY,
                )
        else:
            logger.info("Publishing message to summarization for loop back")
            # TODO: uncomment the comment below to use PubSub for message publishing and delete the triggering via http
            # publish_pubsub_message(PROJECT_ID, SUMMARIZATION_TOPIC_ID, payload)

            # Trigger summarization for loop back
            next_consumer = VmConsumers.SUMMARIZATION_VM.value
            trigger_execution_via_http(pod_ip, next_consumer, payload)

        end_time = time.perf_counter()
        total_time = end_time - start_time
        print(
            f"Time taken of the execution for encapsulation marker {encapsulation_marker} with context {context} : {total_time}"
        )
        return {"status": 200}

    except Exception as e:
        message = f"Error while process Capabilities Summarization: {e}"
        logger.exception(message)
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.FAILED.value,
            message,
            error=str(type(e)),
            consumer_type=CONSUMER_TYPE_SUMMARY,
        )
        return {"status": 400, "error_message": message}
