# Use the official Python image from the Docker Hub
FROM python:3.12-slim

# Set the working directory in the container
WORKDIR /app

# Install build tools and Python development headers
RUN apt-get update && apt-get install -y build-essential python3-dev \
    && rm -rf /var/lib/apt/lists/*

# Copy the requirements file into the container
COPY requirements.txt /app/requirements.txt

# Install the required packages
RUN pip install --no-cache-dir -r /app/requirements.txt

# Copy the rest of the application code
COPY . /app/

# Add a non-root user
RUN useradd -m appuser

# Switch to non-root user
USER appuser

# Download NLTK 'punkt' tokenizer data
RUN python3 -m nltk.downloader punkt_tab

# Set up Google Cloud authentication
ENV GOOGLE_APPLICATION_CREDENTIALS="/app/key.json"
ENV PYTHONUNBUFFERED=1

# Health check (ensure /health endpoint is implemented in your app)
HEALTHCHECK --interval=30s --timeout=5s \
    CMD curl --fail http://localhost:8080/health || exit 1

# Expose the port that the app runs on
EXPOSE 8080

# Command to run the application using Gunicorn
CMD ["gunicorn", "-w", "4", "-b", "0.0.0.0:8080", "--timeout", "3600", "app:app"]
