"""
This module processes and generates summaries for different contexts (Cluster, Story, Theme) 
in a MongoDB-based pipeline. It defines a base `Context` class with common logic for 
summarizing and updating MongoDB records, and subclasses (`Cluster`, `Story`, `Theme`) that 
handle specific contexts.
"""

import pandas as pd
import dask
from pymongo import UpdateMany
from pymongo.collection import Collection
from updated_summary.helper import clean_summary_text
from updated_summary.summary import generate_summary, generate_weighted_summary_text
from updated_summary.metrics import weight_evaluation_function, discard_function
from updated_summary.const import MAX_CLUSTER_IDS, MIN_RECORDS
from utils import logger
from utils.utilities import SummaryContext, NewSummaryProcessingField
from utils.llm_settings import SUMMARIES_TO_IGNORE


class Context:
    """Class to define context object"""

    def __init__(self, payload, context, lanchain_util):
        """This is constructor for the intialize of the context object

        Args:
            payload (dict): message recieved by the pubsub
            context (_type_): context of the summarization
            lanchain_util (_type_): langchain_util to handle langhchain operation
        """
        self.payload = payload
        self.encapsulation_marker = payload.get("encapsulation_marker")
        self.langchain_util = lanchain_util
        self.context = context
        self.summary_column = "new_" + self.context + "_summary"
        self._get_context_attr()

    def _get_context_attr(self):
        pass

    def process_context(self, texts, context_id):
        """
        Generic function to process cluster,story and theme summaries.

        Parameters:
        - texts : Aggeragate texts array of the context id
        - filter_doc : filter doc for the updating the records based on context
        - context : context value of the cluster,story or theme
        - context_id : context id is the cluster,story or theme id we want to process

        Returns:
        - UpdateMany object with processed combined summary
        """
        context_id_column = getattr(self, "context_id_column")

        filter_doc = {
            "encapsulation_marker": self.encapsulation_marker,
            context_id_column: context_id,
        }

        combined_summary = clean_summary_text(
            generate_summary(self.langchain_util, texts)
        )
        return UpdateMany(
            filter_doc,
            {
                "$set": {
                    self.summary_column: combined_summary,
                    self.processing_field: True,
                }
            },
        )

    def process_data(self, records):
        """This function process the data using dask delayed

        Args:
        - records : records containing context ids and texts

        Returns:
        - UpdateMany object with processed combined summary
        """
        records = list(records)
        tasks = [
            dask.delayed(self.process_context)(
                record["texts"],
                record["_id"],
            )
            for record in records
        ]
        updated_fields = dask.compute(*tasks)
        return list(updated_fields)

    def get_next_context(self, collection: Collection):
        """Get the next context after processing of the data

        Args:
            collection (Collection): RAC collection of the query

        Returns:
            context : return the context based on the checks
        """
        processing_field = getattr(self, "processing_field")
        count_query = {
            "encapsulation_marker": self.encapsulation_marker,
            processing_field: False,
        }
        num_records = collection.count_documents(count_query)
        next_context = getattr(self, "next_context")

        if num_records == 0:
            return next_context

        return self.context

    def pre_processing(self, docs):
        """
        Pre-processes a list of documents by calculating weights, filtering, and grouping.
        Steps:
            1. Converts the input documents into a pandas DataFrame.
            2. Computes a `weight` column as the proportion of `record_count` to 
                the total count within each group.
            3. Sorts the DataFrame in descending order of weights.
            4. Applies a text formatting function (`generate_weighted_summary_text`) 
                to generate a `formatted_text` column.
            5. Logs the length and records before and after filtering out rows with 
                `record_count` <= 10.
            6. Groups the DataFrame by `context` and aggregates the `formatted_text` 
                values into lists.
            7. Prepares the final output as a list of dictionaries with `_id` and `texts`.


        Args:
            docs (list): A list of dictionaries, where each dictionary represents a \
                document with at least the keys `context` and `record_count`.

        Returns:
            list: A list of dictionaries containing grouped results. Each dictionary has:
                - `_id`: The `context` representing the group.
                - `texts`: A list of weighted and formatted summary texts for the group.
        """
        # Convert the input documents into a DataFrame.
        df = pd.DataFrame(docs)

        weight_evaluation_function(self.context, df)

        # Sort records by weight in descending order.
        df.sort_values(by="weight", ascending=False, inplace=True)

        # Generate formatted text summaries.
        df["formatted_text"] = df.apply(generate_weighted_summary_text, axis=1)

        discard_function(df)

        # Group by `context` and aggregate formatted texts into lists.
        grouped_df = df.groupby(self.context).agg(texts=("formatted_text", list))

        # Add `_id` for the group key and prepare the final output.
        grouped_df["_id"] = grouped_df.index
        return grouped_df.loc[:, ["_id", "texts"]].to_dict(orient="records")


class Cluster(Context):
    """Class to define cluster context"""

    def _get_context_attr(self):
        self.processing_field = NewSummaryProcessingField.CLUSTER.value
        self.context_id_column = "Unique_Cluster_ID"
        self.next_context = SummaryContext.STORY.value

    def get_context_data(self):
        """To get context data for cluster using pipeline"""
        pipeline = [
            {
                "$match": {
                    "encapsulation_marker": self.encapsulation_marker,
                    NewSummaryProcessingField.CLUSTER.value: False,
                }
            },
            {
                "$group": {
                    "_id": "$Unique_Cluster_ID",
                    "texts": {"$addToSet": "$BODY1"},
                }
            },
            {"$limit": MAX_CLUSTER_IDS},
        ]
        return pipeline


class Story(Context):
    """Class to define cluster context"""

    def _get_context_attr(self):
        """To get story attributes"""
        self.processing_field = NewSummaryProcessingField.STORY.value
        self.context_id_column = "Unique_Story_ID"
        self.next_context = SummaryContext.THEME.value

    def get_context_data(self):
        """To get context data for Story using pipeline"""
        pipeline = [
            {
                "$match": {
                    "encapsulation_marker": self.encapsulation_marker,
                    NewSummaryProcessingField.STORY.value: False,
                    "cluster_summary": {"$nin": SUMMARIES_TO_IGNORE},
                }
            },
            {
                "$group": {
                    "_id": "$Unique_Story_ID",
                    "texts": {"$addToSet": "$cluster_summary"},
                }
            },
        ]
        return pipeline

    def get_context(self):
        """To get story context"""
        pipeline = [
            {
                "$match": {
                    "encapsulation_marker": self.encapsulation_marker,
                    NewSummaryProcessingField.STORY.value: False,
                    "new_cluster_summary": {"$nin": SUMMARIES_TO_IGNORE},
                }
            },
            {
                "$group": {
                    "_id": {
                        "cluster": "$Unique_Cluster_ID",
                    },
                    "story": {"$first": "$Unique_Story_ID"},
                    "summary": {"$first": "$new_cluster_summary"},
                    "record_count": {"$sum": 1},
                }
            },
        ]
        return pipeline

    def pre_processing(self, docs):
        """
        Pre-processes a list of documents by calculating weights, filtering, and grouping.
        Steps:
            1. Converts the input documents into a pandas DataFrame.
            2. Computes a `weight` column as the proportion of `record_count` to the
                total count within each group.
            3. Sorts the DataFrame in descending order of weights.
            4. Applies a text formatting function (`generate_weighted_summary_text`)
                to generate a `formatted_text` column.
            5. Logs the length and records before and after filtering out rows with
                `record_count` <= 10.
            6. Groups the DataFrame by `story` and aggregates the `formatted_text`
                values into lists.
            7. Prepares the final output as a list of dictionaries with `_id` and `texts`.


        Args:
            docs (list): A list of dictionaries, where each dictionary represents a
                document with at least the keys `story` and `record_count`.

        Returns:
            list: A list of dictionaries containing grouped results. Each dictionary has:
                - `_id`: The `story` representing the group.
                - `texts`: A list of weighted and formatted summary texts for the group.
        """
        # Convert the input documents into a DataFrame.
        df = pd.DataFrame(docs)

        # Calculate total record counts for each group and derive the weight for each record.
        df["story"] = df["_id"].apply(lambda x: x["story"])
        total_counts = df.groupby("story")["record_count"].transform("sum")
        df["weight"] = df["record_count"] / total_counts

        # Sort records by weight in descending order.
        df.sort_values(by="weight", ascending=False, inplace=True)

        # Generate formatted text summaries.
        df["formatted_text"] = df.apply(generate_weighted_summary_text, axis=1)

        # Log data before filtering.
        logger.info("length before %s", len(df))
        logger.info("record before %s", df)

        # Filter out records with `record_count` <= 10.
        df = df[df["record_count"] > MIN_RECORDS]

        # Log data after filtering.
        logger.info("length after %s", len(df))
        logger.info("record after %s", df)

        # Group by `story` and aggregate formatted texts into lists.
        grouped_df = df.groupby("story").agg(texts=("formatted_text", list))

        # Add `_id` for the group key and prepare the final output.
        grouped_df["_id"] = grouped_df.index
        return grouped_df.loc[:, ["_id", "texts"]].to_dict(orient="records")


class Theme(Context):
    """Class to define Theme context"""

    def _get_context_attr(self):
        self.processing_field = NewSummaryProcessingField.THEME.value
        self.context_id_column = "Themes"
        self.next_context = SummaryContext.ALL_PROCESSED.value

    def get_context_data(self):
        """To get context data for Theme using pipeline"""
        pipeline = [
            {
                "$match": {
                    "encapsulation_marker": self.encapsulation_marker,
                    NewSummaryProcessingField.THEME.value: False,
                    "story_summary": {"$nin": SUMMARIES_TO_IGNORE},
                }
            },
            {
                "$group": {
                    "_id": "$Themes",
                    "texts": {"$addToSet": "$story_summary"},
                }
            },
        ]
        return pipeline

    def get_context(self):
        """To get theme context"""
        pipeline = [
            {
                "$match": {
                    "encapsulation_marker": self.encapsulation_marker,
                    NewSummaryProcessingField.THEME.value: False,
                    "new_story_summary": {"$nin": SUMMARIES_TO_IGNORE},
                }
            },
            {
                "$group": {
                    "_id": {
                        "story": "$Unique_Story_ID",
                    },
                    "theme": {"$first": "$Themes"},
                    "summary": {"$first": "$new_story_summary"},
                    "record_count": {"$sum": 1},
                }
            },
        ]
        return pipeline
