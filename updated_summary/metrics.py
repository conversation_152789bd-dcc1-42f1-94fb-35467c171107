"""
This module contains functions for evaluating and filtering records in 
a DataFrame based on their record count and context-specific weights.
"""

import pandas as pd
from updated_summary.const import MIN_RECORDS
from utils.logger import logger


def weight_evaluation_function(context: str, df: pd.DataFrame) -> None:
    """
    This function calculates the total record count for each group defined
    by the `context` column and assigns a weight to each record as the
    proportion of its individual record count over the total count
    for its respective group.

    Parameters:
    - context (str): The column name in the DataFrame used to group the data.
    - df (pandas.DataFrame): The DataFrame containing the records to be evaluated.

    Returns:
    - None: The function updates the `df` DataFrame in place by adding a new `weight` column.
    """
    # Calculate total record counts for each group and derive the weight for each record.
    total_counts = df.groupby(context)["record_count"].transform("sum")
    df["weight"] = df["record_count"] / total_counts


def discard_function(df: pd.DataFrame) -> None:
    """
    Filters out records with `record_count` less than or equal to the predefined
    minimum threshold (`MIN_RECORDS`).

    Before and after filtering, the function logs the length and contents of
    the DataFrame for tracking purposes.

    Parameters:
    - df (pandas.DataFrame): The DataFrame to be filtered.
    """

    # Log data before filtering.
    logger.info("length before %s", len(df))
    logger.info("record before %s", df)

    # Filter out records with `record_count` <= 10.
    df = df[df["record_count"] > MIN_RECORDS]

    # Log data after filtering.
    logger.info("length after %s", len(df))
    logger.info("record after %s", df)
