"""
This module provides functions for generating summaries using a Langchain 
utility and an LLM (Large Language Model) with different batching strategies.
"""

from google.api_core import exceptions
from const import MAP_TEMPLATE, REDUCE_TEMPLATE, DEFAULT_SUMMARY, GARBAGE_SUMMARY
from utils.logger import logger
from langchain.chains import MapReduceDocumentsChain, ReduceDocumentsChain
from langchain.chains.combine_documents.stuff import StuffDocumentsChain
from langchain.schema import Document
from langchain_core.prompts import PromptTemplate
from langchain.chains.llm import LL<PERSON>hain


def get_map_reduce_chain(lanchain_util):
    """
    Attempts to generate a summary, optionally using character-based batching.

    Parameters:
    - langchain_util: Langchain utility object for LLM model and token limits
    - docs: Array of texts to be summarized
    - use_character_split: If True, use character-based batching instead of token-based batching.

    Returns:
    - summary: Generated summary using LLM model
    """
    # Prepare map and reduce chains
    map_prompt = PromptTemplate.from_template(MAP_TEMPLATE)
    map_chain = LLMChain(llm=lanchain_util.llm_model, prompt=map_prompt)

    reduce_prompt = PromptTemplate.from_template(REDUCE_TEMPLATE)
    reduce_chain = LLMChain(llm=lanchain_util.llm_model, prompt=reduce_prompt)

    combine_documents_chain = StuffDocumentsChain(
        llm_chain=reduce_chain, document_variable_name="docs"
    )

    reduce_documents_chain = ReduceDocumentsChain(
        combine_documents_chain=combine_documents_chain,
        collapse_documents_chain=combine_documents_chain,
        token_max=lanchain_util.max_token_count,
    )

    map_reduce_chain = MapReduceDocumentsChain(
        llm_chain=map_chain,
        reduce_documents_chain=reduce_documents_chain,
        document_variable_name="docs",
        return_intermediate_steps=False,
    )
    setattr(lanchain_util, "chain", map_reduce_chain)


def generate_summary(lanchain_util, docs):
    """
    Generates the summaries of summaries using langchain with map reduce method

    Parameters:
    - lanchain_util: langchain util object to get the llm_model with fallback and max token
    - docs: array of texts to be summarized

    Returns:
    - summary: Generated summary with llm model

    Exception Handling:
    - Log the error message and return the default summary
    """
    summary = DEFAULT_SUMMARY
    try:
        # Attempt to generate summary with default token-based batching
        summary = get_summary(lanchain_util, docs, use_character_split=False)
    except exceptions.InternalServerError as e:
        message = f"InternalServerError: {e}. Retrying with character-based batching..."
        logger.warning(message)
        # Retry using character-based batching
        summary = get_summary(lanchain_util, docs, use_character_split=True)
    except Exception as e:
        message = f"Could not generate summary due to some error:{e}"
        logger.error(message)
        raise

    if GARBAGE_SUMMARY in summary or not summary:
        summary = DEFAULT_SUMMARY

    return summary


def get_summary(lanchain_util, docs, use_character_split=False):
    """
    Attempts to generate a summary, optionally using character-based batching.

    Parameters:
    - lanchain_util: Langchain utility object for LLM model and token limits
    - docs: Array of texts to be summarized
    - use_character_split: If True, use character-based batching instead of token-based batching.

    Returns:
    - summary: Generated summary using LLM model
    """

    # Split documents into batches based on token count or character count
    if use_character_split:
        batches = lanchain_util.split_with_characters(docs)
    else:
        batches = lanchain_util.split_with_tokens(docs)

    split_docs = [Document("\n".join(batch)) for batch in batches]

    # Generate the summary with map-reduce chain
    summary = lanchain_util.invoke_chain(split_docs)["output_text"]

    return summary
