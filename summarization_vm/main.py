"""
This module listens to a Google Cloud Pub/Sub subscription and 
triggers summarization tasks based on received messages.
"""

import json
from concurrent.futures import TimeoutError
from google.cloud import pubsub_v1
from trigger import trigger_summarization
from summarization_vm.const import PROJECT_ID, SUBSCRIPTION_ID
from utils.logger import logger


subscriber = pubsub_v1.SubscriberClient()
subscription_path = subscriber.subscription_path(PROJECT_ID, SUBSCRIPTION_ID)


def callback(message: pubsub_v1.subscriber.message.Message) -> None:
    """
    Callback function to process incoming Pub/Sub messages.

    This function is triggered when a message is received from the Pub/Sub subscription.
    It decodes the message, parses the payload, logs the message details,
    and triggers the summarization process.

    Parameters:
    - message (pubsub_v1.subscriber.message.Message): The Pub/Sub message containing
    the data to be processed.

    Returns:
    - None: This function does not return any value. It processes the message
        asynchronously.

    Side Effects:
    - Logs message details (e.g., message data, delivery attempts).
    - Acknowledges the message after processing.
    - Calls `trigger_summarization` to initiate the summarization process
        based on the parsed payload.
    """

    logger.info("Received data of the message %s", message.data)
    logger.info("With delivery attempts: %s.", message.delivery_attempt)

    decoded = message.data.decode("utf-8")
    payload = json.loads(decoded)
    message.ack()
    trigger_summarization(payload)


streaming_pull_future = subscriber.subscribe(subscription_path, callback=callback)
logger.info("Listening for messages on %s..\n", subscription_path)

# Wrap subscriber in a 'with' block to automatically call close() when done.
with subscriber:
    # When `timeout` is not set, result() will block indefinitely,
    # unless an exception is encountered first.
    try:
        streaming_pull_future.result()

    except TimeoutError:
        streaming_pull_future.cancel()  # Trigger the shutdown.
        streaming_pull_future.result()  # Block until the shutdown is complete.
