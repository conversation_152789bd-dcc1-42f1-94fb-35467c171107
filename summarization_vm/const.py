"""
This module contains constants used in files of summarization consumer.
"""

import os

PROJECTION = {"source.data_source_name": 1, "meta_data": 1}
QUERY_COLLECTION = os.environ.get("QUERY_COLLECTION")

PROJECT_ID = os.getenv("PROJECT_ID")
SEQUENCE_COORDINATOR_TOPIC_ID = os.getenv("SEQUENCE_COORDINATOR_TOPIC_ID")
SUBSCRIPTION_ID = os.getenv("SUBSCRIPTION_ID")
SUMMARIZATION_TOPIC_ID = os.getenv("SUMMARIZATION_TOPIC_ID")
ENCAPSULATION_MARKER_COLLECTION_NAME_METADATA_KEY = (
    "ENCAPSULATION_MARKER_COLLECTION_NAME"
)

DEFAULT_SUMMARY = "No summary generated"
GARBAGE_SUMMARY = "Please provide the text"

ORG_CREDENTIALS_COLLECTION = os.getenv("ORG_CREDENTIALS_COLLECTION")

# get api key here - https://aistudio.google.com/app/apikey
GENAI_API_KEY = os.getenv("GENAI_API_KEY")

CALL_LIMIT = os.getenv("CALL_LIMIT")

MAX_CLUSTER_IDS = 100

MAX_TOKEN_COUNT = 1_000_000

# ------------------------------------- Summarization Prompts -----------------------------------------------------------------
# prompt 1 for summarization chain
# Map
MAP_TEMPLATE = """
Carefully read the provided text. Create a short concise summary in a coherent 
paragraph of 100 words or less, focusing on the most significant details. 
Provide the summary without a title. Ensure the summary captures critical 
details, maintaining the original context and logical flow.
TEXT: ```{docs}```

SUMMARY: """

# Reduce
REDUCE_TEMPLATE = """
Carefully read the provided text. Create a short concise summary in a coherent 
paragraph of 100 words or less, focusing on the most significant details. 
Provide the summary without a title. Ensure the summary captures critical 
details, maintaining the original context and logical flow.
TEXT: ```{docs}```

SUMMARY: """
