"""
This module handles the processing of summaries for different contexts 
(Cluster, Story, Theme) within a MongoDB pipeline and integrates with 
the summarization and diagnostic systems."""

import time
from bson import ObjectId
import dask
from pymongo.operations import UpdateMany
from pymongo.collection import Collection
from pymongo.cursor import Cursor
from summary import generate_summary, get_map_reduce_chain
from summarization_vm.helper import add_unique_ids, clean_summary_text
from summarization_vm.const import (
    ENCAPSULATION_MARKER_COLLECTION_NAME_METADATA_KEY,
    MAX_CLUSTER_IDS,
    PROJECT_ID,
    PROJECTION,
    QUERY_COLLECTION,
    SEQUENCE_COORDINATOR_TOPIC_ID,
    SUMMARIZATION_TOPIC_ID,
    ORG_CREDENTIALS_COLLECTION,
    DEFAULT_SUMMARY,
)
from utils.common_utils import get_query_config
from utils.langchain_utils import LangChainUtility
from utils.logger import logger
from utils.mongo_db import get_mongodb_client, get_mongodb_collection, get_mongodb_db
from utils.pubsub_publisher import publish_pubsub_message
from utils.utilities import (
    CONSUMER_TYPE,
    EncapsulationMarkerStatus,
    MetaContainer,
    DiagnosticActionType,
    DiagnosticStatus,
    OrganizationAccountInfo,
    SummaryContext,
)
from utils.llm_settings import SUMMARIES_TO_IGNORE


def get_context_data(
    rac_collection: Collection,
    encapsulation_marker: str,
    context: str,
    context_ids: list,
) -> tuple[Cursor, dict, str]:
    """
    Retrieves context-specific data from a MongoDB collection based on the provided
    encapsulation marker, context, and context IDs.

    Parameters:
    - rac_collection (Collection): The MongoDB collection to query.
    - encapsulation_marker (str): A marker used to filter documents in the collection.
    - context (str): The context type (Cluster, Story, Theme) that determines the
        aggregation pipeline.
    - context_ids (list): A list of context IDs used to filter the relevant documents.

    Returns:
    - tuple: A tuple containing:
    1. records (Cursor): The MongoDB aggregation result based on the pipeline.
    2. filter_doc (dict): The filter document used to query the collection.
    3. summary_column (str): The name of the summary field corresponding to the context.
    """

    filter_doc = {
        "encapsulation_marker": encapsulation_marker,
    }
    pipeline = []
    summary_column = None

    match context:
        case SummaryContext.CLUSTER.value:
            pipeline.append(
                {
                    "$match": {
                        "encapsulation_marker": encapsulation_marker,
                        "Unique_Cluster_ID": {"$in": context_ids},
                    }
                }
            )
            pipeline.append(
                {
                    "$group": {
                        "_id": "$Unique_Cluster_ID",
                        "texts": {"$addToSet": "$BODY1"},
                    }
                }
            )
            summary_column = "cluster_summary"

        case SummaryContext.STORY.value:
            pipeline.append(
                {
                    "$match": {
                        "encapsulation_marker": encapsulation_marker,
                        "cluster_summary": {"$nin": SUMMARIES_TO_IGNORE},
                    }
                }
            )
            pipeline.append(
                {
                    "$group": {
                        "_id": "$Unique_Story_ID",
                        "texts": {"$addToSet": "$cluster_summary"},
                    }
                }
            )
            summary_column = "story_summary"

        case SummaryContext.THEME.value:
            pipeline.append(
                {
                    "$match": {
                        "encapsulation_marker": encapsulation_marker,
                        "story_summary": {"$nin": SUMMARIES_TO_IGNORE},
                    }
                }
            )
            pipeline.append(
                {
                    "$group": {
                        "_id": "$Themes",
                        "texts": {"$addToSet": "$story_summary"},
                    }
                }
            )
            summary_column = "theme_summary"
    records = rac_collection.aggregate(pipeline)

    return records, filter_doc, summary_column


def process_context(texts, filter_doc, context, context_id, summary_column):
    """
    Generic function to process cluster,story and theme summaries.

    Parameters:
    - rac_collection : RAC collection of the query
    - context : context value of the cluster,story or theme
    - encapsulation_marker : encapsulation marker for the collection
    - context_id : context id is the cluster,story or theme id we want to process

    Returns:
    - Processed comined summary
    """
    lanchain_util = LangChainUtility()

    match context:
        case SummaryContext.CLUSTER.value:
            filter_doc["Unique_Cluster_ID"] = context_id
        case SummaryContext.STORY.value:
            filter_doc["Unique_Story_ID"] = context_id
        case SummaryContext.THEME.value:
            filter_doc["Themes"] = context_id

    combined_summary = clean_summary_text(generate_summary(lanchain_util, texts))

    return UpdateMany(filter_doc, {"$set": {summary_column: combined_summary}})


def trigger_summarization(payload):
    """
    This main function where execution start
    - Steps
      - Parse the payload and extract the query_id , organization_id , encapsulation_marker ,context
      - send the message to the diagnostic for pending status
      - Setup the database and collection object with Organization id and vault
      - check for the context and set the function , parameters and context_ids
      - check if context exists and create dask delay task
      - Process the task with auto scheduler
      - else if the context is not set then add the Unique ids
      - check if the context was theme and if all the process are completed
      - if all the process id completed then send the message to capabilities sequence
        coordinator and toggle the diagnostic to completed
      - else set the context and loopback

    Parmeters:
    - cloud_event (CloudEvent): The CloudEvent object representing the incoming event.

    Returns:
    - Dict with status and message
        - status: 200 if success or 400 if any error occurs
        - message: message of success or error

    Exception:
     - If any error occurs during the summarization process it will log and send it diagnostic.

    """
    mongodb_client = None
    try:
        meta_container = MetaContainer()
        lanchain_util = LangChainUtility()

        # parse the payload and extract query_id and organization_id from payload
        payload["publisher_type"] = CONSUMER_TYPE

        logger.info("Message received successfully: %s", payload)

        meta_container.set_payload_info(payload)

        query_id, organization_id, encapsulation_marker, context = (
            payload.get("query_id"),
            payload.get("organization_id"),
            payload.get("encapsulation_marker"),
            payload.get("context"),
        )

        message = f"Capabilities Summarization script started successfully for query_id: {query_id}"
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.PENDING.value,
            message,
        )

        start_time = time.perf_counter()

        # Fetch organization account information
        org_info = OrganizationAccountInfo(organization_id)
        mongodb_connection_string = org_info.mongodb_url
        db_name = org_info.organization_db_name

        # Establish MongoDB connection
        mongodb_client = get_mongodb_client(mongodb_connection_string)
        db = get_mongodb_db(mongodb_client, db_name)

        query_collection = get_mongodb_collection(db, QUERY_COLLECTION)

        query_config = get_query_config(query_collection, query_id, PROJECTION)

        if query_config is not None:
            meta_container.set_meta_data(query_config["meta_data"])
            rac_collection_name = meta_container.meta_data["RAC_COLLECTION_NAME"]
            encapsulation_marker_collection_name = meta_container.meta_data[
                ENCAPSULATION_MARKER_COLLECTION_NAME_METADATA_KEY
            ]

            rac_collection = get_mongodb_collection(db, rac_collection_name)

            llm_config_collection = get_mongodb_collection(
                db, ORG_CREDENTIALS_COLLECTION
            )

            encapsulation_marker_collection = get_mongodb_collection(
                db, encapsulation_marker_collection_name
            )
            model_name = meta_container.meta_data["MODEL_NAME"]
            lanchain_util.set_configuration(model_name, llm_config_collection)

            # set the map reduce chain
            get_map_reduce_chain(lanchain_util)

            is_all_processed = False
            context_ids = None

            match context:
                case SummaryContext.CLUSTER.value:
                    context_ids = payload["cluster_ids"][:MAX_CLUSTER_IDS]
                case SummaryContext.STORY.value:
                    context_ids = payload["story_ids"]
                case SummaryContext.THEME.value:
                    context_ids = payload["theme_ids"]

            if "context" in payload:
                records, filter_doc, summary_column = get_context_data(
                    rac_collection, encapsulation_marker, context, context_ids
                )

                tasks = [
                    dask.delayed(process_context)(
                        record["texts"],
                        filter_doc.copy(),
                        context,
                        record["_id"],
                        summary_column,
                    )
                    for record in records
                ]

                updated_fields = dask.compute(*tasks)

                if updated_fields:
                    rac_collection.bulk_write(list(updated_fields))

                if context == SummaryContext.CLUSTER.value:
                    if len(payload["cluster_ids"]) <= MAX_CLUSTER_IDS:
                        context = SummaryContext.STORY.value
                    else:
                        payload["cluster_ids"] = payload["cluster_ids"][
                            MAX_CLUSTER_IDS:
                        ]

                elif context == SummaryContext.STORY.value:
                    rac_collection.update_many(
                        {
                            "encapsulation_marker": encapsulation_marker,
                            summary_column: {"$exists": False},
                        },
                        {"$set": {summary_column: DEFAULT_SUMMARY}},
                    )
                    context = SummaryContext.THEME.value

                elif context == SummaryContext.THEME.value:
                    rac_collection.update_many(
                        {
                            "encapsulation_marker": encapsulation_marker,
                            summary_column: {"$exists": False},
                        },
                        {"$set": {summary_column: DEFAULT_SUMMARY}},
                    )
                    is_all_processed = True

            else:
                encapsulation_marker_collection.update_one(
                    {
                        "query_id": ObjectId(query_id),
                        "encapsulation_marker": encapsulation_marker,
                    },
                    {
                        "$set": {
                            "summarization_status": EncapsulationMarkerStatus.PENDING.value
                        }
                    },
                )

                add_unique_ids(rac_collection, encapsulation_marker)

                pipeline = [
                    {"$match": {"encapsulation_marker": encapsulation_marker}},
                    {
                        "$group": {
                            "_id": None,
                            "cluster_ids": {"$addToSet": "$Unique_Cluster_ID"},
                            "story_ids": {"$addToSet": "$Unique_Story_ID"},
                            "theme_ids": {"$addToSet": "$Themes"},
                        }
                    },
                ]

                record = rac_collection.aggregate(pipeline).next()
                payload["cluster_ids"], payload["story_ids"], payload["theme_ids"] = (
                    record["cluster_ids"],
                    record["story_ids"],
                    record["theme_ids"],
                )
                context = SummaryContext.CLUSTER.value

            if is_all_processed:
                # print("Publishing to sequence coordinator: ",payload)
                publish_pubsub_message(
                    PROJECT_ID, SEQUENCE_COORDINATOR_TOPIC_ID, payload
                )
                encapsulation_marker_collection.update_one(
                    {
                        "query_id": ObjectId(query_id),
                        "encapsulation_marker": encapsulation_marker,
                    },
                    {
                        "$set": {
                            "summarization_status": EncapsulationMarkerStatus.COMPLETED.value
                        }
                    },
                )

                is_all_encapsulation_markers_completed = (
                    encapsulation_marker_collection.count_documents(
                        {
                            "query_id": ObjectId(query_id),
                            "summarization_status": {
                                "$ne": EncapsulationMarkerStatus.COMPLETED.value
                            },
                        }
                    )
                    == 0
                )

                if is_all_encapsulation_markers_completed:
                    meta_container.send_diagnostic(
                        DiagnosticActionType.UPDATE.value,
                        DiagnosticStatus.COMPLETED.value,
                        message,
                    )

            else:
                payload["context"] = context
                logger.info("Publishing message to summarization for loop back")
                publish_pubsub_message(PROJECT_ID, SUMMARIZATION_TOPIC_ID, payload)

            end_time = time.perf_counter()
            total_time = end_time - start_time
            print(
                f"Time taken of the execution for encapsulation marker {encapsulation_marker} with context {context} : {total_time}"
            )
        return {"status": 200}

    except Exception as e:
        message = f"Error while process Capabilities Summarization: {e}"
        logger.error(message, exc_info=True)
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.FAILED.value,
            message,
            str(type(e)),
        )
        return {"status": 400, "error_message": message}

    finally:
        if mongodb_client is not None:
            mongodb_client.close()
