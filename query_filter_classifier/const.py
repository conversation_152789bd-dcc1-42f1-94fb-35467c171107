"""
This module contains constants used in files of file_upload consumer.
"""

import os

MAX_SIZE = 100 * 1024 * 1024
FILE_EXTENSIONS = [".csv", ".txt", ".json", ".xlsx", ".html", "xml", "yaml"]
DELIMETERS = [",", "\t", "|", " ", ";"]

RAC_VOLUME_METADATA_KEY = os.getenv("RAC_VOLUME_METADATA_KEY")
PROJECT_ID = os.getenv("PROJECT_ID")
RAC_COLLECTION_NAME_METADATA_KEY = "RAC_COLLECTION_NAME"
BASE_LOOPBACK_THRESHOLD = os.getenv("BASE_LOOPBACK_THRESHOLD")
CHUNK_SIZE = os.getenv("CHUNK_SIZE")
QUERY_COLLECTION_NAME = os.getenv("QUERY_COLLECTION_NAME")
QUERY_FILTER_CLASSIFIER_METADATA_KEY = os.getenv("QUERY_FILTER_CLASSIFIER_METADATA_KEY")
QUERY_FILTER_COLLECTION_NAME = os.getenv("QUERY_FILTER_COLLECTION_NAME") 
QUERY_FILTER_CLASSIFIER_TOPIC_ID = os.getenv("QUERY_FILTER_CLASSIFIER_TOPIC_ID")
SEQUENCE_COORDINATOR_TOPIC_ID = os.getenv("SEQUENCE_COORDINATOR_TOPIC_ID")
