"""
This module defines the main function that orchestrates the similarity index 
creation and updates the associated statuses across the system.
"""

import json
from concurrent.futures import TimeoutError
from google.cloud import pubsub_v1
from similarity_vm.similarity import trigger_similarity
from similarity_vm.const import PROJECT_ID, SUBSCRIPTION_ID
from utils.logger import logger


subscriber = pubsub_v1.SubscriberClient()
subscription_path = subscriber.subscription_path(PROJECT_ID, SUBSCRIPTION_ID)


def callback(message: pubsub_v1.subscriber.message.Message) -> None:
    """
    Callback function to process incoming messages from a Pub/Sub subscription.

    Paramaters:
        message (pubsub_v1.subscriber.message.Message): The message received from
        the Pub/Sub subscription, which contains the data and metadata about the
        message.

    Returns:
        None: This function does not return any value.
    """
    logger.info("Received data of the message %s", message.data)
    logger.info("With delivery attempts: %s.", message.delivery_attempt)

    decoded = message.data.decode("utf-8")
    payload = json.loads(decoded)
    message.ack()
    trigger_similarity(payload)


streaming_pull_future = subscriber.subscribe(subscription_path, callback=callback)
logger.info("Listening for messages on %s..\n", subscription_path)

# Wrap subscriber in a 'with' block to automatically call close() when done.
with subscriber:
    # When `timeout` is not set, result() will block indefinitely,
    # unless an exception is encountered first.
    try:
        streaming_pull_future.result()

    except TimeoutError:
        streaming_pull_future.cancel()  # Trigger the shutdown.
        streaming_pull_future.result()  # Block until the shutdown is complete.
