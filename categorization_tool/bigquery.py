"""
Module for managing and processing data between MongoDB and BigQuery.

This module contains functions for handling data operations, including deduplication, 
typecasting, and updating BigQuery tables. It is designed to fetch, transform, and 
insert data into BigQuery while ensuring data consistency, including managing offsets, 
handling duplicates, and updating table schemas.
"""

from typing import Dict, List
import pandas as pd
from pandas import DataFrame
from pymongo.collection import Collection
from google.cloud import bigquery
from google.cloud.exceptions import NotFound
from pandas_gbq import to_gbq, read_gbq
from pandas_gbq.exceptions import GenericGBQException
from categorization_tool.const import (
    CATEGORIZATION_BQ_CHUNK_SIZE,
    CATEGORIZATION_BQ_OFFSET_METADATA_KEY,
    RAC_TRANSFORM_COLLECTION_NAME_METADATA_KEY,
    RAC_VOLUME_METADATA_KEY,
    TEMP_TABLE_SUFFIX,
    PROJECT_ID,
    get_categorization_columns,
)
from utils.utilities import (
    <PERSON>a<PERSON><PERSON><PERSON>,
    SummaryContext,
)
from utils.logger import logger
from utils.common_utils import (
    fetch_collection_data,
    update_mongodb_collection,
    update_query_metadata,
)


def check_table_exists(client: bigquery.Client, table_id: str) -> bool:
    """
    Checks if a table exists in BigQuery given its fully qualified table ID.

    Parameters:
    - client (bigquery.Client): An instance of the BigQuery client.
    - table_id (str): The fully qualified table ID in the format "project_id.dataset_id.table_id".

    Returns:
    - bool: True if the table exists, False otherwise.

    Exception Handling:
    - Not Found: Return False if the table does not exist.
    """
    try:
        # Try to fetch the table metadata using the full table ID
        client.get_table(table_id)
        return True  # Table exists

    except NotFound:
        return False  # Table does not exist


def deduplicate_data(table_id: str) -> None:
    """
    Deduplicate a BigQuery table by removing duplicates based on the `_id` field.

    Parameters:
    - table_id (str): The BigQuery table ID in the format 'project_id.dataset_id.table_id'.

    Returns:
    - None

    Exception Handling:
    - None
    """
    # Split the table_id into project_id, dataset_id, and table_name
    project_id, dataset_id, table_name = table_id.split(".")

    # Query to get the table schema
    schema_query = f"SELECT * FROM `{project_id}.{dataset_id}.INFORMATION_SCHEMA.COLUMNS` WHERE table_name = '{table_name}'"

    # Fetch the table schema
    schema_df = read_gbq(schema_query)

    # Prepare the deduplication query dynamically based on the columns
    select_columns = []
    for column in schema_df["column_name"]:
        if column != "_id":  # Skip the _id column, as it will be in GROUP BY
            select_columns.append(
                f"(ARRAY_AGG({column} LIMIT 1))[OFFSET(0)] AS {column}"
            )

    # Build the complete query string
    select_columns_str = ",\n  ".join(select_columns)
    deduplication_query = f"""
    CREATE OR REPLACE TABLE `{table_id}` AS
    SELECT
      _id,
      {select_columns_str}
    FROM
      `{table_id}`
    GROUP BY
      _id;
    """

    # Execute the deduplication query
    read_gbq(deduplication_query)
    logger.info("Table `%s` has been deduplicated successfully.", table_name)


def filter_duplicates(df: pd.DataFrame, table_id: str, query_id: str) -> pd.DataFrame:
    """
    Checks if any '_id' values from the input DataFrame already exist in the BigQuery table
    and returns a filtered DataFrame without duplicates.

    Parameters:
    - df (pd.DataFrame): The DataFrame containing the records.
    - table_id (str): The BigQuery table ID in the format 'project_id.dataset_id.table_id'.
    - query_id (str): The string representation of the query_id.

    Returns:
    - pd.DataFrame: The filtered DataFrame with non-duplicate records.

    Exception Handling:
    - 404 Not Found: Logs a warning and returns the original DataFrame
        if the table does not exist in BigQuery.
    - Exception: Raised if any error occurs while filtering duplicates.
    """
    try:
        # Extract IDs to check
        ids_to_check = df["_id"].tolist()
        ids_str = ",".join(map(repr, ids_to_check))
        # If there are no IDs to check, return the original DataFrame
        if not ids_to_check:
            return df

        # Build the query to find existing _id values in BigQuery
        query = f"""
        SELECT _id
        FROM `{table_id}`
        WHERE _id IN ({ids_str})
        """
        # Fetch the existing IDs from BigQuery
        result = read_gbq(query)
        existing_ids = set(result["_id"].astype(str))  # Convert IDs to string

        # Filter the DataFrame to remove duplicates
        filtered_df = df[~df["_id"].astype(str).isin(existing_ids)]

        # Log duplicate information if found
        duplicate_count = len(existing_ids)
        if duplicate_count > 0:
            logger.warning(
                "Skipping %d duplicate ID(s) found in the '%s' BigQuery table for query ID '%s'.",
                duplicate_count,
                table_id,
                query_id,
            )

        return filtered_df

    except GenericGBQException as e:
        # Check if exception message contains 404 error information
        if "Not Found" in str(e) or "404" in str(e):
            logger.warning(
                "BigQuery Table `%s` does not exist for query ID '%s', Returning original DataFrame.",
                table_id,
                query_id,
            )
            return df  # Return original DataFrame since table doesn't exist
        raise RuntimeError(f"Error while filtering duplicates: {str(e)}") from e


def get_total_records(table_id: str) -> int:
    """
    Fetch the total number of records based on the '_id' parameter from a BigQuery table.

    Parameters:
    - table_id (str): The BigQuery table ID in the format `project_id.dataset_id.table_id`.

    Returns:
    - int: The total number of records based on '_id' in the specified BigQuery table.

    Exception Handling:
    - None
    """
    # Construct the SQL query to count the total number of records
    query = f"SELECT COUNT(*) as total_record_count FROM `{table_id}`"

    # Execute the query and fetch the result
    result = read_gbq(query)

    # Get the total number of records from the result DataFrame
    total_record_count = result["total_record_count"].iloc[0]

    return total_record_count


def typecast_columns(df: DataFrame, type_mapping: Dict) -> DataFrame:
    """
    Typecast multiple DataFrame columns to specified data types and
    convert unspecified columns to string type.

    Parameters:
    - df (pd.DataFrame): The DataFrame containing the columns to typecast.
    - type_mapping (dict): A dictionary where keys are column names
        and values are target data types.

    Returns:
    - pd.DataFrame: The DataFrame with typecasted columns.

    Exception Handling:
    - None
    """
    # Mapping for pandas-specific nullable data types
    nullable_type_map = {
        "int64": pd.Int64Dtype(),
    }

    # Prepare a dictionary for final type mapping
    final_mapping = {
        col: nullable_type_map.get(target_type, target_type)
        for col, target_type in type_mapping.items()
        if col in df.columns
    }

    # Apply typecasting for columns specified in type_mapping
    df = df.astype(final_mapping)

    # Convert all columns to pd.StringDtype() if they are not in type_mapping
    cols_to_convert = [col for col in df.columns if col not in type_mapping]
    if cols_to_convert:
        df[cols_to_convert] = df[cols_to_convert].astype("string")

    return df


def update_table_schema(
    client: bigquery.Client, table_id: str, temp_table_id: str
) -> None:
    """
    Adds missing columns from the temp table to the target table schema with correct data types.

    Parameters:
    - client (bigquery.Client): The BigQuery client.
    - table_id (str): Target table ID in BigQuery.
    - temp_table_id (str): Temporary table ID in BigQuery.

    Returns:
    - None

    Exception Handling:
    - None
    """
    # Fetch the schema of the target table
    table = client.get_table(table_id)  # Get the target table schema
    existing_columns = {field.name: field for field in table.schema}

    # Fetch the schema of the temporary table
    temp_table = client.get_table(temp_table_id)  # Get the temp table schema
    temp_columns = {field.name: field for field in temp_table.schema}

    # Identify missing columns (those in temp table but not in target table)
    missing_columns = [col for col in temp_columns if col not in existing_columns]

    if missing_columns:
        # Add missing columns from temp table schema to the target table schema
        new_fields = [temp_columns[col] for col in missing_columns]
        table.schema = table.schema + new_fields  # Combine existing and new fields
        client.update_table(table, ["schema"])  # Update the table schema in BigQuery
        logger.info("Added missing columns: %s with correct types.", missing_columns)

    else:
        logger.info("No missing columns to add.")


def update_bigquery_table(
    client: bigquery.Client, df_columns: List, table_id: str, temp_table_id: str
) -> None:
    """
    Updates a BigQuery table by adding new columns and updating rows based on an input DataFrame.

    Parameters:
    - client (bigquery.Client): The BigQuery client.
    - df_columns (list): DataFrame columns, Must include a unique identifier column (e.g., '_id').
    - table_id (str): Target table ID in BigQuery.
    - temp_table_id (str): Temporary table ID in BigQuery.

    Returns:
        None

    Exception Handling:
    - ValueError: Raised if the unique identifier column ('_id') is not present in `df_columns`.
    - Exception: Logs errors and raises exceptions for failures during the merge query or
        temporary table deletion.
    """
    unique_id_col = "_id"

    if unique_id_col not in df_columns:
        raise ValueError(
            f"Input DataFrame must contain the unique identifier column '{unique_id_col}'"
        )

    logger.info("Starting the update process...")

    # Ensure the target table schema is up-to-date
    update_table_schema(client, table_id, temp_table_id)

    # Create a MERGE query to update the target table
    merge_query = f"""
    MERGE `{table_id}` T
    USING `{temp_table_id}` S
    ON T.{unique_id_col} = S.{unique_id_col}
    WHEN MATCHED THEN
    UPDATE SET {', '.join([f"T.{col} = S.{col}" for col in df_columns if col != unique_id_col])}
    """
    logger.info("Executing MERGE query for table: %s", table_id)

    try:
        query_job = client.query(merge_query)
        query_job.result()  # Wait for the query to finish
        logger.info("Table %s updated successfully.", table_id)

    except Exception as e:
        logger.error("Failed to update the table %s: %s", table_id, e)
        raise

    # Delete the temporary table
    try:
        client.delete_table(temp_table_id, not_found_ok=True)
        logger.info("Temporary table %s deleted successfully.", temp_table_id)

    except Exception as e:
        logger.warning("Failed to delete temporary table %s: %s", table_id, e)
        raise


def update_offset(
    offset: int,
    offset_metadata_key: str,
    batch_count: int,
    query_collection: Collection,
    query_id: str,
) -> int:
    """
    Updates the offset in query metadata based on the number of documents fetched.

    Parameters:
    - offset (int): The current offset.
    - offset_metadata_key (str): Specifies the key used for setting the offset
        in the query's metadata.
    - batch_count (int): The number of fetched documents.
    - query_collection (pymongo.collection.Collection): The MongoDB query collection.
    - query_id (str) : Query ID for the query

    Returns:
    - int: The new offset.

    Exception Handling:
    - None
    """
    # Calculate the new offset
    new_offset = offset + batch_count

    # Update the query metadata with the new offset
    update_query_metadata(
        query_collection,
        query_id,
        offset_metadata_key,
        new_offset,
    )

    return new_offset


def insert_into_bigquery(df: pd.DataFrame, query_id: str, table_id: str) -> None:
    """
    Inserts a Pandas DataFrame into a specified BigQuery table.

    Parameters:
    - df (pandas.DataFrame): The DataFrame containing the data to be inserted into
        the BigQuery table.
    - query_id (str): The string representation of the query_id.
    - table_id (str): The ID of the BigQuery table where the data should be inserted,
        in the format `project_id.your_dataset.your_table`.

    Returns:
    - None

    Exception Handling:
    - Exception: Raised if any error occurs during the insertion process.
    """
    try:
        # Insert the DataFrame into the BigQuery table
        to_gbq(df, table_id, if_exists="append")
        logger.info(
            "Inserted '%d' rows into '%s' for query_id: '%s'.",
            len(df),
            table_id,
            query_id,
        )

    except Exception as e:
        message = f"An error occurred while inserting data for '{query_id}' query_id into '{table_id}' BigQuery table: {e}"
        logger.error(message)
        raise


def perform_bigquery_operations(
    query_collection: Collection,
    rac_collection: Collection,
    rac_transform_collection: Collection,
    organization_db_name: str,
    meta_container: MetaContainer,
) -> None:
    """
    Performs operations to insert data from RAC collection into BigQuery tables.

    - This function fetches data from a MongoDB RAC collection in chunks,
    updates RAC transform collection with categorization data, applies
    necessary transformations, filters out duplicates, and inserts the data into
    temporary BigQuery table and updates the final BigQuery table.
    - It handles updating offsets, typecasting, and managing duplicate records to
        ensure data consistency in BigQuery.

    Parameters:
    - query_collection (Collection): MongoDB collection for query information.
    - rac_collection (Collection): MongoDB collection containing RAC data to be
        inserted into BigQuery.
    - rac_transform_collection (Collection): MongoDB collection containing  RAC transform data.
    - organization_db_name (str): Name of the organization's database, used to
        construct BigQuery table IDs.
    - meta_container (MetaContainer): MetaContainer class Instance.

    Returns:
    - None

    Exception Handling:
    - Exception: Raised if any error occurs during the process.
    """
    try:
        # Get categorization columns based on context
        categorization_columns = get_categorization_columns(SummaryContext.THEME.name)

        # Get query information
        query_meta_data = meta_container.meta_data
        payload = meta_container.payload
        query_id = payload["query_id"]
        offset = query_meta_data.get(CATEGORIZATION_BQ_OFFSET_METADATA_KEY, 0)
        rac_transform_collection_name = query_meta_data[
            RAC_TRANSFORM_COLLECTION_NAME_METADATA_KEY
        ]

        # Create BigQuery table Ids
        table_id = (
            f"{PROJECT_ID}.{organization_db_name}.{rac_transform_collection_name}"
        )
        temp_table_id = f"{PROJECT_ID}.{organization_db_name}.{rac_transform_collection_name}_{TEMP_TABLE_SUFFIX}"

        # Define RAC query projection attributes
        rac_query_projection = {
            "_id": 1,
            categorization_columns.topic: 1,
            categorization_columns.sub_topics: 1,
            categorization_columns.categorization_explanation: 1,
            categorization_columns.notable_entities: 1,
            categorization_columns.subject: 1,
            categorization_columns.object: 1,
            categorization_columns.high_level_category: 1,
            categorization_columns.youtube_category: 1,
            categorization_columns.combined_topic: 1,
            categorization_columns.cluster_prediction: 1,
            categorization_columns.cluster_name: 1,
        }
        rac_df_columns = list(rac_query_projection.keys())

        # Define type mapping
        type_mapping = {categorization_columns.cluster_prediction: "int64"}

        # Retrieve the RAC collection volume
        total_documents = query_meta_data.get(
            RAC_VOLUME_METADATA_KEY
        ) or rac_collection.count_documents({}, hint="_id_")

        # Initialize a BigQuery client
        client = bigquery.Client()

        while offset < total_documents:
            rac_df = fetch_collection_data(
                rac_collection,
                {},
                rac_query_projection,
                offset,
                int(CATEGORIZATION_BQ_CHUNK_SIZE),
            )
            batch_count = len(rac_df)

            if not rac_df.empty:
                # Update categorization data in RAC transform collection
                update_mongodb_collection(rac_transform_collection, rac_df)

                rac_df = typecast_columns(rac_df, type_mapping)
                rac_df = filter_duplicates(rac_df, temp_table_id, query_id)

            if not rac_df.empty:
                insert_into_bigquery(rac_df, query_id, temp_table_id)
                offset = update_offset(
                    offset,
                    CATEGORIZATION_BQ_OFFSET_METADATA_KEY,
                    batch_count,
                    query_collection,
                    query_id,
                )

            else:
                # Handling the case where all records in the batch are duplicates
                if batch_count:
                    offset = update_offset(
                        offset,
                        CATEGORIZATION_BQ_OFFSET_METADATA_KEY,
                        batch_count,
                        query_collection,
                        query_id,
                    )
                else:
                    break

        is_temp_table_exists = check_table_exists(client, temp_table_id)

        if is_temp_table_exists:
            bigquery_record_count = get_total_records(temp_table_id)

            # Ideal scenario: No duplicate records present
            if offset == total_documents == bigquery_record_count:
                update_bigquery_table(client, rac_df_columns, table_id, temp_table_id)

            # Handling scenario where duplicate data exists in BigQuery
            if offset >= total_documents and bigquery_record_count > total_documents:
                deduplicate_data(temp_table_id)
                update_bigquery_table(client, rac_df_columns, table_id, temp_table_id)

        else:
            logger.warning(
                "BigQuery temp table `%s` does not exist, Process already completed!",
                temp_table_id,
            )

    except Exception as e:
        logger.error("Error while performing BigQuery operations: %s", e)
        raise
