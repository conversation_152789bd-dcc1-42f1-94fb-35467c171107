"""
This module performs the categorization process for a specific encapsulation 
marker in a given query. It interacts with MongoDB collections, processes video 
transcripts, and applies categorization and embedding logic. The categorization 
results are updated in the database, and unprocessed contexts are iteratively 
processed until completion.
"""

import json
import math
import os
import re
import time
from typing import List
import dask
from dask import delayed
from google.api_core.exceptions import InternalServerError
import pandas as pd
from pandas import DataFrame
from pymongo.collection import Collection
from embedding import process_embeddings_with_dask
from youtube_transcript_api import YouTubeTranscriptApi
from categorization_tool.const import (
    CATEGORIZATION_BATCH_PROMPT,
    DEFAULT_CATEGORIZATION,
    DEFAULT_EXPLANATION,
    MAX_PARSE_RETRIES,
    MAX_UNIQUE_GROUPS,
    PARSE_RETRY_DELAY,
    CATEGORIZATION_PROMPT_CHUNK_SIZE,
    MAX_RECORD_COUNT,
    RAW_TEXT_COL,
    VIDEO_ID_COL,
    CategorizationColumns,
    get_categorization_columns,
)
from utils.llm_settings import SUMMARIES_TO_IGNORE
from utils.langchain_utils import LangChainUtility
from utils.common_utils import (
    check_marker_status,
    convert_array_columns_to_strings,
    drop_existing_columns,
    update_mongodb_collection,
)
from utils.logger import logger
from utils.utilities import (
    CategorizationProcessingField,
    EncapsulationMarkerStatus,
    EncapsulationMarkerStatusColumn,
    MetaContainer,
    SummaryContext,
    SummaryContextIdField,
)


def fetch_collection_data(
    collection: Collection,
    query_filter: dict,
    encapsulation_marker: str,
    context: str,
    max_unique_groups: int = int(MAX_UNIQUE_GROUPS),
    max_records: int = int(MAX_RECORD_COUNT),
) -> DataFrame:
    """
    Fetch records from a MongoDB collection, grouping by `unique_context_id`, and video_id.
    Limits the number of unique groups to `max_unique_groups`.
    Excludes groups where all records are processed.

    Parameters:
    - collection (pymongo.collection.Collection): The MongoDB collection object.
    - query_filter (dict): The query filter to fetch documents from collection.
    - encapsulation_marker (str): Filter records based on this marker.
    - context (str): The processing context which can be one of "CLUSTER", "STORY" or "THEME".
    - max_unique_groups (int): Maximum number of unique groups.
    - max_records (int): Maximum number of records to fetch in total.

    Returns:
    - pd.DataFrame: A DataFrame containing the fetched records.

    Exception Handling:
    - Exception: Raised if an error occurs during the fetch process.
    """
    try:
        # Get context ID and summary col fields
        context_id_field = SummaryContextIdField[context].value
        summary_col = f"{SummaryContext[context].value}_summary"

        pipeline = [
            {"$match": query_filter},
            {
                "$sort": {
                    context_id_field: -1,
                    VIDEO_ID_COL: 1,  # Sort by context_id and video_id field
                }
            },
            {"$limit": max_records},  # Limit the number of records before grouping
            {
                "$lookup": {
                    "from": collection.name,  # Same collection
                    "localField": "comment_id",  # Parent comment Id field in current document
                    "foreignField": "id",  # Id Field in the joined collection
                    "as": "matched_comment",  # Resulting array field
                }
            },
            {
                "$set": {
                    "original_comment": {
                        "$arrayElemAt": [f"$matched_comment.{RAW_TEXT_COL}", 0]
                    }
                }
            },
            {
                "$group": {
                    "_id": {
                        context_id_field: f"${context_id_field}",
                        VIDEO_ID_COL: f"${VIDEO_ID_COL}",
                    },  # Group by unique context ID and the video Id
                    "records": {
                        "$push": {
                            "_id": "$_id",
                            "original_comment": "$original_comment",
                            "video_description": "$video_info.snippet.description",
                            "video_title": "$video_info.snippet.title",
                            VIDEO_ID_COL: f"${VIDEO_ID_COL}",
                            RAW_TEXT_COL: f"${RAW_TEXT_COL}",
                            context_id_field: f"${context_id_field}",
                            summary_col: f"${summary_col}",
                        }
                    },
                }
            },
            {"$limit": max_unique_groups},  # Limit the number of unique groups
        ]

        records = list(collection.aggregate(pipeline))

        # Convert the list of records to a DataFrame
        records = pd.json_normalize(records, "records")
        logger.info(
            "Successfully fetched '%d' records for '%s' encapsulation marker from '%s' collection",
            len(records),
            encapsulation_marker,
            collection.name,
        )

        return records

    except Exception as e:
        logger.error(
            "An error occurred while fetching data for '%s' encapsulation marker from '%s' collection: %s",
            encapsulation_marker,
            collection.name,
            e,
        )
        raise


def find_overall_processing_context(
    collection: Collection, encapsulation_marker: str
) -> str:
    """
    Determines the overall processing context based on the categorization processing
    status in a MongoDB collection.

    - This function checks documents in the specified collection to determine
        which processing context to return.
    It evaluates the categorization processing status for themes in the documents based on
    the provided encapsulation marker. The priority is checked in the following order:
        - If any document has `is_theme_categorization_processed` as `False` or missing,
            the context is "THEME".
        - If all fields are processed, the context is "ALL_PROCESSED".

    Parameters:
    - collection (pymongo.collection.Collection): The MongoDB collection to query.
    - encapsulation_marker (str): The value of the `encapsulation_marker` field used
        to filter documents.

    Returns:
    - str: The processing context which can be one of "THEME", or "ALL_PROCESSED".

    Exception Handling:
    - None
    """
    # Check if there is any document where `is_{context}_categorization_processed` is not True
    context_check = collection.find_one(
        {
            "encapsulation_marker": encapsulation_marker,
            "$or": [
                {CategorizationProcessingField.THEME.value: {"$ne": True}},
            ],
        },
        projection={
            "_id": 1,
            CategorizationProcessingField.THEME.value: 1,
        },
    )

    # Determine the context based on the result of the query
    if context_check:
        if context_check.get(CategorizationProcessingField.THEME.value) is not True:
            return SummaryContext.THEME.value.upper()

    # If all documents have all fields as True
    return SummaryContext.ALL_PROCESSED.value.upper()


def get_unique_video_ids(
    collection: Collection, encapsulation_marker: str
) -> List[str]:
    """
    Fetches unique video IDs for a given encapsulation marker.

    Parameters:
    - collection (pymongo.collection.Collection): The MongoDB collection object.
    - encapsulation_marker (str): The encapsulation marker to filter the records.

    Returns:
    - List[str]: A list of unique video IDs matching the encapsulation marker.

    Exception Handling:
    - None
    """
    return collection.distinct(
        VIDEO_ID_COL, {"encapsulation_marker": encapsulation_marker}
    )


def get_video_transcript(video_id: str) -> str:
    """
    Fetches the transcript of a YouTube video in English and returns it as
    a clean, concatenated string.

    Parameters:
    - video_id (str): The ID of the YouTube video.

    Returns:
    - str: The cleaned and concatenated transcript of the video or an empty string
        if the transcript is not available.

    Exception Handling:
    - Exception: Logs the error and returns an empty string in case of an exception
    """
    try:
        # Directly fetch the English transcript
        transcript = YouTubeTranscriptApi.get_transcript(video_id, languages=["en"])

        # Combine and clean transcript text
        final_transcript = " ".join(
            entry["text"].replace("\n", " ") for entry in transcript
        )

    except Exception as e:
        logger.warning("Error fetching transcript for video %s: %s", video_id, str(e))

        final_transcript = ""  # Return empty string in case of error

    return final_transcript


def generate_prompt_for_chunk(
    chunk_df: pd.DataFrame,
    summary_col: str,
    separator: str,
    meta_container: MetaContainer,
) -> str:
    """
    Generates a prompt for a chunk of data for analysis.

    Parameters:
    - chunk_df (pd.DataFrame): DataFrame containing a chunk of records.
    - summary_col (str): Name of the column containing summaries.
    - separator (str): Separator string to distinguish between records.
    - meta_container (MetaContainer): MetaContainer class Instance.

    Returns:
    - str: The generated prompt for the chunk.

    Exception Handling:
    - None
    """
    # Use the first summary, video_description, and video_title as the shared context
    summary_text = chunk_df.get(summary_col, pd.Series([""])).iloc[0]
    video_description = chunk_df.get("video_description", pd.Series([""])).iloc[0]
    video_id = chunk_df.get(VIDEO_ID_COL, pd.Series([""])).iloc[0]
    video_title = chunk_df.get("video_title", pd.Series([""])).iloc[0]

    # Replace NaN values with empty strings for the given variables
    summary_text, video_description, video_id, video_title = [
        "" if pd.isna(col) else col
        for col in [summary_text, video_description, video_id, video_title]
    ]
    summary_text = "" if summary_text in SUMMARIES_TO_IGNORE else summary_text

    # Get transcript for particular video
    transcript = meta_container.meta_data["video_transcripts"].get(video_id, "")

    # Ensure separator is defined as part of formatting
    separator = f"\n{separator}\n"

    # Create the combined texts
    combined_texts = separator.join(
        [
            f"\nORIGINAL TOP LEVEL COMMENT {i+1} FOR CONTEXT:\n{row.get('original_comment', '') if pd.notna(row.get('original_comment', '')) else ''}\n"
            f"\nTEXT {i+1}:\n{row[RAW_TEXT_COL]}\n"
            for i, (_, row) in enumerate(chunk_df.iterrows())
        ]
    )

    # Insert values into the prompt template
    prompt = CATEGORIZATION_BATCH_PROMPT.format(
        summary=summary_text,
        video_title=video_title,
        video_description=video_description,
        transcript=transcript,
        combined_texts=combined_texts,
    )

    return prompt


def extract_and_parse_json(text: str) -> List:
    """
    Extracts a JSON array from a given text string and parses it.

    Parameters:
    - text (str): The input text containing random text and a JSON array.

    Returns:
    - list: Parsed JSON data if extraction and parsing are successful.

    Exception Handling:
    -  Exception: Raised error message if parsing fails.
    """
    # Regular expression pattern to capture JSON array
    pattern = r"\[\s*(?:\{[^{}]*\}(?:\s*,\s*\{[^{}]*\})*)\s*\]"
    # Search for the JSON portion in the string
    match = re.search(pattern, text)
    if match:
        json_string = match.group(0)
        try:
            # Parse the JSON string
            data = json.loads(json_string)
            return data
        except json.JSONDecodeError as e:
            raise Exception("JSON decoding failed: %s", e) from e
    else:
        return []


def generate_analysis(text_prompt: str, langchain_utility: LangChainUtility) -> str:
    """
    Generate analysis for the given text prompt.

    Parameters:
    - text_prompt (str): The input text prompt.
    - langchain_utility (LangChainUtility): LangChainUtility class Instance.

    Returns:
    - str: The analysis for the given text prompt.

    Exception Handling:
    - Exception: Raised if an error occurs while generating analysis for the text prompt.
    """
    try:
        # Generate content using the LangChain utility
        model_response = langchain_utility.invoke_llm(text_prompt)

        # Check if content exists and return it, otherwise return an empty string
        if model_response.content:
            return model_response.content

        # Handle case where the model does not return content (e.g., max_output_tokens limit or AI violations)
        return ""

    except Exception as e:
        # Log the error and raise it to allow higher-level handling
        logger.error("Failed to generate analysis: %s", e)
        raise


def update_dataframe_with_categorization(
    df: pd.DataFrame,
    parsed_response: list,
    categorization_columns: CategorizationColumns,
    start_index: int,
) -> None:
    """
    Updates the original DataFrame with categorization results in bulk.

    Parameters:
    - df (pd.DataFrame): Original DataFrame to be updated.
    - parsed_response (list): Parsed response from the model API.
    - categorization_columns (CategorizationColumns): An object containing column
        names for categorization explanation, topics, sub-topics, notable entities,
        and other related fields.
    - start_index (int): The starting index in the DataFrame for the current chunk.

    Returns:
    - None

    Exception Handling:
    - Exception: Raised if Mismatch between parsed response length and expected update range.
    """
    # Convert parsed_response to a DataFrame
    response_df = pd.DataFrame(parsed_response)

    # Ensure 'record_number' is sorted to match the original DataFrame index order
    response_df.sort_values(by="record_number", inplace=True)

    # Create index range for updating the original DataFrame
    update_index = df.index[start_index : start_index + len(response_df)]

    # Check if lengths match before updating
    if len(response_df) != len(update_index):
        raise Exception(
            "Mismatch between parsed response length and expected update range."
        )

    # Convert columns with array-like values in a DataFrame to comma-separated strings.
    response_df = convert_array_columns_to_strings(response_df)

    # Mark the categorization processing status for particular context
    response_df[categorization_columns.is_categorization_processed] = True

    # Update multiple columns at once in the original DataFrame
    df.loc[
        update_index,
        [
            categorization_columns.topic,
            categorization_columns.sub_topics,
            categorization_columns.categorization_explanation,
            categorization_columns.notable_entities,
            categorization_columns.subject,
            categorization_columns.object,
            categorization_columns.high_level_category,
            categorization_columns.youtube_category,
            categorization_columns.is_categorization_processed,
        ],
    ] = response_df[
        [
            "topic",
            "sub_topics",
            "explanation",
            "notable_entities",
            "subject",
            "object",
            "high_level_category",
            "youtube_category",
            categorization_columns.is_categorization_processed,
        ]
    ].values


def process_categorization_in_chunks(
    df: pd.DataFrame,
    chunk_size: int,
    context: str,
    categorization_columns: CategorizationColumns,
    langchain_utility: LangChainUtility,
    meta_container: MetaContainer,
    separator: str = "---",
    max_recursion_depth: int = 1,
    current_depth: int = 0,
) -> DataFrame | None:
    """
    Processes categorization analysis in chunks and stores the results in the DataFrame.

    Parameters:
    - df (pd.DataFrame): DataFrame containing the summaries and texts for multiple records.
    - chunk_size (int): Number of records per chunk.
    - context (str): The processing context which can be one of "CLUSTER", "STORY" or "THEME".
    - categorization_columns (CategorizationColumns): An object containing column names
        for categorization explanation, topics, sub-topics, notable entities, and other related fields.
    - langchain_utility (LangChainUtility): LangChainUtility class Instance.
    - meta_container (MetaContainer): MetaContainer class Instance.
    - separator (str, optional): Separator string to distinguish between records. Default is '---'.
    - max_recursion_depth (int, optional): Maximum depth for recursion to prevent infinite loops.
        Default is 1.
    - current_depth (int, optional): Current recursion depth to track retries.

    Returns:
    - pd.DataFrame: DataFrame with additional columns for topic, sub_topics, notable_entities,
        subject, object, high_level_category, youtube_category, and explanation.

    Exception Handling:
    - Exception: Raise an error occurs while processing categorization analysis after retries.
    """
    if current_depth > max_recursion_depth:
        logger.warning("Maximum recursion depth reached. Aborting further retries.")
        # Handling the special case where the model fails to provide a response for a single record (i.e., chunk_size=1)
        # This ensures the process continues by marking the categorization as processed for the single record
        df.loc[df.index[0], categorization_columns.is_categorization_processed] = True
        return

    # Get summary column based on the context
    summary_col = f"{SummaryContext[context].value}_summary"

    # Initialize the new columns if not already present
    for col_name in vars(categorization_columns).values():
        if col_name not in df.columns:
            df[col_name] = None

    # Split DataFrame into chunks
    num_chunks = (len(df) + chunk_size - 1) // chunk_size

    for chunk_index in range(num_chunks):
        start_index = chunk_index * chunk_size
        chunk_df = df[start_index : (chunk_index + 1) * chunk_size]

        # Generate prompt for the current chunk
        prompt = generate_prompt_for_chunk(
            chunk_df, summary_col, separator, meta_container
        )

        # Processing categorization with retry logic
        retry_count = 0

        # Prepare parameters to recursively invoke the function with chunk size 1
        single_chunk_process_params = {
            "df": chunk_df,
            "chunk_size": 1,
            "context": context,
            "categorization_columns": categorization_columns,
            "langchain_utility": langchain_utility,
            "meta_container": meta_container,
            "separator": separator,
            "max_recursion_depth": max_recursion_depth,
        }

        while retry_count < int(MAX_PARSE_RETRIES):
            try:
                # Call model API and parse the response
                response = generate_analysis(prompt, langchain_utility)
                parsed_response = extract_and_parse_json(response)

                # Update the DataFrame with the categorization analysis results
                if parsed_response:
                    update_dataframe_with_categorization(
                        df,
                        parsed_response,
                        categorization_columns,
                        start_index,
                    )
                else:
                    # If parsed response is not there, recursively process the current chunk with a chunk size of 1
                    # This ensures that each record is handled individually
                    process_categorization_in_chunks(
                        **single_chunk_process_params, current_depth=current_depth + 1
                    )
                break

            except InternalServerError:
                # Raise an exception if an internal server error occurs even after retries for processing a single chunk.
                if len(chunk_df) == 1:
                    raise

                # If Internal server error occurred, recursively process the current chunk with a chunk size of 1
                # This ensures that each record is handled individually
                process_categorization_in_chunks(
                    **single_chunk_process_params, current_depth=current_depth + 1
                )
                break

            except Exception as e:
                retry_count += 1

                if retry_count < int(MAX_PARSE_RETRIES):
                    logger.warning(
                        "Model response parsing attempt '%d' failed for categorization: '%s'. Retrying in '%d' seconds...",
                        retry_count,
                        e,
                        int(PARSE_RETRY_DELAY),
                    )
                    time.sleep(int(PARSE_RETRY_DELAY))

                else:
                    logger.warning(
                        "Maximum '%d' retries reached for chunk starting at index %d. Switching to smaller chunk size.",
                        retry_count,
                        start_index,
                    )
                    # Recursively process the current chunk with a chunk size of 1
                    # This ensures that each record is handled individually
                    process_categorization_in_chunks(
                        **single_chunk_process_params, current_depth=current_depth + 1
                    )

    return df


def fill_missing_categorization_columns(
    categorization_df: pd.DataFrame, categorization_columns: CategorizationColumns
) -> DataFrame:
    """
    Fills missing values in categorization columns of the provided DataFrame with default values.

    Parameters:
    - categorization_df (DataFrame): The DataFrame containing the categorization columns.
    - categorization_columns (CategorizationColumns): An object containing the column names
        for categorization.

    Returns:
    - DataFrame: The DataFrame with missing categorization column values filled with default values.

    Exception Handling:
    - None
    """
    # Create a dictionary with default values for all categorization columns
    columns_to_check = {
        col: DEFAULT_CATEGORIZATION for col in vars(categorization_columns).values()
    }

    # Override specific columns default values
    columns_to_check.update(
        {
            categorization_columns.categorization_explanation: DEFAULT_EXPLANATION,
            categorization_columns.cluster_name: None,
            categorization_columns.cluster_prediction: None,
            categorization_columns.embedding: None,
            categorization_columns.is_categorization_processed: False,
        }
    )

    # Iterate through each column and replace empty strings or null values with the default string
    for col, default_string in columns_to_check.items():
        if col in categorization_df.columns:
            categorization_df[col] = categorization_df[col].apply(
                lambda x: default_string if pd.isna(x) or x == "" else x
            )

    return categorization_df


def generate_combined_topic(
    df: DataFrame,
    topic_col: str,
    subtopic_col: str,
    combined_topic_col: str,
    exclude_phrase: str = DEFAULT_CATEGORIZATION,
) -> DataFrame:
    """
    Concatenates the topic and subtopics columns to create a combined topic column,
    avoiding redundant occurrences of a specific phrase.

    Parameters:
    - df (pd.DataFrame): The DataFrame containing the topic and subtopics columns.
    - topic_col (str): The name of the column containing topics.
    - subtopic_col (str): The name of the column containing subtopics.
    - combined_topic_col (str): The name of the combined topic column.
    - exclude_phrase (str): The phrase to exclude from redundant inclusion
        (default is "Unable to categorize").

    Returns:
    - pd.DataFrame: The DataFrame with the new combined topic column added.

    Exception Handling:
    - None
    """

    def combine_topics(topic, subtopic):
        # If both are the excluded phrase, return it
        if topic == exclude_phrase and subtopic == exclude_phrase:
            return exclude_phrase

        # If one is the excluded phrase, return the other
        if topic == exclude_phrase:
            return subtopic

        if subtopic == exclude_phrase:
            return topic

        # Otherwise, concatenate topic and subtopic
        return f"{topic}, {subtopic}"

    # Apply row-wise logic and update the combined topic column
    df[combined_topic_col] = df.apply(
        lambda row: combine_topics(row[topic_col], row[subtopic_col]), axis=1
    ).str.replace("\n", "", regex=False)

    return df


def process_categorization_with_dask(
    categorization_df: pd.DataFrame,
    context: str,
    categorization_columns: CategorizationColumns,
    langchain_utility: LangChainUtility,
    meta_container: MetaContainer,
) -> DataFrame:
    """
    Process summaries data using Dask with specified categorization function.
    - Optimize processing by partitioning the data into chunks based on unique
        context IDs and VideoIds.

    Parameters:
    - categorization_df (pd.DataFrame): Input Pandas DataFrame containing summaries
        data for theme, stories or clusters.
    - context (str): The processing context which can be one of "CLUSTER", "STORY" or "THEME".
    - categorization_columns (CategorizationColumns): An object containing column names
        for categorization explanation, topics, sub-topics, notable entities, and other related fields.
    - langchain_utility (LangChainUtility): LangChainUtility class Instance.
    - meta_container (MetaContainer): MetaContainer class Instance.

    Returns:
    - pd.DataFrame: Processed Pandas DataFrame containing the final results after computation using dask.

    Exception Handling:
    - None
    """
    # Group the DataFrame by unique context ID and the video Id
    grouped = list(
        categorization_df.groupby([SummaryContextIdField[context].value, VIDEO_ID_COL])
    )

    # Determine the number of unique group IDs
    num_unique_ids = len(grouped)
    desired_partitions = os.cpu_count()  # The target number of partitions

    partitions = []

    if num_unique_ids >= desired_partitions:
        # Case 1: Unique group IDs more than desired partitions, each gets its own partition
        # Convert the grouped DataFrame to delayed objects
        for _, group in grouped:
            partitions.append(
                delayed(process_categorization_in_chunks)(
                    group,
                    int(CATEGORIZATION_PROMPT_CHUNK_SIZE),
                    context,
                    categorization_columns,
                    langchain_utility,
                    meta_container,
                )
            )
    else:
        # Case 2: Unique group IDs fewer than desired partitions
        total_records = len(categorization_df)
        partitions_count = []

        # Calculate the number of partitions each unique group ID should have based on its proportion
        for uid, group in grouped:
            num_records = len(group)
            proportion = num_records / total_records
            num_partitions = max(
                1, round(proportion * desired_partitions)
            )  # Ensure at least 1 partition
            partitions_count.append((uid, group, num_partitions))

        # Create partitions for each unique group ID
        for uid, group, num_partitions in partitions_count:
            num_records = len(group)
            partition_size = math.ceil(num_records / num_partitions)

            for i in range(num_partitions):
                start_index = i * partition_size
                end_index = min((i + 1) * partition_size, num_records)
                partition_records = group.iloc[
                    start_index:end_index
                ].copy()  # Create a copy to avoid modification warnings

                if not partition_records.empty:  # Check if the partition is not empty
                    partitions.append(
                        delayed(process_categorization_in_chunks)(
                            partition_records,
                            int(CATEGORIZATION_PROMPT_CHUNK_SIZE),
                            context,
                            categorization_columns,
                            langchain_utility,
                            meta_container,
                        )
                    )

    # Trigger computation
    results = dask.compute(*partitions, scheduler="threads")

    # Combine results back into a single DataFrame and reset the index
    categorization_df = pd.concat(results).reset_index(drop=True)

    # Fills missing values in categorization columns with default values
    categorization_df = fill_missing_categorization_columns(
        categorization_df, categorization_columns
    )

    # Get combined topic
    categorization_df = generate_combined_topic(
        categorization_df,
        categorization_columns.topic,
        categorization_columns.sub_topics,
        categorization_columns.combined_topic,
    )

    # Drop columns that are not needed for the update operation
    columns_to_drop = [
        "original_comment",
        "video_description",
        "video_title",
        VIDEO_ID_COL,
        RAW_TEXT_COL,
        SummaryContextIdField[context].value,
        f"{SummaryContext[context].value}_summary",
        categorization_columns.cluster_name,
        categorization_columns.cluster_prediction,
    ]
    drop_existing_columns(categorization_df, columns_to_drop)

    logger.info(
        "Successfully processed DataFrame using Dask. Length: %d, Partitions: %d",
        len(categorization_df),
        len(partitions),
    )

    return categorization_df


def perform_categorization(
    query_id: str,
    encapsulation_marker: str,
    encapsulation_marker_id: str,
    encapsulation_marker_collection: Collection,
    rac_collection: Collection,
    langchain_utility: LangChainUtility,
    meta_container: MetaContainer,
) -> None:
    """
    Processes categorization for an encapsulation marker and updates the database.

    Parameters:
    - query_id (str): Unique identifier for the query.
    - encapsulation_marker (str): The name of the encapsulation marker.
    - encapsulation_marker_id (str): Unique identifier for the encapsulation marker.
    - encapsulation_marker_collection (Collection): MongoDB collection for marker data.
    - rac_collection (Collection): MongoDB collection for RAC data.
    - langchain_utility (LangChainUtility): LangChainUtility class Instance.
    - meta_container (MetaContainer): MetaContainer class Instance.

    Steps:
    1. Checks if categorization is already completed or processed.
    2. Fetches processing context and video transcripts for the encapsulation marker.
    3. Iteratively processes unprocessed contexts:
        - Fetches categorization data.
        - Performs categorization and embedding updates.
        - Updates results in the database and determines the next context.

    Exception Handling:
    - Exception: Raised if any error occurs during the categorization process.

    Returns:
    - None
    """
    try:
        # Check if the encapsulation marker with the given query ID has completed the categorization.
        is_marker_completed = check_marker_status(
            encapsulation_marker_collection,
            encapsulation_marker_id,
            query_id,
            EncapsulationMarkerStatusColumn.CATEGORIZATION_TOOL_STATUS.value,
            EncapsulationMarkerStatus.COMPLETED.value,
        )
        # Get context such as theme for which we need to process
        context = find_overall_processing_context(rac_collection, encapsulation_marker)
        if is_marker_completed or context == SummaryContext.ALL_PROCESSED.value.upper():
            logger.warning(
                "'%s' encapsulation Marker with query ID '%s' and encapsulation marker ID '%s' is already completed.",
                encapsulation_marker,
                query_id,
                encapsulation_marker_id,
            )

            return

        # Fetches transcripts for unique video IDs corresponding to a given encapsulation marker,
        # Create a dictionary where keys are video IDs and values are their transcripts
        transcripts = {}
        video_ids = get_unique_video_ids(rac_collection, encapsulation_marker)

        for video_id in video_ids:
            transcripts[video_id] = get_video_transcript(video_id)
        meta_container.update_meta_data({"video_transcripts": transcripts})

        # Filter to find documents where categorization is not processed
        query_filter = {
            "encapsulation_marker": encapsulation_marker,
            CategorizationProcessingField[context].value: {"$ne": True},
        }

        while context != SummaryContext.ALL_PROCESSED.value.upper():
            # Get categorization columns based on context
            categorization_columns = get_categorization_columns(context)
            categorization_df = fetch_collection_data(
                rac_collection, query_filter, encapsulation_marker, context
            )
            if not categorization_df.empty:
                categorization_df = process_categorization_with_dask(
                    categorization_df,
                    context,
                    categorization_columns,
                    langchain_utility,
                    meta_container,
                )

                # Process embeddings for combined_topic columns
                categorization_df = process_embeddings_with_dask(
                    categorization_df,
                    categorization_columns,
                    encapsulation_marker,
                    langchain_utility,
                )

                # Update categorization data
                update_mongodb_collection(
                    rac_collection, categorization_df, encapsulation_marker
                )

            else:
                logger.warning(
                    "Warning: categorization_df for '%s' encapsulation_marker of '%s' query_id is empty. Skipping categorization.",
                    encapsulation_marker,
                    query_id,
                )

            # Retrieve the context for a specific encapsulation marker for which the categorization is not processed
            context = find_overall_processing_context(
                rac_collection, encapsulation_marker
            )

    except Exception as e:
        logger.error(
            "Error while processing categorization for '%s' encapsulation_marker: %s",
            encapsulation_marker,
            e,
        )
        raise
