"""
This module contains the logic for subscribing to a Google Cloud Pub/Sub topic, 
processing incoming messages, and triggering categorization based on the message payload.
"""

import json
from concurrent.futures import TimeoutError
from google.cloud import pubsub_v1
from categorization_tool.const import PROJECT_ID, SUBSCRIPTION_ID
from categorization_tool import trigger_categorization
from utils.logger import logger


subscriber = pubsub_v1.SubscriberClient()
subscription_path = subscriber.subscription_path(PROJECT_ID, SUBSCRIPTION_ID)


def callback(message: pubsub_v1.subscriber.message.Message) -> None:
    """
    Processes a Pub/Sub message by decoding the data, logging the message details,
    acknowledging receipt, and triggering categorization with the decoded payload.

    Parameters:
    - message (pubsub_v1.subscriber.message.Message): The Pub/Sub message containing the event data.

    Returns:
    - None

    Exception Handling:
    - None
    """
    logger.info("Received data of the message %s", message.data)
    logger.info("With delivery attempts: %s.", message.delivery_attempt)
    decoded = message.data.decode("utf-8")
    payload = json.loads(decoded)
    message.ack()
    trigger_categorization(payload)


streaming_pull_future = subscriber.subscribe(subscription_path, callback=callback)
logger.info("Listening for messages on %s..\n", subscription_path)

# Wrap subscriber in a 'with' block to automatically call close() when done.
with subscriber:

    # When `timeout` is not set, result() will block indefinitely,
    # unless an exception is encountered first.
    try:
        streaming_pull_future.result()

    except TimeoutError:
        streaming_pull_future.cancel()  # Trigger the shutdown.
        streaming_pull_future.result()  # Block until the shutdown is complete.
