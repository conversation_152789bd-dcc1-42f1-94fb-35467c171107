"""
This module handles the clustering process for a given encapsulation marker using 
KMeans clustering. It retrieves embedding data, determines the optimal number of 
clusters, generates cluster predictions, and assigns intelligible cluster names using 
generative AI. The clustering results are then updated in the MongoDB database.
"""

import gc
import numpy as np
import pandas as pd
from pymongo.collection import Collection
from sklearn.cluster import KMeans
from sklearn.metrics import silhouette_score
from categorization import generate_analysis
from categorization_tool.const import (
    CLUSTER_NAME_PROMPT,
    DEFAULT_CATEGORIZATION,
    DEFAULT_CLUSTER_NAME,
    CategorizationColumns,
    get_categorization_columns,
)
from utils.common_utils import (
    drop_existing_columns,
    fetch_collection_data,
    update_mongodb_collection,
)
from utils.langchain_utils import LangChainUtility
from utils.logger import logger
from utils.utilities import SummaryContext


def determine_optimal_clusters(
    embeddings: np.ndarray,
    range_n_clusters: range = range(3, 9),
    init: str = "k-means++",
    random_state: str = 42,
) -> int:
    """
    Determines the best number of clusters for KMeans using the Silhouette score method.

    Parameters:
    - embeddings (np.ndarray): The input data for clustering.
    - range_n_clusters (range): The range of cluster numbers to evaluate.
    - init (str): Method for initialization (default is 'k-means++').
    - random_state (int): Seed for random number generation (default is 42).

    Returns:
    - int: The optimal number of clusters (best_clusters).

    Exception Handling:
    - None
    """
    best_clusters = 0  # Best cluster number
    previous_silh_avg = (
        -1
    )  # Start with a low number to ensure the first calculation is considered
    avg_sil = []
    num_samples = len(embeddings)

    # Adjust the range of clusters to ensure it is valid
    valid_range = [k for k in range_n_clusters if 2 <= k <= num_samples - 1]

    # Silhouette score method of determining K
    # Evaluate silhouette score for each cluster count in the range
    for n_clusters in valid_range:
        logger.info("Starting KMeans for %s clusters.", n_clusters)

        clusterer = KMeans(n_clusters=n_clusters, init=init, random_state=random_state)
        cluster_labels = clusterer.fit_predict(embeddings)
        silhouette_avg = silhouette_score(embeddings, cluster_labels)
        avg_sil.append(silhouette_avg)

        logger.info("Clusters: %d, Silhouette Score: %.4f", n_clusters, silhouette_avg)

        if silhouette_avg > previous_silh_avg:
            previous_silh_avg = silhouette_avg
            best_clusters = n_clusters

    logger.info(
        "Best clusters: %s with silhouette score: %.4f",
        best_clusters,
        previous_silh_avg,
    )

    return best_clusters


def generate_cluster_predictions(
    embeddings: np.ndarray,
    embeddings_df: pd.DataFrame,
    cluster_prediction_col: str,
    n_clusters: int,
    init: str = "k-means++",
    random_state: int = 42,
) -> pd.DataFrame:
    """
    Generates cluster predictions using KMeans.

    Parameters:
    - embeddings (np.ndarray): The input data for clustering.
    - embeddings_df (pd.DataFrame): The embeddings dataFrame for clustering.
    - cluster_prediction_col (str): The name of the cluster prediction column.
    - n_clusters (int): The number of clusters to use for KMeans.
    - init (str): Initialization method for centroids. Default is "k-means++".
    - random_state (int): Random seed for reproducibility. Default is 42.

    Returns:
    - pd.DataFrame: DataFrame with predicted cluster labels for each data point.

    Exception Handling:
    - None
    """
    kmeans = KMeans(n_clusters=n_clusters, init=init, random_state=random_state)

    # Predict cluster labels for each data point and store in the dataFrame
    embeddings_df[cluster_prediction_col] = kmeans.fit_predict(embeddings).tolist()
    logger.info("Cluster predictions generated")

    return embeddings_df


def sanitize_response(response, bad_chars=["##", "**"]) -> str:
    """
    Sanitizes the response by removing unwanted characters and trimming extra spaces.

    Parameters:
    - response (str): The raw response string to be sanitized.
    - bad_chars (list, optional): List of unwanted characters or substrings to remove.
        Defaults to ["##", "**"].

    Returns:
    -  str: The sanitized response string.

    Exception Handling:
    - None
    """
    for char in bad_chars:
        response = response.replace(char, "").strip()

    return response


def generate_cluster_names(
    df: pd.DataFrame,
    n_clusters: int,
    categorization_columns: CategorizationColumns,
    langchain_utility: LangChainUtility,
) -> pd.DataFrame:
    """
    Generates intelligible cluster names using semantic analysis of data points in each cluster.

    Parameters:
    - df (pd.DataFrame): DataFrame containing data points and cluster predictions.
    - n_clusters (int): Number of clusters to process.
    - categorization_columns (CategorizationColumns): An object containing column names
        for categorization explanation,
    topics, sub-topics, combined topic, notable entities, embedding and other related fields.
    - langchain_utility (LangChainUtility): LangChainUtility class Instance.

    Returns:
    - pd.DataFrame: DataFrame with a new column 'cluster name' containing the cluster names.

    Exception Handling:
    - None
    """
    # Get column name for cluster prediction in the DataFrame
    cluster_prediction_col = categorization_columns.cluster_prediction

    # Initialize a dictionary to store cluster names
    cluster_name_mapping = {}

    for cluster_id in range(n_clusters):
        # Extract data points for the current cluster
        cluster_data = df[df[cluster_prediction_col] == cluster_id][
            categorization_columns.combined_topic
        ].tolist()
        cluster_size = len(cluster_data)

        # Remove items that match 'Unable to categorize'
        cluster_data = [
            item for item in cluster_data if item not in {DEFAULT_CATEGORIZATION}
        ]

        # Remove duplicates
        cluster_data = list(set(cluster_data))

        if not cluster_data:  # Handle empty clusters gracefully
            logger.warning("Cluster %s has no data points.", cluster_id)
            cluster_name_mapping[cluster_id] = DEFAULT_CLUSTER_NAME
            continue

        logger.info(f"Processing Cluster {cluster_id}: {cluster_size} data points")

        # Format the prompt with cluster data
        prompt = CLUSTER_NAME_PROMPT.format(cluster_data=cluster_data)

        # Make the Gemini API call to get the cluster name
        response = generate_analysis(prompt, langchain_utility)
        if not response:
            response = DEFAULT_CLUSTER_NAME

        # Clean up the response by removing unwanted characters
        response = sanitize_response(response)
        logger.info(f"Cluster {cluster_id} Name: {response}")

        # Store the cleaned response in the dictionary
        cluster_name_mapping[cluster_id] = response or DEFAULT_CLUSTER_NAME

    # Map the generated names back to the DataFrame
    df[categorization_columns.cluster_name] = df[cluster_prediction_col].map(
        cluster_name_mapping
    )
    logger.info("Cluster names generation completed successfully.")

    return df


def perform_clustering(
    encapsulation_marker: str,
    rac_collection: Collection,
    langchain_utility: LangChainUtility,
) -> None:
    """
    Performs clustering for a given encapsulation marker and updates the database.

    Parameters:
    - encapsulation_marker (str): The name of the encapsulation marker.
    - rac_collection (Collection): MongoDB collection for RAC data.
    - langchain_utility (LangChainUtility): LangChainUtility class Instance.

    Steps:
    1. Fetches embedding data for the encapsulation marker.
    2. Determines the optimal number of clusters and generates cluster predictions.
    3. Assigns cluster names using generative AI and prepares the data for updates.
    4. Updates clustering results in the database.

    Exception Handling:
    - Exception: Raised if any error occurs during the clustering process.

    Returns:
    - None
    """
    try:
        # Get categorization columns based on context
        categorization_columns = get_categorization_columns(SummaryContext.THEME.name)
        embedding_column = categorization_columns.embedding

        # Filter to find documents for particular encapsulation marker
        query_filter = {"encapsulation_marker": encapsulation_marker}

        # Fetch embeddings data
        embeddings_df = fetch_collection_data(
            rac_collection,
            query_filter,
            {"_id": 1, categorization_columns.combined_topic: 1, embedding_column: 1},
        )

        if not embeddings_df.empty:
            embeddings = np.array(
                embeddings_df.pop(embedding_column).tolist(), dtype=np.float32
            )
            logger.info(
                "Embeddings successfully extracted and converted to NumPy array."
            )

            # Find the best number of clusters
            best_clusters = determine_optimal_clusters(embeddings)

            # Generate cluster predictions using the best number of clusters
            embeddings_df = generate_cluster_predictions(
                embeddings,
                embeddings_df,
                categorization_columns.cluster_prediction,
                best_clusters,
            )

            # Free memory by removing reference, setting to None, and invoke garbage collection
            del embeddings
            embeddings = None
            gc.collect()

            # Generate cluster names using generative AI
            embeddings_df = generate_cluster_names(
                embeddings_df, best_clusters, categorization_columns, langchain_utility
            )

            # Drop columns that are not needed for the update operation
            columns_to_drop = [
                categorization_columns.combined_topic,
                categorization_columns.embedding,
            ]
            drop_existing_columns(embeddings_df, columns_to_drop)

            # Update clustering data
            update_mongodb_collection(
                rac_collection, embeddings_df, encapsulation_marker
            )

        else:
            logger.warning(
                "Warning: embeddings_df for '%s' encapsulation_marker is empty. Skipping clustering.",
                encapsulation_marker,
            )

    except Exception as e:
        logger.error(
            "Error while processing clustering for '%s' encapsulation_marker: %s",
            encapsulation_marker,
            e,
        )
        raise
