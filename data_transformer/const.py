"""
This module contains constants used in files of data transformer consumer.
"""

import os
from utils.utilities import SentimentPro<PERSON>ing<PERSON>ield, SummaryProcessingField

BASE_LOOPBACK_THRESHOLD = os.getenv("BASE_LOOPBACK_THRESHOLD")
CHUNK_SIZE = os.getenv("CHUNK_SIZE")
DATA_TRANSFORMER_TOPIC_ID = os.getenv("DATA_TRANSFORMER_TOPIC_ID")
PROJECT_ID = os.getenv("PROJECT_ID")
QUERY_COLLECTION_NAME = os.getenv("QUERY_COLLECTION_NAME")
RAC_TRANSFORM_COLLECTION_SUFFIX = os.getenv("RAC_TRANSFORM_COLLECTION_SUFFIX")
SEQUENCE_COORDINATOR_TOPIC_ID = os.getenv("SEQUENCE_COORDINATOR_TOPIC_ID")

COLUMNS_TO_FLATTEN = ["snippet", "user_upload_info", "video_info"]
DATA_UNIFIER_COLUMNS = {"date", "id", "text"}
DATA_TRANSFORMER_OFFSET_METADATA_KEY = "DATA_TRANSFORMER_OFFSET"
RAC_COLLECTION_NAME_METADATA_KEY = "RAC_COLLECTION_NAME"
RAC_TRANSFORM_COLLECTION_NAME_METADATA_KEY = "RAC_TRANSFORM_COLLECTION_NAME"
RAC_VOLUME_METADATA_KEY = "RAC_VOLUME"
RAC_TRANSFORM_VOLUME_METADATA_KEY = "RAC_TRANSFORM_VOLUME"
RAC_QUERY_PROJECTION = {
    "embedding": 0,
    "is_embedded": 0,
    "POS": 0,
    SentimentProcessingField.CLUSTER.value: 0,
    SentimentProcessingField.STORY.value: 0,
    SentimentProcessingField.THEME.value: 0,
    SummaryProcessingField.CLUSTER.value: 0,
    SummaryProcessingField.STORY.value: 0,
    SummaryProcessingField.THEME.value: 0,
}
QUERY_PROJECTION_ATTRIBUTES = {
    f"meta_data.{DATA_TRANSFORMER_OFFSET_METADATA_KEY}": 1,
    f"meta_data.{RAC_COLLECTION_NAME_METADATA_KEY}": 1,
    f"meta_data.{RAC_TRANSFORM_COLLECTION_NAME_METADATA_KEY}": 1,
    f"meta_data.{RAC_VOLUME_METADATA_KEY}": 1,
}
YT_COMMENT_COL_PREFIXES = ["user_upload_info"]
YT_COMMENTS_COLUMNS_TO_RETAIN = [
    "_id",
    "kind",
    "etag",
    "id",
    "record_type",
    "author",
    "author_profile_image_url",
    "author_channel_url",
    "like_count",
    "reply_count",
    "comment_id",
    "text",
    "video_id",
    "BODY1",
    "EMOJIS",
    "EMOJIS_Unique",
    "EMOJIS_Unique_Count",
    "Hashtag",
    "Hashtag_Position",
    "Hashtag_Unique",
    "Hashtag_Unique_Count",
    "Keyword",
    "Lemitized",
    "Phrase",
    "Stories",
    "Themes",
    "Unique_Cluster_ID",
    "Unique_Story_ID",
    "cluster_id",
    "encapsulation_marker",
    "cluster_summary",
    "cluster_sentiment",
    "cluster_sentiment_reasoning",
    "story_summary",
    "story_sentiment",
    "theme_summary",
    "theme_sentiment",
    "story_sentiment_reasoning",
    "theme_sentiment_reasoning",
    "replies",
    "snippet_authorChannelId_value",
    "snippet_authorChannelUrl",
    "snippet_authorDisplayName",
    "snippet_authorProfileImageUrl",
    "snippet_canRate",
    "snippet_canReply",
    "snippet_channelId",
    "snippet_isPublic",
    "snippet_likeCount",
    "snippet_parentId",
    "snippet_textDisplay",
    "snippet_textOriginal",
    "snippet_topLevelComment_etag",
    "snippet_topLevelComment_id",
    "snippet_topLevelComment_kind",
    "snippet_topLevelComment_snippet_authorChannelId_value",
    "snippet_topLevelComment_snippet_authorChannelUrl",
    "snippet_topLevelComment_snippet_authorDisplayName",
    "snippet_topLevelComment_snippet_authorProfileImageUrl",
    "snippet_topLevelComment_snippet_canRate",
    "snippet_topLevelComment_snippet_channelId",
    "snippet_topLevelComment_snippet_likeCount",
    "snippet_topLevelComment_snippet_publishedAt",
    "snippet_topLevelComment_snippet_updatedAt",
    "snippet_topLevelComment_snippet_textDisplay",
    "snippet_topLevelComment_snippet_textOriginal",
    "snippet_topLevelComment_snippet_videoId",
    "snippet_topLevelComment_snippet_viewerRating",
    "snippet_totalReplyCount",
    "snippet_publishedAt",
    "snippet_updatedAt",
    "snippet_videoId",
    "snippet_viewerRating",
    "video_info_contentDetails_caption",
    "video_info_contentDetails_contentRating",
    "video_info_contentDetails_definition",
    "video_info_contentDetails_dimension",
    "video_info_contentDetails_duration",
    "video_info_contentDetails_licensedContent",
    "video_info_contentDetails_regionRestriction_blocked",
    "video_info_snippet_categoryId",
    "video_info_snippet_channelId",
    "video_info_snippet_channelTitle",
    "video_info_snippet_title",
    "video_info_snippet_description",
    "video_info_snippet_defaultAudioLanguage",
    "video_info_snippet_defaultLanguage",
    "video_info_snippet_liveBroadcastContent",
    "video_info_snippet_publishedAt",
    "video_info_snippet_tags",
    "video_info_snippet_thumbnails_default_height",
    "video_info_snippet_thumbnails_default_url",
    "video_info_snippet_thumbnails_default_width",
    "video_info_statistics_commentCount",
    "video_info_statistics_favoriteCount",
    "video_info_statistics_likeCount",
    "video_info_statistics_viewCount",
    "date",
    "date_utc",
    "date_utc_str",
]
