"""
This module contains a Cloud Function that is triggered by a Cloud Pub/Sub message, 
processes data retrieved from a MongoDB collection, and inserts the transformed 
data into a target MongoDB collection.
"""

import base64
import re
import json
from bson import ObjectId
from cloudevents.http import CloudEvent
import functions_framework
import numpy as np
import pandas as pd
from pymongo.collection import Collection
from data_transformer.const import (
    DATA_TRANSFORMER_OFFSET_METADATA_KEY,
    RAC_VOLUME_METADATA_KEY,
    RAC_TRANSFORM_VOLUME_METADATA_KEY,
    SEQUENCE_COORDINATOR_TOPIC_ID,
    BASE_LOOPBACK_THRESHOLD,
    CHUNK_SIZE,
    DATA_TRANSFORMER_TOPIC_ID,
    PROJECT_ID,
    QUERY_COLLECTION_NAME,
    RAC_TRANSFORM_COLLECTION_SUFFIX,
    COLUMNS_TO_FLATTEN,
    DATA_UNIFIER_COLUMNS,
    RAC_COLLECTION_NAME_METADATA_KEY,
    RAC_TRANSFORM_COLLECTION_NAME_METADATA_KEY,
    RAC_QUERY_PROJECTION,
    QUERY_PROJECTION_ATTRIBUTES,
    YT_COMMENTS_COLUMNS_TO_RETAIN,
    YT_COMMENT_COL_PREFIXES,
)
from utils.common_utils import (
    fetch_collection_data,
    get_query_config,
    update_query_metadata,
)
from utils.logger import logger
from utils.mongo_db import get_mongodb_client, get_mongodb_collection, get_mongodb_db
from utils.pubsub_publisher import publish_pubsub_message
from utils.utilities import (
    CONSUMER_TYPE,
    DataSourceId,
    DiagnosticActionType,
    DiagnosticStatus,
    MetaContainer,
    OrganizationAccountInfo,
)

meta_container = MetaContainer()


def complete_data_transformation(query_id: str) -> None:
    """
    Completes the data transformation process by sending a diagnostic
    message for completion.

    Parameters:
    - query_id (str): The ID of the query for which the process is completed.

    Returns:
    - None

    Exception Handling:
    - None
    """
    message = f"Capabilities data transformer script completed successfully for query_id: {query_id}"
    meta_container.send_diagnostic(
        DiagnosticActionType.UPDATE.value,
        DiagnosticStatus.COMPLETED.value,
        message,
    )


def filter_duplicates(
    collection: Collection, df: pd.DataFrame, query_id: str
) -> pd.DataFrame:
    """
    Filters out any '_id' values from the input DataFrame that already exist
    in the MongoDB collection.

    Parameters:
    - collection (pymongo.collection.Collection): The MongoDB collection
        object to check for duplicates.
    - df (pd.DataFrame): The DataFrame containing the records.
    - query_id (str): The string representation of the query_id.

    Returns:
    - pd.DataFrame: A filtered DataFrame without duplicates found in the MongoDB collection.

    Exception Handling:
    - Exception: Raised if any error occurs while filtering duplicates.
    """
    try:
        # Convert _id values to ObjectId if they are not already ObjectId
        ids_to_check = [ObjectId(_id) for _id in df["_id"]]

        # Query to find existing IDs in MongoDB collection
        query = {"_id": {"$in": ids_to_check}}

        # Use MongoDB's distinct to fetch existing id values and convert them to strings
        existing_ids = {str(id) for id in collection.distinct("_id", query)}

        # Ensure DataFrame _id values are in string format for comparison and filter out existing IDs
        filtered_df = df[~df["_id"].astype(str).isin(existing_ids)]

        # Log duplicate information if found
        duplicate_count = len(existing_ids)
        if duplicate_count > 0:
            logger.warning(
                "Skipping %d duplicate ID(s) found in '%s' collection for '%s' query ID.",
                duplicate_count,
                collection.name,
                query_id,
            )

        return filtered_df

    except Exception as e:
        raise Exception("Error while filtering out duplicates: %s", e) from e


def filter_dataframe(df: pd.DataFrame, data_source_id: str) -> None:
    """
    Filters a DataFrame based on specified columns to retain for particular data source.

    Parameters:
    - df (pd.DataFrame): The input DataFrame to filter.
    - data_source_id (str): The identifier of the data source.

    Returns:
    - None

    Exception Handling:
    - None
    """
    # Perform filtering by `data_source_id` only if it matches a specific value
    if data_source_id == DataSourceId.YT_COMMENTS.value:

        # Identify columns to retain including those with any of the specified prefixes
        columns_to_keep = set(YT_COMMENTS_COLUMNS_TO_RETAIN)
        if YT_COMMENT_COL_PREFIXES:
            columns_to_keep.update(
                col
                for col in df.columns
                if any(col.startswith(prefix) for prefix in YT_COMMENT_COL_PREFIXES)
            )

        # Identify missing columns
        missing_columns = [col for col in columns_to_keep if col not in df.columns]

        # Add missing columns and set them to None
        df[missing_columns] = None

        # Determine extra columns to drop
        columns_to_drop = df.columns.difference(columns_to_keep)
        df.drop(columns=columns_to_drop, inplace=True)


def flatten_dataframe_columns(
    df: pd.DataFrame, columns: list, sep: str = ".", keep_original: bool = False
) -> pd.DataFrame:
    """
    Flattens specified nested columns in a DataFrame and returns updated DataFrame.

    Parameters:
    - df (pandas.DataFrame): The DataFrame containing the data to be flattened.
    - columns (list): List of column names to be flattened. These columns should contain dictionaries.
    - sep (str, optional, default='.'): The separator used for naming the flattened columns.
    - keep_original (bool, optional, default=False):
        - If True, the original nested columns are retained in the DataFrame.
        - If False, the original nested columns are dropped after flattening.

    Returns
    - pd.DataFrame: The modified DataFrame with flattened columns.

    Exception Handling:
    - None
    """
    # Filter out columns that don't exist in the DataFrame
    existing_columns = [col for col in columns if col in df.columns]

    for column in existing_columns:

        # Handle missing or empty data in the column
        df[column] = df[column].apply(lambda x: x if isinstance(x, dict) else {})

        # Flatten the specified column
        flattened = pd.json_normalize(df[column], sep=sep)

        # Create a rename dictionary for the flattened columns to include the original column name as a prefix
        rename_dict = {col: f"{column}{sep}{col}" for col in flattened.columns}

        # Rename the flattened columns using pandas rename
        flattened = flattened.rename(columns=rename_dict)

        # Concatenate the flattened columns with the original DataFrame
        df = pd.concat([df, flattened], axis=1)

        # Optionally drop the original nested column
        if not keep_original:
            df.drop(columns=[column], inplace=True)

    return df


def rename_columns_for_bigquery(df: pd.DataFrame) -> None:
    """
    Cleans and standardizes column names in a DataFrame to make them compatible with BigQuery.
    - Replaces invalid characters with underscores.
    - Ensures the column name starts with a letter or underscore.
    - Truncates names to a maximum of 300 characters.

    Parameters:
    - df (pd.DataFrame): The DataFrame whose columns need to be renamed.

    Returns:
    - None

    Exception Handling:
    - Exception: Raised if an error occurs while renaming columns.
    """

    def clean_column_name(name: str) -> str:
        """
        Cleans a column name by replacing invalid characters, ensuring it starts with a valid character,
        and truncating to 300 characters.

        Parameters:
        - name (str): The original column name to be cleaned.

        Returns:
        - str: The cleaned and standardized column name.

        Exception Handling:
        - None
        """
        # Replace invalid characters with underscores
        name = re.sub(r"[^a-zA-Z0-9_]", "_", name)

        # Ensure the name starts with a letter or underscore
        if not re.match(r"^[a-zA-Z_]", name):
            name = "_" + name

        # Truncate the name to 300 characters if necessary
        name = name[:300]

        return name

    try:
        # Create a dictionary mapping original names to cleaned names
        new_column_names = {col: clean_column_name(col) for col in df.columns}

        # Rename the columns
        df.rename(columns=new_column_names, inplace=True)

    except Exception as e:
        message = f"An error occurred while renaming columns: {str(e)}"
        raise Exception(message) from e


def rename_conflicting_columns(df: pd.DataFrame, unifier_columns: set) -> None:
    """
    Renames columns in a DataFrame that conflict with a set of unifier column names.

    - The function checks for column names that are similar to the unifier column names
    (case-insensitive) and renames them by appending a numerical suffix (e.g., '_1', '_2').
    - This ensures that all column names in the DataFrame are unique and suitable for
    insertion into BigQuery.

    Parameters:
    - df (pd.DataFrame): The DataFrame whose columns need to be renamed.
    - unifier_columns (set): A set of unifier column names (in their intended
        lowercase form) that should not be renamed.

    Returns:
    - None

    Exception Handling:
    - Exception: Raised if an error occurs while renaming conflicting columns.
    """
    try:
        # Convert unifier columns to lowercase set for fast lookup
        unifier_columns_lower = {col.lower() for col in unifier_columns}

        # Keep track of counts for suffixes
        column_suffix_count = {}

        # Create a dictionary to hold renamed columns
        renamed_columns = {}
        for col in df.columns:
            col_lower = col.lower()

            # Check if the column name conflicts with the unifier columns
            if col_lower in unifier_columns_lower and col_lower != col:
                # Initialize or increment suffix counter
                column_suffix_count[col_lower] = (
                    column_suffix_count.get(col_lower, 0) + 1
                )

                # Create a new unique column name with a numerical suffix
                new_col_name = f"{col}_{column_suffix_count[col_lower]}"
                renamed_columns[col] = new_col_name

        # Rename the columns in the DataFrame
        df.rename(columns=renamed_columns, inplace=True)

    except Exception as e:
        message = f"An error occurred while renaming conflicting columns: {str(e)}"
        raise Exception(message) from e


def process_dataframe(df: pd.DataFrame) -> None:
    """
    Processes a pandas DataFrame by applying the following transformations:
    - Converts lists of phrases in the "Phrase" column into comma-separated strings.
    - Handles date transformations to generate UTC-formatted strings and extracts date parts.
    - Replaces null-like values (e.g., NaN, NA, NaT) with None.

    Parameters:
    - df (pd.DataFrame): The input DataFrame to process.

    Returns:
    - None

    Exception Handling:
    - None
    """
    # Convert list of phrases to comma-separated strings
    if "Phrase" in df.columns:
        df["Phrase"] = df["Phrase"].apply(
            lambda x: (
                ", ".join(x)
                if isinstance(x, list)
                else (x if isinstance(x, str) else "")
            )
        )

    # Transform date columns
    if "date" in df.columns:
        # Convert the 'date' column to datetime, handling any errors by converting invalid values to NaT
        df["date"] = pd.to_datetime(df["date"], errors="coerce")

        df["date_utc_str"] = df["date"].dt.strftime("%Y-%m-%dT%H:%M:%SZ")
        df["date_utc"] = df["date"].dt.strftime("%Y-%m-%d")

    # Replace null-like values in df with None
    df.replace(
        {
            np.nan: None,
            pd.NA: None,
            pd.NaT: None,
        },
        inplace=True,
    )


def insert_dataframe_into_collection(
    collection: Collection,
    query_id: str,
    df: pd.DataFrame,
) -> bool:
    """
    Insert a pandas DataFrame into a MongoDB collection.

    Parameters:
    - collection (pymongo.collection.Collection): The MongoDB collection.
    - query_id (str): The string representation of the query_id.
    - df (pandas.DataFrame): The DataFrame containing the data to be inserted.

    Returns:
    - bool: True if data insertion is successful, False otherwise.

    Exception Handling:
    - Exception: If any error occurs during the insertion process, the error message is sent to the diagnostic engine.
    """
    try:
        data = df.to_dict(orient="records")
        collection.insert_many(data)
        logger.info(
            "Successfully inserted '%d' record(s) into the '%s' collection for query ID '%s'.",
            len(df),
            collection.name,
            query_id,
        )

        return True

    except Exception as e:
        message = f"An error occurred during data insertion for '{query_id}' query_id in '{collection.name}' collection: {str(e)}"
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.FAILED.value,
            message,
        )
        return False


def update_offset_and_publish(
    offset: int,
    total_documents: int,
    loopback_threshold: int,
    payload: dict,
    batch_count: int,
    query_collection: Collection,
    query_id: str,
) -> int:
    """
    Updates the offset based on the number of documents fetched,
    and publishes a Pub/Sub message if necessary.

    Parameters:
    - offset (int): The current offset.
    - total_documents (int): The total number of documents in collection.
    - loopback_threshold (int): Threshold for the loopback.
    - payload (dict): The data payload to be published.
    - batch_count (int): The number of fetched documents.
    - query_collection (pymongo.collection.Collection): The MongoDB query collection.
    - query_id (str) : query ID for the query

    Returns:
    - int: The new offset.

    Exception Handling:
    - None
    """
    # Calculate the new offset
    new_offset = offset + batch_count

    # Update the query metadata with the new offset
    update_query_metadata(
        query_collection,
        query_id,
        DATA_TRANSFORMER_OFFSET_METADATA_KEY,
        new_offset,
    )

    # Publish a Pub/Sub message if there are more documents to process
    if loopback_threshold <= new_offset < total_documents:
        publish_pubsub_message(PROJECT_ID, DATA_TRANSFORMER_TOPIC_ID, payload)
        logger.info(
            "Published loopback message for offset %d out of %d",
            new_offset,
            total_documents,
        )

    return new_offset


# Triggered from a message on a Cloud Pub/Sub topic.
@functions_framework.cloud_event
def main(cloud_event: CloudEvent):
    """
    Cloud Function triggered from a message on a Cloud Pub/Sub topic.

    - This function is triggered by a Cloud Pub/Sub message and performs data fetch from
        MongoDB RAC collection, process data, and then inserts into RAC transform
        collection based on the provided payload.

    Steps:
    - Decode the cloud event payload to extract relevant data.
    - Retrieve organization and query information from the payload.
    - Toggle diagnostic status to 'PENDING'.
    - Retrieve MongoDB connection details from the organization vault using the
        organization ID.
    - Fetch the query configuration from the MongoDB database.
    - Retrieve or create the RAC transform collection name from query metadata.
    - Process data in chunks while the offset is less than the loopback threshold:
        - Fetch query data in chunks using the provided offset and chunk size.
        - Flatten specified nested columns in the DataFrame.
        - Renames columns in a DataFrame that conflict with a set of unifier column names.
        - Clean and standardize column names to be compatible with BigQuery.
        - Filter the DataFrame to retain only specified columns for the data source.
        - Filters out any '_id' values from the input DataFrame that already exist in
            the MongoDB collection.
        - Insert the transformed DataFrame into the MongoDB collection.
        - If there are more documents to process (new_offset >= loopback_threshold),
            loop back with the new offset by sending pub sub message to data transformer topic id.
        - If all documents are processed (new_offset equals total_documents), send a
            message to the sequence coordinator Pub/Sub topic.
        - Toggle diagnostic status to 'COMPLETED'.
    - If an error occurs during the transformation, toggle diagnostic status to 'FAILED'.

    Parameters:
    - cloud_event (CloudEvent): The CloudEvent object representing the incoming event.

    Returns:
    - tuple: A success message and the corresponding HTTP status code.

    Exception Handling:
    - Exception: If any error occurs during the data transformation process.
    """
    mongodb_client = None
    try:
        payload = json.loads(
            base64.b64decode(cloud_event.data["message"]["data"]).decode("utf-8")
        )

        logger.info("Message received successfully: %s", payload)
        payload["publisher_type"] = CONSUMER_TYPE
        meta_container.set_payload_info(payload)

        organization_id = payload["organization_id"]
        query_id = payload["query_id"]
        data_source_id = payload["data_source_id"]

        message = f"Capabilities data transformer script started successfully for query_id: {query_id}"
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.PENDING.value,
            message,
        )

        org_account_info = OrganizationAccountInfo(organization_id)
        mongodb_url = org_account_info.mongodb_url
        organization_db_name = org_account_info.organization_db_name

        mongodb_client = get_mongodb_client(mongodb_url)
        organization_db = get_mongodb_db(mongodb_client, organization_db_name)

        query_collection = get_mongodb_collection(
            organization_db, QUERY_COLLECTION_NAME
        )

        query_config = get_query_config(
            query_collection, query_id, QUERY_PROJECTION_ATTRIBUTES
        )

        if not query_config:
            logger.warning("No configuration found for query_id: '%s'", query_id)
            return

        query_meta_data = query_config["meta_data"]
        query_meta_data = (
            query_meta_data[0] if isinstance(query_meta_data, list) else query_meta_data
        )

        offset = query_meta_data.get(DATA_TRANSFORMER_OFFSET_METADATA_KEY, 0)
        rac_collection_name = query_meta_data.get(RAC_COLLECTION_NAME_METADATA_KEY)
        rac_transform_collection_name = query_meta_data.get(
            RAC_TRANSFORM_COLLECTION_NAME_METADATA_KEY
        )

        if not rac_transform_collection_name:
            # Set the collection name in query metadata
            rac_transform_collection_name = (
                f"{rac_collection_name}_{RAC_TRANSFORM_COLLECTION_SUFFIX}"
            )
            update_query_metadata(
                query_collection,
                query_id,
                RAC_TRANSFORM_COLLECTION_NAME_METADATA_KEY,
                rac_transform_collection_name,
            )

        rac_collection = get_mongodb_collection(organization_db, rac_collection_name)
        rac_transform_collection = get_mongodb_collection(
            organization_db, rac_transform_collection_name
        )

        # Retrieve the RAC collection volume
        total_documents = (
            query_meta_data.get(RAC_VOLUME_METADATA_KEY)
            or rac_collection.estimated_document_count()
        )

        loopback_threshold = offset + int(BASE_LOOPBACK_THRESHOLD)

        while offset < loopback_threshold:
            rac_df = fetch_collection_data(
                rac_collection, {}, RAC_QUERY_PROJECTION, offset, int(CHUNK_SIZE)
            )
            batch_count = len(rac_df)
            rac_df = flatten_dataframe_columns(rac_df, COLUMNS_TO_FLATTEN)
            rename_conflicting_columns(rac_df, DATA_UNIFIER_COLUMNS)
            rename_columns_for_bigquery(rac_df)
            process_dataframe(rac_df)
            filter_dataframe(rac_df, data_source_id)
            rac_df = filter_duplicates(rac_transform_collection, rac_df, query_id)

            if not rac_df.empty:
                success = insert_dataframe_into_collection(
                    rac_transform_collection, query_id, rac_df
                )
                if success:
                    offset = update_offset_and_publish(
                        offset,
                        total_documents,
                        loopback_threshold,
                        payload,
                        batch_count,
                        query_collection,
                        query_id,
                    )
                    if offset == total_documents:
                        # Update the query metadata with rac transform volume
                        rac_transform_volume = rac_transform_collection.count_documents(
                            {}, hint="_id_"
                        )
                        update_query_metadata(
                            query_collection,
                            query_id,
                            RAC_TRANSFORM_VOLUME_METADATA_KEY,
                            rac_transform_volume,
                        )
                        publish_pubsub_message(
                            PROJECT_ID, SEQUENCE_COORDINATOR_TOPIC_ID, payload
                        )
                        complete_data_transformation(query_id)
                        break
                else:
                    break

            else:
                if total_documents == rac_transform_collection.count_documents(
                    {}, hint="_id_"
                ):
                    # Update the query metadata with the new offset
                    update_query_metadata(
                        query_collection,
                        query_id,
                        DATA_TRANSFORMER_OFFSET_METADATA_KEY,
                        total_documents,
                    )
                    complete_data_transformation(query_id)
                    break

                # Handling the case where all records in the batch are duplicates
                if batch_count:
                    offset = update_offset_and_publish(
                        offset,
                        total_documents,
                        loopback_threshold,
                        payload,
                        batch_count,
                        query_collection,
                        query_id,
                    )
                else:
                    break

        mongodb_client.close()
        return "Success", 200

    except Exception as e:
        message = f"An error occurred during capabilities data transformer: {e}"
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.FAILED.value,
            message,
            str(type(e)),
        )

    finally:
        if mongodb_client is not None:
            mongodb_client.close()
