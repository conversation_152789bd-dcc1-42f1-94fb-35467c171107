"""
This module contains Mandatory parameters used in Onboarding consumer.
"""

# onboarding organization parameters
ORGANIZATION_NAME = ""
ORGANIZATION_DOMAIN = ""
ORG_MONTHLY_DATA_POINT_LIMIT = -1  # No limit (-1 indicates unlimited, update as needed)
ORG_MONTHLY_QUERY_LIMIT = -1  # No limit (-1 indicates unlimited, update as needed)

# Onboarding user parameters
USER_EMAIL = ""
FIRST_NAME = ""
LAST_NAME = ""

# Needs to be created while onboarding a user
YOUTUBE_API_KEY = ""
CRED_NAME = FIRST_NAME + "_" + LAST_NAME + "_YouTube_cred"

DATASOURCES = ["YouTube Comments", "File Upload"]

# Generative AI API keys: Add API keys while onboarding new organization for each consumer type
GEN_AI_CREDS = {
    "SENTIMENT": "",
    "SENTIMENT_MULTI": "",
    "SUMMARIZATION": "",
    "CATEGORIZATION_TOOL": "",
}

# OPTIONAL/DEFUALT PARAMETERS
IS_ADMIN = False

# Specifies if the organization's database should be hosted within our system's MongoDB instance (True) or externally (False).
USE_INTERNAL_MONGODB = True

# Indicates whether the client API is enabled for the organization (True for enabled, False for disabled)
IS_CLIENT_API_ENABLED = False

IS_CATEGORIZATION_ENABLED = True

SHOW_ANALYSIS = True