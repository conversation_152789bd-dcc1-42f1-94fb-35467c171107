"""
This module contains constants used in files of Onboarding consumer.
"""

import os
from bson import ObjectId

os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = ""  # path to service account

RESPONSE_TIMEOUT = 300  # 5 mins

ATLAS_API_ENDPOINT = os.getenv("ATLAS_API_ENDPOINT")
ATLAS_API_PUBLIC_KEY = os.getenv("ATLAS_API_PUBLIC_KEY")
ATLAS_API_PRIVATE_KEY = os.getenv("ATLAS_API_PRIVATE_KEY")
ATLAS_PROJECT_ID = os.getenv("ATLAS_PROJECT_ID")

CLIENT_API_MONGODB_URL = os.getenv("CLIENT_API_MONGODB_URL")
PLATFORM_MONGODB_URL = os.getenv("PLATFORM_MONGODB_URL")
ORGANIZATION_MONGODB_URL = os.getenv("ORGANIZATION_MONGODB_URL")

PROJECT_ID = os.getenv("PROJECT_ID")
CAPABILITIES_DATABASE = os.getenv("CAPABILITIES_DATABASE")
ORG_CREDS_COLLECTION = os.getenv("ORG_CREDS_COLLECTION")
ORG_MONGO_URI_HOSTNAME = os.getenv("ORG_MONGO_URI_HOSTNAME")
ORGANIZATION_DOMAIN_INFO_COLLECTION = os.getenv("ORGANIZATION_DOMAIN_INFO_COLLECTION")
ORG_QUOTA_CONFIG_COLLECTION = os.getenv("ORG_QUOTA_CONFIG_COLLECTION")
ORGANIZATION_VAULT_COLLECTION = os.getenv("ORGANIZATION_VAULT_COLLECTION")
SERVICE_ACCOUNT_EMAIL = os.getenv("SERVICE_ACCOUNT_EMAIL")
USER_CREDS_COLLECTION = os.getenv("USER_CREDS_COLLECTION")
USER_INFO_COLLECTION = os.getenv("USER_INFO_COLLECTION")

DATA_SOURCE_OBJECT_ID = {
    "YouTube Comments": ObjectId("66211db016389fb92ea7c1b2"),
    "File Upload": ObjectId("66067d884469a10686feff37"),
    "Database": ObjectId("665d973539608b9aa9f05a8f"),
    "Sprinklr API": ObjectId("6605b690df8ad607995e9a97"),
}
HYDRATION_BUCKET_PREFIX = "hydration_bucket"
HYDRATION_TEMP_BUCKET_PREFIX = "hydration_bucket_temp"
QUERY_ARCHIVE_BUCKET_PREFIX = "query_archive_bucket"
DATA_EXPORT_BUCKET_PREFIX = "data_export_bucket"
MODEL_NAME = "gemini-1.5-flash-002"
MONGODB_URI_TEMPLATE = "mongodb+srv://{username}:{password}@{org_mongo_uri_hostname}"
BIG_QUERY_SCHEMA_META_DATA_KEY = "BIG_QUERY_SCHEMA"
IS_BIG_QUERY_ENABLED_META_DATA_KEY = "IS_BIG_QUERY_ENABLED"
IS_CLIENT_API_ENABLED_META_DATA_KEY = "IS_CLIENT_API_ENABLED"
IS_CATEGORIZATION_ENABLED_META_DATA_KEY = "IS_CATEGORIZATION_ENABLED"
SHOW_ANALYSIS_META_DATA_KEY = "SHOW_ANALYSIS"

YOUTUBE_BIG_QUERY_SCHEMA = {
    "_id": "STRING",
    "kind": "STRING",
    "etag": "STRING",
    "id": "STRING",
    "record_type": "STRING",
    "author": "STRING",
    "author_profile_image_url": "STRING",
    "author_channel_url": "STRING",
    "like_count": "STRING",
    "reply_count": "STRING",
    "comment_id": "STRING",
    "text": "STRING",
    "video_id": "STRING",
    "BODY1": "STRING",
    "EMOJIS": "STRING",
    "EMOJIS_Unique": "STRING",
    "EMOJIS_Unique_Count": "INTEGER",
    "Hashtag": "STRING",
    "Hashtag_Position": "FLOAT",
    "Hashtag_Unique": "STRING",
    "Hashtag_Unique_Count": "INTEGER",
    "Keyword": "STRING",
    "Lemitized": "STRING",
    "Phrase": "STRING",
    "Stories": "INTEGER",
    "Themes": "INTEGER",
    "Unique_Cluster_ID": "INTEGER",
    "Unique_Story_ID": "INTEGER",
    "cluster_id": "INTEGER",
    "encapsulation_marker": "STRING",
    "cluster_summary": "STRING",
    "cluster_sentiment": "STRING",
    "cluster_sentiment_reasoning": "STRING",
    "story_summary": "STRING",
    "story_sentiment": "STRING",
    "theme_summary": "STRING",
    "theme_sentiment": "STRING",
    "story_sentiment_reasoning": "STRING",
    "theme_sentiment_reasoning": "STRING",
    "replies": "STRING",
    "snippet_authorChannelId_value": "STRING",
    "snippet_authorChannelUrl": "STRING",
    "snippet_authorDisplayName": "STRING",
    "snippet_authorProfileImageUrl": "STRING",
    "snippet_canRate": "BOOLEAN",
    "snippet_canReply": "BOOLEAN",
    "snippet_channelId": "STRING",
    "snippet_isPublic": "BOOLEAN",
    "snippet_likeCount": "STRING",
    "snippet_parentId": "STRING",
    "snippet_textDisplay": "STRING",
    "snippet_textOriginal": "STRING",
    "snippet_topLevelComment_etag": "STRING",
    "snippet_topLevelComment_id": "STRING",
    "snippet_topLevelComment_kind": "STRING",
    "snippet_topLevelComment_snippet_authorChannelId_value": "STRING",
    "snippet_topLevelComment_snippet_authorChannelUrl": "STRING",
    "snippet_topLevelComment_snippet_authorDisplayName": "STRING",
    "snippet_topLevelComment_snippet_authorProfileImageUrl": "STRING",
    "snippet_topLevelComment_snippet_canRate": "BOOLEAN",
    "snippet_topLevelComment_snippet_channelId": "STRING",
    "snippet_topLevelComment_snippet_likeCount": "INTEGER",
    "snippet_topLevelComment_snippet_publishedAt": "STRING",
    "snippet_topLevelComment_snippet_updatedAt": "STRING",
    "snippet_topLevelComment_snippet_textDisplay": "STRING",
    "snippet_topLevelComment_snippet_textOriginal": "STRING",
    "snippet_topLevelComment_snippet_videoId": "STRING",
    "snippet_topLevelComment_snippet_viewerRating": "STRING",
    "snippet_totalReplyCount": "INTEGER",
    "snippet_publishedAt": "STRING",
    "snippet_updatedAt": "STRING",
    "snippet_videoId": "STRING",
    "snippet_viewerRating": "STRING",
    "video_info_contentDetails_caption": "STRING",
    "video_info_contentDetails_contentRating": "STRING",
    "video_info_contentDetails_definition": "STRING",
    "video_info_contentDetails_dimension": "STRING",
    "video_info_contentDetails_duration": "STRING",
    "video_info_contentDetails_licensedContent": "BOOLEAN",
    "video_info_contentDetails_regionRestriction_blocked": "STRING",
    "video_info_snippet_categoryId": "STRING",
    "video_info_snippet_channelId": "STRING",
    "video_info_snippet_channelTitle": "STRING",
    "video_info_snippet_title": "STRING",
    "video_info_snippet_description": "STRING",
    "video_info_snippet_defaultAudioLanguage": "STRING",
    "video_info_snippet_defaultLanguage": "STRING",
    "video_info_snippet_liveBroadcastContent": "STRING",
    "video_info_snippet_publishedAt": "STRING",
    "video_info_snippet_tags": "STRING",
    "video_info_snippet_thumbnails_default_height": "STRING",
    "video_info_snippet_thumbnails_default_url": "STRING",
    "video_info_snippet_thumbnails_default_width": "STRING",
    "video_info_statistics_commentCount": "INTEGER",
    "video_info_statistics_favoriteCount": "INTEGER",
    "video_info_statistics_likeCount": "INTEGER",
    "video_info_statistics_viewCount": "INTEGER",
    "date": ["TIMESTAMP", "DATETIME"],
    "date_utc": "DATE",
    "date_utc_str": "STRING",
}

FILE_UPLOAD_BIG_QUERY_SCHEMA = {
    "Unique_Story_ID": "INTEGER",
    "BODY1": "STRING",
    "text": "STRING",
    "cluster_sentiment": "STRING",
    "EMOJIS_Unique_Count": "INTEGER",
    "Lemitized": "STRING",
    "theme_summary": "STRING",
    "cluster_summary": "STRING",
    "Stories": "INTEGER",
    "date_utc": "DATE",
    "cluster_sentiment_reasoning": "STRING",
    "Hashtag_Position": "FLOAT",
    "Keyword": "STRING",
    "Hashtag_Unique": "STRING",
    "Hashtag": "STRING",
    "Themes": "INTEGER",
    "date_utc_str": "STRING",
    "Unique_Cluster_ID": "INTEGER",
    "encapsulation_marker": "STRING",
    "story_summary": "STRING",
    "Phrase": "STRING",
    "Hashtag_Unique_Count": "INTEGER",
    "EMOJIS": "STRING",
    "date": ["TIMESTAMP", "DATETIME"],
    "id": "STRING",
    "_id": "STRING",
    "cluster_id": "INTEGER",
    "EMOJIS_Unique": "STRING",
}
