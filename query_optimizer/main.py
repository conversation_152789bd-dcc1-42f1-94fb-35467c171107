"""
This module implements a system for processing encapsulation markers and query optimization 
tasks in a distributed environment.

The module defines the main execution function that coordinates the entire process of:
- Parsing incoming events and payloads.
- Managing organization-specific details and configurations.
- Handling diagnostic messages, including setting the status of encapsulation markers.
- Managing and processing contexts (cluster, story, or theme) and ensuring the query 
    optimization is completed.
- Monitoring the status of query-related data, including RAC counts (total, original, 
    share, replies, and quotes).
- Updating processing dates and context information.
- Coordinating communication with other system components, such as the Query Optimizer 
    Pub/Sub topic and the sequence coordinator.
"""

from typing import Literal
from concurrent.futures import ThreadPoolExecutor
import json
import base64
from bson import ObjectId
import functions_framework
from pymongo import UpdateMany, errors
from pymongo.collection import Collection
from pymongo.cursor import Cursor
import pandas as pd
from query_optimizer.const import (
    CONTEXT_DATA_COLLECTION_NAME,
    QUERY_COLLECTION_NAME,
    PROJECT_ID,
    QUERY_OPTIMIZER_TOPIC_ID,
    SEQUENCE_COORDINATOR_TOPIC_ID,
)
from utils.common_utils import (
    check_marker_status,
    check_query_status,
    get_query_config,
    toggle_marker_status,
)
from utils.logger import logger
from utils.pubsub_publisher import publish_pubsub_message
from utils.mongo_db import get_mongodb_client, get_mongodb_collection, get_mongodb_db
from utils.utilities import (
    DiagnosticActionType,
    DiagnosticStatus,
    EncapsulationMarkerStatus,
    EncapsulationMarkerStatusColumn,
    MetaContainer,
    OrganizationAccountInfo,
    SummaryContext,
    CONSUMER_TYPE,
)


def get_rac_counts(
    collection: Collection,
) -> tuple[int, int, Literal[0], int, Literal[0], int]:
    """
    Counts the total number of documents in the specified collection.

    Parameters:
    - collection: The MongoDB collection object.

    Returns:
    - total_count: The total number of documents in the collection.
    - original_count: The
    - replies_count: The total replies count which is not none in the collection.
    - comment_count: The total comment count in the collection.

    Exception Handling:
    - None
    """
    total_count = 0
    original_count = 0
    share_count = 0
    replies_count = 0
    quote_count = 0
    like_count = 0

    replies_count = collection.count_documents({"record_type": "reply"})
    original_count = collection.count_documents({"record_type": "comment"})
    
    like_count_pipeline = [
        {"$group": {"_id": None, "total_likes": {"$sum": "$like_count"}}}
    ]
    like_count_result = list(collection.aggregate(like_count_pipeline))
    like_count = like_count_result[0]["total_likes"] if like_count_result else 0
    
    replies_count = replies_count if replies_count is not None else 0
    original_count = original_count if original_count is not None else 0

    total_count = collection.estimated_document_count()

    return (
        total_count,
        original_count,
        share_count,
        replies_count,
        quote_count,
        like_count,
    )


def get_context_data(
    rac_collection, encapsulation_marker, encapsulation_marker_id, query_id, context
):
    """Get the context data for cluster , story and theme and insert in context data collection

    Parameters:
        rac_collection (Collection): RAC collection of the query
        encapsulation_marker (str): encapsulation marker of the context
        encapsulation_marker_id (ObjectId): encapsulation marker id
        query_id (ObjectId): query id of the context
        context (str): context of the data (theme , story or cluster)

    Returns:
        result: result of the aggregation pipeline
    """
    pipeline = [
        {"$match": {"encapsulation_marker": encapsulation_marker}},
        {
            "$group": {
                "document_count": {"$sum": 1},
                f"{context}_summary": {"$first": f"${context}_summary"},
                "theme": {"$first": "$Themes"},
                "original_count": {
                    "$sum": {"$cond": [{"$eq": ["$record_type", "comment"]}, 1, 0]}
                },
                "replies_count": {
                    "$sum": {"$cond": [{"$eq": ["$record_type", "reply"]}, 1, 0]}
                },
                "like_count": {"$sum": "$like_count"},
            }
        },
        {
            "$group": {
                "theme": {"$first": "$theme"},
                f"{context}_summary": {"$first": f"${context}_summary"},
                "sentiments": {
                    "$push": {
                        "sentiment": "$_id.sentiment",
                        "count": {"$sum": "$document_count"},
                    }
                },
                "original_count": {"$sum": "$original_count"},
                "replies_count": {"$sum": "$replies_count"},
                "like_count": {"$sum": "$like_count"},
                "count": {"$sum": "$document_count"},
            }
        },
        {
            "$project": {
                "_id": 0,
                "query_id": ObjectId(query_id),
                "count": 1,
                "original_count": 1,
                "replies_count": 1,
                "like_count": 1,
                "encapsulation_marker": encapsulation_marker,
                "encapsulation_marker_id": encapsulation_marker_id,
                f"{context}_summary": 1,
                "context": context,
                "sentiments": 1,
            }
        },
    ]

    sentiment_group = pipeline[1]["$group"]
    context_group = pipeline[2]["$group"]
    project = pipeline[3]["$project"]

    match context:
        case SummaryContext.CLUSTER.value:
            sentiment_group["_id"] = {"Unique_Cluster_ID": "$Unique_Cluster_ID"}
            sentiment_group["story"] = {"$first": "$Stories"}
            sentiment_group["cluster"] = {"$first": "$cluster_id"}

            context_group["_id"] = "$_id.Unique_Cluster_ID"
            context_group["story"] = {"$first": "$story"}
            context_group["cluster"] = {"$first": "$cluster"}

            project["unique_cluster_id"] = "$_id"
            project["theme"], project["story"], project["cluster"] = 1, 1, 1

        case SummaryContext.STORY.value:
            sentiment_group["_id"] = {"Unique_Story_ID": "$Unique_Story_ID"}
            sentiment_group["story"] = {"$first": "$Stories"}
            # Add clusters to the group
            sentiment_group["clusters"] = {"$addToSet": "$Unique_Cluster_ID"}

            context_group["_id"] = "$_id.Unique_Story_ID"
            context_group["story"] = {"$first": "$story"}
            # Collect clusters at the context group level
            context_group["clusters"] = {"$first": "$clusters"}

            project["unique_story_id"] = "$_id"
            project["phrases"] = []
            project["theme"], project["story"] = 1, 1
            # Compute cluster_count directly from the clusters array
            project["cluster_count"] = {"$size": "$clusters"}

        case SummaryContext.THEME.value:
            sentiment_group["_id"] = {"Themes": "$Themes"}
            sentiment_group["sentiment_stories"] = {"$addToSet": "$Stories"}

            context_group["_id"] = "$_id.Themes"
            context_group["story_count"] = {"$addToSet": "$sentiment_stories"}

            project["theme"] = "$_id"
            project["keywords"] = []
            project["story_count"] = {
                "$size": {
                    "$reduce": {
                        "input": "$story_count",
                        "initialValue": [],
                        "in": {"$setUnion": {"$concatArrays": ["$$value", "$$this"]}},
                    }
                }
            }

    # TODO: Story and theme sentiment processing are currently disabled, as only cluster
    # sentiment is needed. Re-enable if additional sentiment processing
    # becomes necessary in the future.
    sentiment_group["_id"]["sentiment"] = f"${SummaryContext.CLUSTER.value}_sentiment"

    result = list(rac_collection.aggregate(pipeline))
    return result


def get_keywords_and_keyphrase(
    rac_collection: Collection, encapsulation_marker: str, context: str
) -> Cursor:
    """Get the keywords and keyphrase for theme and story respectively

    Parameters:
        rac_collection (Collection): RAC collection of the query
        encapsulation_marker (str): encapsulation marker of the context
        context (str): context of the data (theme or story)

    Returns:
        result (cursor): result of the pipeline
    """
    pipeline = []
    
    match context:
        case SummaryContext.STORY.value:
            pipeline = [
                {"$match": {"encapsulation_marker": encapsulation_marker}},
                {"$project": {"Themes": 1, "Stories": 1, "Phrase": 1}},
                {"$unwind": "$Phrase"},
                {
                    "$group": {
                        "_id": {
                            "theme": "$Themes",
                            "story": "$Stories",
                            "phrase": "$Phrase",
                        },
                        "count": {"$sum": 1},
                    }
                },
                {"$sort": {"_id.theme": 1, "_id.story": 1, "count": -1}},
                {
                    "$project": {
                        "phrase": "$_id.phrase",
                        "theme": "$_id.theme",
                        "story": "$_id.story",
                        "count": 1,
                    }
                },
            ]
            data = rac_collection.aggregate(pipeline)
            df = pd.DataFrame(data)

            # Group by theme and story, then get the top 5 phrases by count with their respective counts
            result = []
            if not df.empty:
                result = (
                    df.groupby(["theme", "story"])
                    .apply(
                        lambda x: x.nlargest(5, "count")[["phrase", "count"]]
                        .sort_values("count", ascending=False)
                        .to_dict("records")
                    )
                    .reset_index(name="phrases")
                )
                result = result.to_dict(orient="records")
            return result

        case SummaryContext.THEME.value:
            pipeline = [
                {"$match": {"encapsulation_marker": encapsulation_marker}},
                {
                    "$project": {
                        "Themes": 1,
                        "keywords": {"$setUnion": {"$split": ["$Keyword", " "]}},
                    }
                },
                {"$unwind": "$keywords"},
                {"$match": {"keywords": {"$ne": ""}}},
                {
                    "$group": {
                        "_id": {"theme": "$Themes", "keyword": "$keywords"},
                        "count": {"$sum": 1},
                    }
                },
                {"$sort": {"_id.theme": 1, "count": -1}},
                {
                    "$group": {
                        "_id": "$_id.theme",
                        "keywords": {
                            "$push": {"keyword": "$_id.keyword", "count": "$count"}
                        },
                    }
                },
                {
                    "$project": {
                        "keywords": {"$slice": ["$keywords", 5]},
                        "theme": "$_id",
                    }
                },
            ]

    result = rac_collection.aggregate(pipeline)
    return result


def get_next_context(current_context: str) -> str:
    """
    Returns the next context in the sequence or last context if at the last context.

    Parameters:
    - current_context (str): The current context.

    Returns:
    - next_context: The next context in sequence or last context if at the last context.

    Exception Handling:
    - None
    """
    # Convert the string to the corresponding enum member
    current_context = SummaryContext(current_context)

    # Define the list of enum members
    context_list = list(SummaryContext)

    # Check if the current context is the last one
    if current_context == SummaryContext.ALL_PROCESSED:
        return current_context.value

    # Find the index of the current context
    current_index = context_list.index(current_context)

    # Return the next context
    return context_list[current_index + 1].value


def find_overall_processing_context(
    collection: Collection, encapsulation_marker_id: str
) -> str:
    """
    Retrieves the overall processing context for a specific encapsulation marker.

    Parameters:
    - collection (Collection): The MongoDB collection object.
    - encapsulation_marker_id (str): The _id of the document to retrieve the context from.

    Returns:
    - str: The 'query_optimizer_context' value if found, otherwise None.

    Exception Handling:
    - None
    """
    document = collection.find_one(
        {"_id": ObjectId(encapsulation_marker_id)},
        {
            "_id": 0,
            "query_optimizer_context": 1,
        },
    )

    return document.get("query_optimizer_context", SummaryContext.CLUSTER.value)


def update_overall_processing_context(
    collection: Collection, encapsulation_marker_id: str, context: str
) -> None:
    """
    Updates the overall processing context for a specific encapsulation marker.

    Parameters:
    - collection (Collection): The MongoDB collection object.
    - encapsulation_marker_id (str): The _id of the document to update.
    - context (str): The context string to be added or updated in the
        'query_optimizer_context' field.

    Returns:
    - None

    Exception Handling:
    - None
    """
    collection.update_one(
        {"_id": ObjectId(encapsulation_marker_id)},
        {"$set": {"query_optimizer_context": context}},
    )


@functions_framework.cloud_event
def main(cloud_event):
    """This is main execution function which perform following steps
        - Parse the payload
        - Set the organization details for the organization id
        - Get query collection and query config.
        - Send diagnostic with `PENDING` status to the diagnostic engine.
        - Toggle encapsulation marker status to 'PENDING'.
        - Retrieve the context(cluster, story, or theme) for a specific encapsulation marker
            for which the query optimization is not done.
        - Get the rac count of total , original , share , replies and quotes count
        - Check for the start and end date and update if null
        - Set the context data
        - Get the next context(cluster, story, or theme) in the sequence.
        - Updates the overall processing context for a specific encapsulation marker.
        - Update encapsulation marker status based on the success or failure.
        - If context is not equals to `all_processed` for a particular encapsulation marker:
            - Publish a loopback message to a Query Optimizer Pub/Sub topic.
        - Else:
            - If all encapsulation markers are completed for a particular query, send message
                to sequence coordinator, and send diagnostic with `COMPLETED` status.
        - Log any errors encountered during the process for troubleshooting purposes.

    Parameters:
    - cloud_event (CloudEvent): The CloudEvent object representing the incoming event.

    Returns:
    - None

    Exception Handling:
    - Raises if any error occurred during capabilities query optimization process
    """
    mongodb_client = None
    try:
        meta_container = MetaContainer()

        payload = json.loads(
            base64.b64decode(cloud_event.data["message"]["data"]).decode("utf-8")
        )
        logger.info("Message received successfully: %s", payload)
        payload["publisher_type"] = CONSUMER_TYPE
        meta_container.set_payload_info(payload)

        organization_id = payload["organization_id"]
        query_id = payload["query_id"]
        encapsulation = payload["encapsulation"]
        encapsulation_marker = encapsulation["encapsulation_marker"]
        encapsulation_marker_id = encapsulation["_id"]
        record_count = encapsulation["record_count"]

        org_account_info = OrganizationAccountInfo(organization_id)
        mongodb_url = org_account_info.mongodb_url
        organization_db_name = org_account_info.organization_db_name

        mongodb_client = get_mongodb_client(mongodb_url)
        organization_db = get_mongodb_db(mongodb_client, organization_db_name)
        query_collection = get_mongodb_collection(
            organization_db, QUERY_COLLECTION_NAME
        )

        query = get_query_config(query_collection, ObjectId(query_id), {"_id": 0})

        if not query:
            logger.warning("No configuration found for query_id: '%s'", query_id)
            return

        meta_container.set_meta_data(query["meta_data"])
        meta_data = meta_container.meta_data
        encapsulation_marker_collection_name = meta_data.get(
            "ENCAPSULATION_MARKER_COLLECTION_NAME"
        )

        encapsulation_marker_collection = get_mongodb_collection(
            organization_db, encapsulation_marker_collection_name
        )

        rac_collection_name = meta_data.get("RAC_COLLECTION_NAME")
        rac_collection = get_mongodb_collection(organization_db, rac_collection_name)

        # Check if the encapsulation marker with the given query ID has been already completed
        is_marker_completed = check_marker_status(
            encapsulation_marker_collection,
            encapsulation_marker_id,
            query_id,
            EncapsulationMarkerStatusColumn.QUERY_OPTIMIZER_STATUS.value,
            EncapsulationMarkerStatus.COMPLETED.value,
        )

        if is_marker_completed:
            logger.warning(
                "'%s' encapsulation Marker with query ID '%s' and encapsulation marker ID '%s' is already completed.",
                encapsulation_marker,
                query_id,
                encapsulation_marker_id,
            )

            return "Success", 200

        message = f"Capabilities query optimizer script started successfully for query_id: {query_id}"
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.PENDING.value,
            message,
        )

        # Update the encapsulation marker status to 'PENDING'
        toggle_marker_status(
            encapsulation_marker_collection,
            encapsulation_marker_id,
            query_id,
            EncapsulationMarkerStatusColumn.QUERY_OPTIMIZER_STATUS.value,
            EncapsulationMarkerStatus.PENDING.value,
        )

        # Retrieve the context for a specific encapsulation marker for which the query optimization is not done
        context = find_overall_processing_context(
            encapsulation_marker_collection, encapsulation_marker_id
        )

        if context == SummaryContext.CLUSTER.value:
            (
                total_count,
                original_count,
                share_count,
                replies_count,
                quote_count,
                like_count,
            ) = get_rac_counts(rac_collection)
            update_fields = {
                "$set": {
                    "meta_data.TOTAL_COUNT": total_count,
                    "meta_data.ORIGINAL_COUNT": original_count,
                    "meta_data.SHARE_COUNT": share_count,
                    "meta_data.REPLIES_COUNT": replies_count,
                    "meta_data.QUOTE_COUNT": quote_count,
                    "meta_data.LIKE_COUNT": like_count,
                }
            }

            start_date = meta_data.get("START_DATETIME")
            end_date = meta_data.get("END_DATETIME")

            if start_date is None and end_date is None:
                query_result = encapsulation_marker_collection.aggregate(
                    [
                        {"$match": {"query_id": ObjectId(query_id)}},
                        {
                            "$group": {
                                "_id": None,
                                "min_date": {"$min": "$encapsulation_start_date"},
                                "max_date": {"$max": "$encapsulation_end_date"},
                            }
                        },
                    ]
                ).next()
                start_date = query_result["min_date"]
                end_date = query_result["max_date"]

                update_fields["$set"]["meta_data.START_DATETIME"] = start_date
                update_fields["$set"]["meta_data.END_DATETIME"] = end_date

            query_collection.update_one({"_id": ObjectId(query_id)}, update_fields)

        context_data_collection = get_mongodb_collection(
            organization_db, CONTEXT_DATA_COLLECTION_NAME
        )

        if record_count is None:
            record_count = rac_collection.count_documents(
                {"encapsulation_marker": encapsulation_marker}
            )

        if record_count:
            # Using ThreadPoolExecutor to run functions concurrently
            with ThreadPoolExecutor() as executor:
                context_data_future = executor.submit(
                    get_context_data,
                    rac_collection,
                    encapsulation_marker,
                    encapsulation_marker_id,
                    query_id,
                    context,
                )

                if context != SummaryContext.CLUSTER.value:
                    keywords_and_keyphrases_future = executor.submit(
                        get_keywords_and_keyphrase,
                        rac_collection,
                        encapsulation_marker,
                        context,
                    )

                # Retrieve results
                context_data = context_data_future.result()
                if context != SummaryContext.CLUSTER.value:
                    keywords_and_keyphrases = keywords_and_keyphrases_future.result()

            try:
                context_data_collection.insert_many(context_data, ordered=False)

            except errors.BulkWriteError as bwe:
                # Extract error details
                for error in bwe.details.get("writeErrors", []):
                    if error.get("code") == 11000:  # Duplicate key error (E11000)
                        logger.error("Duplicate Key Error: %s", {error["errmsg"]})
                    else:
                        logger.error("Other Write Error: %s", {error["errmsg"]})
                        raise

            if context != SummaryContext.CLUSTER.value:
                update_context_data = []
                for record in keywords_and_keyphrases:
                    filter_query = {
                        "encapsulation_marker": encapsulation_marker,
                        "theme": record["theme"],
                    }
                    update_fields = {"$set": {}}

                    if context == SummaryContext.THEME.value:
                        update_fields["$set"]["keywords"] = record["keywords"]

                    if context == SummaryContext.STORY.value:
                        filter_query["story"] = record["story"]
                        update_fields["$set"]["phrases"] = record["phrases"]

                    update_context_data.append(UpdateMany(filter_query, update_fields))

                if update_context_data:
                    context_data_collection.bulk_write(update_context_data)

        # Get the next context(cluster, story, or theme) in the sequence
        context = get_next_context(context)
        
        # Updates the overall processing context for a specific encapsulation marker.
        update_overall_processing_context(
            encapsulation_marker_collection, encapsulation_marker_id, context
        )

        if context != SummaryContext.ALL_PROCESSED.value:
            publish_pubsub_message(PROJECT_ID, QUERY_OPTIMIZER_TOPIC_ID, payload)
            logger.info(
                "Published loopback message for '%s encapsulation marker' with '%s' context",
                encapsulation_marker,
                context,
            )

        else:
            toggle_marker_status(
                encapsulation_marker_collection,
                encapsulation_marker_id,
                query_id,
                EncapsulationMarkerStatusColumn.QUERY_OPTIMIZER_STATUS.value,
                EncapsulationMarkerStatus.COMPLETED.value,
            )

            if check_query_status(
                encapsulation_marker_collection,
                query_id,
                EncapsulationMarkerStatusColumn.QUERY_OPTIMIZER_STATUS.value,
                EncapsulationMarkerStatus.COMPLETED.value,
            ):
                message = f"Capabilities query optimizer script completed successfully for query_id: {query_id}"
                publish_pubsub_message(
                    PROJECT_ID, SEQUENCE_COORDINATOR_TOPIC_ID, payload
                )
                meta_container.send_diagnostic(
                    DiagnosticActionType.UPDATE.value,
                    DiagnosticStatus.COMPLETED.value,
                    message,
                )

        return "Success", 200

    except Exception as e:
        message = f"An error occurred during capabilities query optimizer: {str(e)}"
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.FAILED.value,
            message,
        )
        toggle_marker_status(
            encapsulation_marker_collection,
            encapsulation_marker_id,
            query_id,
            EncapsulationMarkerStatusColumn.QUERY_OPTIMIZER_STATUS.value,
            EncapsulationMarkerStatus.FAILED.value,
        )
        return {"status": 400, "error": message}, 400

    finally:
        if mongodb_client is not None:
            mongodb_client.close()


