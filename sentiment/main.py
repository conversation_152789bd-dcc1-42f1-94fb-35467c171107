"""
This module processes incoming cloud events for sentiment analysis by handling the
entire flow from event decoding to updating the database and sending messages
based on processing status.
"""

from typing import Dict, Optional
import base64
import os
import json
from bson import ObjectId
import dask.dataframe as dd
import functions_framework
import pandas as pd
from pymongo import UpdateOne
from pymongo.collection import Collection
from const import (
    QUERY_COLLECTION_NAME,
    PROJECT_ID,
    SENTIMENT_TOPIC_ID,
    SEQUENCE_COORDINATOR_TOPIC_ID,
    LOOPBACK_THRESHOLD,
    RAW_TEXT_COL,
    ENCAPSULATION_MARKER_COLLECTION_NAME_METADATA_KEY,
    MODEL_NAME_METADATA_KEY,
    RAC_COLLECTION_NAME_METADATA_KEY,
    QUERY_PROJECTION_ATTRIBUTES,
    DEFAULT_EXPLANATION,
    DEFAULT_SENTIMENT,
    SENTIMENT_PROMPT,
    SENTIMENT_PROMPT_NO_CONTEXT,
    ORG_CREDENTIALS_COLLECTION,
)
from utils.langchain_utils import <PERSON><PERSON><PERSON><PERSON>U<PERSON>
from utils.llm_settings import SUMMARIES_TO_IGNORE
from utils.common_utils import (
    check_marker_status,
    check_query_status,
    get_query_config,
    toggle_marker_status,
    generate_sentiment_analysis,
    clean_sentiment,
)
from utils.logger import logger
from utils.mongo_db import get_mongodb_client, get_mongodb_collection, get_mongodb_db
from utils.pubsub_publisher import publish_pubsub_message
from utils.utilities import (
    CONSUMER_TYPE,
    DiagnosticActionType,
    DiagnosticStatus,
    EncapsulationMarkerStatus,
    EncapsulationMarkerStatusColumn,
    SentimentProcessingField,
    SummaryContext,
    MetaContainer,
    OrganizationAccountInfo,
)


def fetch_collection_data(
    collection: Collection,
    query_filter: Dict,
    encapsulation_marker: str,
    context: str,
    limit: int,
) -> pd.DataFrame:
    """
    Fetches data from a MongoDB collection for particular encapsulation_marker with a
    desired query filter and returns it as a Pandas DataFrame.

    Parameters:
    - collection (pymongo.collection.Collection): The MongoDB collection to fetch data from.
    - query_filter (dict): The query filter to fetch documents from collection.
    - encapsulation_marker (str): The name of the encapsulation marker.
    - context (str): The processing context which can be one of "CLUSTER", "STORY" or "THEME".
    - limit (int): The maximum number of documents to fetch.

    Returns:
    - pandas.DataFrame or None: DataFrame containing the fetched data, or None if an error occurs.

    Exception Handling:
    - Exception: If an error occurs during the fetch process, the error message is sent
        to the diagnostic engine.
    """
    try:
        meta_container = MetaContainer()

        # Specify projection to fetch only specific columns
        projection = {
            "_id": 1,
            RAW_TEXT_COL: 1,
            f"{SummaryContext[context].value}_summary": 1,
        }

        cursor = collection.find(query_filter, projection).limit(limit)

        # Convert cursor to DataFrame
        df = pd.DataFrame(cursor)
        logger.info(
            "Successfully fetched '%d' records for '%s' encapsulation marker from '%s' collection",
            len(df),
            encapsulation_marker,
            collection.name,
        )
        return df

    except Exception as e:
        message = f"An error occurred while fetching data for '{encapsulation_marker}' encapsulation marker from '{collection.name}' collection: {e}"
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.FAILED.value,
            message,
        )
        return None


def find_overall_processing_context(
    collection: Collection, encapsulation_marker: str
) -> str:
    """
    Determines the overall processing context based on the sentiment processing
    status in a MongoDB collection.

    This function checks documents in the specified collection to determine which
    processing context to return. It evaluates the sentiment processing status for
    clusters, stories, and themes in the documents based on the provided encapsulation
    marker. The priority is checked in the following order:
        - If any document has `is_cluster_sentiment_processed` as `False` or missing,
            the context is "CLUSTER".
        - If all clusters are processed but any document has `is_story_sentiment_processed`
            as `False` or missing, the context is "STORY".
        - If all clusters and stories are processed but any document has `
            is_theme_sentiment_processed` as `False` or missing, the context is "THEME".
        - If all fields are processed, the context is "ALL_PROCESSED".

    Parameters:
    - collection (pymongo.collection.Collection): The MongoDB collection to query.
    - encapsulation_marker (str): The value of the `encapsulation_marker` field used
        to filter documents.

    Returns:
    - str: The processing context which can be one of "CLUSTER", "STORY", "THEME",
        or "ALL_PROCESSED".

    Exception Handling:
    - None
    """
    # TODO: Story and theme sentiment processing are currently disabled, as only cluster
    # sentiment is needed. Re-enable if additional sentiment processing becomes
    # necessary in the future.

    # Check if there is any document where `is_cluster_sentiment_processed` is not True
    context_check = collection.find_one(
        {
            "encapsulation_marker": encapsulation_marker,
            "$or": [
                {SentimentProcessingField.CLUSTER.value: {"$ne": True}},
                # {SentimentProcessingField.STORY.value: {"$ne": True}},
                # {SentimentProcessingField.THEME.value: {"$ne": True}},
            ],
        },
        projection={
            "_id": 1,
            SentimentProcessingField.CLUSTER.value: 1,
            # SentimentProcessingField.STORY.value: 1,
            # SentimentProcessingField.THEME.value: 1,
        },
    )

    # Determine the context based on the result of the query
    if context_check:
        if context_check.get(SentimentProcessingField.CLUSTER.value) is not True:
            return SummaryContext.CLUSTER.value.upper()
        # elif context_check.get(SentimentProcessingField.STORY.value) is not True:
        #     return SummaryContext.STORY.value.upper()
        # elif context_check.get(SentimentProcessingField.THEME.value) is not True:
        #     return SummaryContext.THEME.value.upper()

    # If all documents have all fields as True
    return SummaryContext.ALL_PROCESSED.value.upper()


def extract_sentiment_and_explanation(text: str) -> tuple:
    """
    Parses the generated text into Sentiment and Explanation.

    Parameters:
    - text (str): The generated text to be parsed.

    Returns:
    - tuple: A tuple containing sentiment and explanation strings.

    Exception Handling:
    - Exception: Raised if an error occurs while extracting sentiment and explanation from the text.
    """
    try:
        text = text.strip()

        # Split the text by new line and consider only the first line
        sentiment = str(text).split("\n", maxsplit=1)[0].strip()

        # Split the text by new line and consider all lines except the first line
        explanation = "\n".join(str(text).split("\n")[1:]).strip()

        if explanation == "":
            parsed_text = sentiment
            sentiment = parsed_text.split(".")[0].strip()

            # Split the parsed text by full stop and consider all text except the first split if it exists
            # If the first split does not exist, then set the default explanation.
            explanation = (
                ". ".join(parsed_text.split(".")[1:]).strip()
                if len(parsed_text.split(".")) > 1
                else DEFAULT_EXPLANATION
            )

        if sentiment == DEFAULT_SENTIMENT:
            explanation = DEFAULT_EXPLANATION
        return sentiment, explanation

    except Exception as e:
        message = f"Failed to extract sentiment and explanation from the text: {e}"
        raise Exception(message) from e


def get_sentiment(
    langchain_utility: LangChainUtility, summary_text: str, raw_text: str
) -> tuple:
    """
    Get sentiment based on the provided summary and raw text.

    Parameters:
    - summary_text (str): Summary text to include in the prompt.
    - raw_text (str): Raw text needs to be used in the sentiment analysis prompt.

    Returns:
    - tuple: A tuple containing sentiment and explanation extracted from the analysis response.
             The sentiment is typically a string (e.g., "positive", "negative", "neutral").
             The explanation provides additional context or reasoning behind the sentiment.

    Exception Handling:
    - None
    """
    # Determine the appropriate prompt based on summary availability
    if pd.isna(summary_text) or summary_text in SUMMARIES_TO_IGNORE:
        prompt = SENTIMENT_PROMPT_NO_CONTEXT.format(text=raw_text)
    else:
        prompt = SENTIMENT_PROMPT.format(summary=summary_text, text=raw_text)

    # Generate sentiment analysis response
    response_text = generate_sentiment_analysis(langchain_utility, prompt)

    # Extract and return sentiment and explanation
    return extract_sentiment_and_explanation(response_text)


def process_sentiment(
    df: pd.DataFrame,
    context: str,
    langchain_utility: LangChainUtility,
    partition_info: Optional[Dict] = None,
) -> pd.DataFrame:
    """
    Processes sentiment analysis on the summary columns of the DataFrame and add corresponding
    sentiment and explanation columns. It considers both the summary and the 'BODY1' column for analysis.

    - The function analyzes the sentiment of text in 'cluster_summary', 'story_summary',
    and 'theme_summary' columns, combined with the 'BODY1' column, and adds new columns with the
    sentiment and explanation results.

    Parameters:
    - df (pd.DataFrame): The input DataFrame containing summary and 'BODY1' columns.
    - context (str): The processing context which can be one of "CLUSTER", "STORY" or "THEME".
    - partition_info (dict, optional): Information about the current partition being processed.
        Expected keys:
        - 'number': The index of the partition.
        - 'division': The starting index or position of the partition within the entire DataFrame.

    Returns:
    - pd.DataFrame: The DataFrame with added sentiment and explanation columns.

    Exception Handling:
    - None
    """
    # Get summary, sentiment, sentiment explanation, and is sentiment processed columns based on context
    summary_col = f"{SummaryContext[context].value}_summary"
    sentiment_col = f"{SummaryContext[context].value}_sentiment"
    sentiment_explanation_col = f"{sentiment_col}_reasoning"
    is_sentiment_processed_col = SentimentProcessingField[context].value

    if summary_col in df.columns and RAW_TEXT_COL in df.columns:
        # Apply get_sentiment to each row in the DataFrame and assign directly to new columns
        df[
            [
                sentiment_col,
                sentiment_explanation_col,
            ]
        ] = df[[summary_col, RAW_TEXT_COL]].apply(
            lambda row: pd.Series(
                get_sentiment(langchain_utility, row[summary_col], row[RAW_TEXT_COL])
            ),
            axis=1,
        )

    # Apply the cleaning function to sentiment column
    df[sentiment_col] = df[sentiment_col].apply(clean_sentiment)

    # Mark the sentiment processing status as complete for all rows in the DataFrame for current partition of particular context
    df[is_sentiment_processed_col] = True

    # Drop columns that are not needed for the update operation
    columns_to_drop = [
        RAW_TEXT_COL,
        f"{SummaryContext[context].value}_summary",
    ]

    # Filter the columns to drop only those that exist in the DataFrame
    columns_to_drop_existing = [col for col in columns_to_drop if col in df.columns]
    df.drop(columns=columns_to_drop_existing, inplace=True)

    logger.info(
        "Sentiment processing completed successfully for partition: %s", partition_info
    )
    return df


def process_data_with_dask(
    langchain_utility: LangChainUtility, summaries_df: pd.DataFrame, context: str
) -> pd.DataFrame:
    """
    Process summaries data using Dask DataFrame with specified sentiment function.

    Parameters:
    - summaries_df (pd.DataFrame): Input Pandas DataFrame containing summaries
        data for theme, stories and clusters.
    - context (str): The processing context which can be one of "CLUSTER",
        "STORY" or "THEME".

    Returns:
    - pd.DataFrame: Processed Pandas DataFrame containing the final results
        after computation using dask.

    Exception Handling:
    - None
    """
    # Convert DataFrame to Dask DataFrame with partitions based on CPU count
    ddf = dd.from_pandas(summaries_df, npartitions=os.cpu_count())

    # Apply process sentiment function to each partition and compute the result
    summaries_df = ddf.map_partitions(
        process_sentiment, context, langchain_utility
    ).compute(scheduler="threads")

    logger.info(
        "Successfully processed DataFrame using Dask. Length: %d, Partitions: %d",
        len(summaries_df),
        ddf.npartitions,
    )

    return summaries_df


def update_mongodb_collection(
    collection: Collection, dataframe: pd.DataFrame, encapsulation_marker: str
) -> bool:
    """
    Updates a MongoDB collection with data from a Pandas DataFrame using a bulk write operation.

    Parameters:
    - collection (pymongo.collection.Collection): The MongoDB collection to update.
    - dataframe (pandas.DataFrame): The DataFrame containing data to append.
    - encapsulation_marker (str): The name of the encapsulation marker.

    Returns:
    - bool: True if the collection update was successful, False otherwise.

    Exception Handling:
    - ValueError: Raised if the DataFrame does not contain an '_id' column.
    - Exception: If an error occurs during the update process, the error message is sent to the diagnostic engine.
    """
    try:
        # Check for mandatory '_id' column
        if "_id" not in dataframe.columns:
            raise ValueError(
                "DataFrame must contain an '_id' column for matching documents in MongoDB."
            )

        bulk_updates = []
        for _, row in dataframe.iterrows():
            mongo_id = ObjectId(row["_id"])
            update_data = {
                "$set": {col: row[col] for col in dataframe.columns if col != "_id"}
            }
            bulk_updates.append(UpdateOne({"_id": mongo_id}, update_data))

        # Execute bulk updates
        result = collection.bulk_write(bulk_updates)
        matched_count = result.matched_count
        updated_count = result.modified_count

        logger.info(
            "Updated %d documents for '%s' encapsulation marker in the '%s' collection",
            updated_count,
            encapsulation_marker,
            collection.name,
        )

        logger.info(
            "%d documents were not modified (already matched).",
            matched_count - updated_count,
        )
        return True

    except Exception as e:
        message = f"An error occurred while updating documents for '{encapsulation_marker}' encapsulation marker in the '{collection.name}' collection: {e}"

        meta_container = MetaContainer()
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.FAILED.value,
            message,
        )
        return False


@functions_framework.cloud_event
def main(cloud_event):
    """
    This function is the entry point that processes incoming cloud events, extracts relevant data,
    and initiates sentiment analysis based on the provided payload.

    Steps:
    - Decode the payload of the cloud event to extract relevant data.
    - Retrieve organization, query, and encapsulation information from the payload.
    - Retrieve MongoDB connection details based on the organization ID from the organization vault.
    - Retrieve the capabilities database and LLM configuration collection.
    - Fetch query configuration from the organization database.
    - Send diagnostic with `PENDING` status to the diagnostic engine.
    - Get context such as cluster, story, or theme for which we need to process.
    - Set the configuration for the LangChain utility using model name and llm config collection.
    - Toggle encapsulation marker status to 'PENDING'.
    - Fetch summaries data(records which are not processed) associated with the encapsulation
        marker for particular context.
    - Parallelize sentiment analysis on the encapsulation marker data using Dask.
    - Update the 'RAC' collection with sentiment analysis data.
    - Update encapsulation marker status based on the success or failure.
    - If there are inactive documents for a particular encapsulation marker for a particular context:
        - Publish a loopback message to a Sentiment Pub/Sub topic.
    - Else:
        - All documents for a particular encapsulation marker are processed successfully for all
            contexts: cluster, story and themes, send message to sequence coordinator.
        - If all encapsulation markers are completed for a particular query, send diagnostic with
            `COMPLETED` status.
    - Log any errors encountered during the process for troubleshooting purposes.

    Parameters:
    - cloud_event (google.cloud.functions.Context): CloudEvent data provided by the
        function framework.

    Returns:
    - {"Status":200}: For successfully execution.

    Exception Handling:
    - Exception: If an error occurs during the sentiment analysis process, the error message is
        sent to the diagnostic engine.
    """
    mongodb_client = None
    try:
        meta_container = MetaContainer()
        langchain_utility = LangChainUtility()

        payload = json.loads(
            base64.b64decode(cloud_event.data["message"]["data"]).decode("utf-8")
        )
        logger.info("Message received successfully: %s", payload)
        payload["publisher_type"] = CONSUMER_TYPE
        meta_container.set_payload_info(payload)

        encapsulation = payload["encapsulation"]

        organization_id = payload["organization_id"]
        query_id = payload["query_id"]
        encapsulation_marker = encapsulation["encapsulation_marker"]
        encapsulation_marker_id = encapsulation["_id"]

        org_account_info = OrganizationAccountInfo(organization_id)
        mongodb_url = org_account_info.mongodb_url
        organization_db_name = org_account_info.organization_db_name

        mongodb_client = get_mongodb_client(mongodb_url)
        organization_db = get_mongodb_db(mongodb_client, organization_db_name)
        query_collection = get_mongodb_collection(
            organization_db, QUERY_COLLECTION_NAME
        )

        query_config = get_query_config(
            query_collection, query_id, QUERY_PROJECTION_ATTRIBUTES
        )

        if not query_config:
            logger.warning("No configuration found for query_id: '%s'", query_id)
            return

        query_meta_data = query_config["meta_data"]
        query_meta_data = (
            query_meta_data[0] if isinstance(query_meta_data, list) else query_meta_data
        )

        encapsulation_marker_collection_name = query_meta_data[
            ENCAPSULATION_MARKER_COLLECTION_NAME_METADATA_KEY
        ]

        rac_collection_name = query_meta_data[RAC_COLLECTION_NAME_METADATA_KEY]
        encapsulation_marker_collection = get_mongodb_collection(
            organization_db, encapsulation_marker_collection_name
        )
        rac_collection = get_mongodb_collection(organization_db, rac_collection_name)

        # Check if the encapsulation marker with the given query ID has completed the sentiment.
        is_marker_completed = check_marker_status(
            encapsulation_marker_collection,
            encapsulation_marker_id,
            query_id,
            EncapsulationMarkerStatusColumn.SENTIMENT_STATUS.value,
            EncapsulationMarkerStatus.COMPLETED.value,
        )

        if is_marker_completed:
            logger.warning(
                "'%s' encapsulation Marker with query ID '%s' and encapsulation marker ID '%s' is already completed.",
                encapsulation_marker,
                query_id,
                encapsulation_marker_id,
            )

            return "Success", 200

        message = f"Capabilities sentiment script started successfully for query_id: {query_id}"
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.PENDING.value,
            message,
        )

        # Get context such as cluster, story, or theme for which we need to process
        context = find_overall_processing_context(rac_collection, encapsulation_marker)
        llm_config_collection = get_mongodb_collection(
            organization_db, ORG_CREDENTIALS_COLLECTION
        )

        # Set configuration for the langchain utility
        langchain_utility.set_configuration(
            query_meta_data[MODEL_NAME_METADATA_KEY], llm_config_collection
        )

        # Update the encapsulation marker status to 'PENDING' for sentiment analysis
        toggle_marker_status(
            encapsulation_marker_collection,
            encapsulation_marker_id,
            query_id,
            EncapsulationMarkerStatusColumn.SENTIMENT_STATUS.value,
            EncapsulationMarkerStatus.PENDING.value,
        )

        # Check if all records are already processed by sentiment multi consumer
        summaries_df = pd.DataFrame()
        if context != SummaryContext.ALL_PROCESSED.value.upper():

            # Filter to find documents where sentiment is not processed
            query_filter = {
                "encapsulation_marker": encapsulation_marker,
                SentimentProcessingField[context].value: {"$ne": True},
            }

            summaries_df = fetch_collection_data(
                rac_collection,
                query_filter,
                encapsulation_marker,
                context,
                int(LOOPBACK_THRESHOLD),
            )

        if summaries_df is not None:
            if summaries_df.empty:
                success = True

            else:
                summaries_df = process_data_with_dask(
                    langchain_utility, summaries_df, context
                )
                success = update_mongodb_collection(
                    rac_collection, summaries_df, encapsulation_marker
                )

            if success:
                # Retrieve the context for a specific encapsulation marker for which the sentiment is not processed
                context = find_overall_processing_context(
                    rac_collection, encapsulation_marker
                )

                if context != SummaryContext.ALL_PROCESSED.value.upper():
                    publish_pubsub_message(PROJECT_ID, SENTIMENT_TOPIC_ID, payload)
                    logger.info(
                        "Published loopback message for inactive documents with '%s encapsulation marker' for '%s' context",
                        encapsulation_marker,
                        context,
                    )

                else:
                    toggle_marker_status(
                        encapsulation_marker_collection,
                        encapsulation_marker_id,
                        query_id,
                        EncapsulationMarkerStatusColumn.SENTIMENT_STATUS.value,
                        EncapsulationMarkerStatus.COMPLETED.value,
                    )

                    publish_pubsub_message(
                        PROJECT_ID, SEQUENCE_COORDINATOR_TOPIC_ID, payload
                    )

                    if check_query_status(
                        encapsulation_marker_collection,
                        query_id,
                        EncapsulationMarkerStatusColumn.SENTIMENT_STATUS.value,
                        EncapsulationMarkerStatus.COMPLETED.value,
                    ):
                        message = f"Capabilities sentiment script completed successfully for query_id: {query_id}"
                        meta_container.send_diagnostic(
                            DiagnosticActionType.UPDATE.value,
                            DiagnosticStatus.COMPLETED.value,
                            message,
                        )

            else:
                toggle_marker_status(
                    encapsulation_marker_collection,
                    encapsulation_marker_id,
                    query_id,
                    EncapsulationMarkerStatusColumn.SENTIMENT_STATUS.value,
                    EncapsulationMarkerStatus.FAILED.value,
                )

        mongodb_client.close()
        return "Success", 200

    except Exception as e:
        message = f"An error occurred during capabilities sentiment: {e}"
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.FAILED.value,
            message,
        )

        toggle_marker_status(
            encapsulation_marker_collection,
            encapsulation_marker_id,
            query_id,
            EncapsulationMarkerStatusColumn.SENTIMENT_STATUS.value,
            EncapsulationMarkerStatus.FAILED.value,
        )

    finally:
        if mongodb_client is not None:
            mongodb_client.close()
