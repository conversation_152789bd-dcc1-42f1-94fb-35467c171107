"""
This module contains constants used in files of sentiment consumer.
"""

import os

ORG_CREDENTIALS_COLLECTION = os.getenv("ORG_CREDENTIALS_COLLECTION")
QUERY_COLLECTION_NAME = os.getenv("QUERY_COLLECTION_NAME")

PROJECT_ID = os.getenv("PROJECT_ID")
SENTIMENT_TOPIC_ID = os.getenv("SENTIMENT_TOPIC_ID")
SEQUENCE_COORDINATOR_TOPIC_ID = os.getenv("SEQUENCE_COORDINATOR_TOPIC_ID")
LOOPBACK_THRESHOLD = os.getenv("LOOPBACK_THRESHOLD")

RAW_TEXT_COL = "BODY1"
ENCAPSULATION_MARKER_COLLECTION_NAME_METADATA_KEY = (
    "ENCAPSULATION_MARKER_COLLECTION_NAME"
)
RAC_COLLECTION_NAME_METADATA_KEY = "RAC_COLLECTION_NAME"
MODEL_NAME_METADATA_KEY = "MODEL_NAME"
QUERY_PROJECTION_ATTRIBUTES = {
    f"meta_data.{ENCAPSULATION_MARKER_COLLECTION_NAME_METADATA_KEY}": 1,
    f"meta_data.{MODEL_NAME_METADATA_KEY}": 1,
    f"meta_data.{RAC_COLLECTION_NAME_METADATA_KEY}": 1,
}

DEFAULT_EXPLANATION = "No explanation generated"
DEFAULT_SENTIMENT = "No sentiment generated"

# Define the allowed sentiment classifications
SENTIMENT_CLASSIFICATION = ["Negative", "Neutral", "Positive"]

# ------------------------------------- Sentiment Generation Prompts ----------------------------------------------------------
SENTIMENT_PROMPT = """
    Classify the text delimited into one of these sentiment categories: Neutral, Positive, Negative. 
    Give an explanation that gives the reasoning for the chosen sentiment category. 
    Format the output to first give the sentiment, followed by an explanation on a new line.

    CONTEXT: ```{summary}```

    TEXT: ```{text}```

    OUTPUT: 
    """

SENTIMENT_PROMPT_NO_CONTEXT = """
    Classify the text delimited into one of these sentiment categories: Neutral, Positive, Negative. 
    Give an explanation that gives the reasoning for the chosen sentiment category. 
    Format the output to first give the sentiment, followed by an explanation on a new line.

    TEXT: ```{text}```

    OUTPUT: 
    """
