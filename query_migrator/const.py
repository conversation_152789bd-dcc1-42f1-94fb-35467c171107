"""
This module contains constants used in files of query migrator consumer.
"""

import os

BASE_LOOPBACK_THRESHOLD = os.getenv("BASE_LOOPBACK_THRESHOLD")
CHUNK_SIZE = os.getenv("CHUNK_SIZE")
CONTEXT_DATA_COLLECTION_NAME = os.getenv("CONTEXT_DATA_COLLECTION_NAME")
PROJECT_ID = os.getenv("PROJECT_ID")
QUERY_COLLECTION_NAME = os.getenv("QUERY_COLLECTION_NAME")
QUERY_MIGRATOR_TOPIC_ID = os.getenv("QUERY_MIGRATOR_TOPIC_ID")
SEQUENCE_COORDINATOR_TOPIC_ID = os.getenv("SEQUENCE_COORDINATOR_TOPIC_ID")

CLIENT_API_RAC_TRANSFORM_VOLUME_METADATA_KEY = "CLIENT_API_RAC_TRANSFORM_VOLUME"
INDEX_STATUS_METADATA_KEY = "INDEX_STATUS"
QUERY_MIGRATOR_OFFSET_METADATA_KEY = "QUERY_MIGRATOR_OFFSET"
QUERY_MIGRATOR_STATUS_METADATA_KEY = "QUERY_MIGRATOR_STATUS"
RAC_TRANSFORM_COLLECTION_NAME_METADATA_KEY = "RAC_TRANSFORM_COLLECTION_NAME"
RAC_TRANSFORM_VOLUME_METADATA_KEY = "RAC_TRANSFORM_VOLUME"
QUERY_PROJECTION_ATTRIBUTES = {
    f"meta_data.{QUERY_MIGRATOR_OFFSET_METADATA_KEY}": 1,
    f"meta_data.{QUERY_MIGRATOR_STATUS_METADATA_KEY}": 1,
    f"meta_data.{RAC_TRANSFORM_COLLECTION_NAME_METADATA_KEY}": 1,
    f"meta_data.{RAC_TRANSFORM_VOLUME_METADATA_KEY}": 1,
}
