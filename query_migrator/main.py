"""
This module contains a Cloud Function that is triggered by a Cloud Pub/Sub message,
retrieved data from the RAC transform, context_data, and query organization MongoDB collections,
and inserts the data into a target client API MongoDB collections.
"""

import base64
import json
from typing import Dict
from bson import ObjectId
from cloudevents.http import CloudEvent
import functions_framework
import numpy as np
import pandas as pd
from pymongo.collection import Collection
from const import (
    CLIENT_API_RAC_TRANSFORM_VOLUME_METADATA_KEY,
    CONTEXT_DATA_COLLECTION_NAME,
    INDEX_STATUS_METADATA_KEY,
    QUERY_MIGRATOR_OFFSET_METADATA_KEY,
    QUERY_MIGRATOR_STATUS_METADATA_KEY,
    RAC_TRANSFORM_VOLUME_METADATA_KEY,
    SEQUENCE_COORDINATOR_TOPIC_ID,
    BASE_LOOPBACK_THRESHOLD,
    CHUNK_SIZE,
    PROJECT_ID,
    QUERY_COLLECTION_NAME,
    QUERY_MIGRATOR_TOPIC_ID,
    RAC_TRANSFORM_COLLECTION_NAME_METADATA_KEY,
    QUERY_PROJECTION_ATTRIBUTES,
)
from utils.common_utils import (
    fetch_collection_data,
    get_query_config,
    update_offset_and_publish,
    update_query_metadata,
)
from utils.logger import logger
from utils.mongo_db import get_mongodb_client, get_mongodb_collection, get_mongodb_db
from utils.pubsub_publisher import publish_pubsub_message
from utils.utilities import (
    CONSUMER_TYPE,
    DiagnosticActionType,
    DiagnosticStatus,
    MetaContainer,
    OrganizationAccountInfo,
)

meta_container = MetaContainer()


def complete_query_migration(query_id: str, payload: Dict) -> None:
    """
    Completes the query migration process by publishing a message to the sequence coordinator
    and sending a diagnostic completion message.

    Parameters:
    - query_id (str): The ID of the query for which the process is completed.
    - payload (dict): The data payload to be published.

    Returns:
    - None

    Exception Handling:
    - None
    """
    publish_pubsub_message(PROJECT_ID, SEQUENCE_COORDINATOR_TOPIC_ID, payload)
    message = f"Capabilities query migrator script completed successfully for query_id: {query_id}"
    meta_container.send_diagnostic(
        DiagnosticActionType.UPDATE.value,
        DiagnosticStatus.COMPLETED.value,
        message,
    )


def filter_duplicates(
    collection: Collection, df: pd.DataFrame, query_id: str
) -> pd.DataFrame:
    """
    Filters out any '_id' values from the input DataFrame that already exist
    in the MongoDB collection.

    Parameters:
    - collection (pymongo.collection.Collection): The MongoDB collection
        object to check for duplicates.
    - df (pd.DataFrame): The DataFrame containing the records.
    - query_id (str): The string representation of the query_id.

    Returns:
    - pd.DataFrame: A filtered DataFrame without duplicates found in the MongoDB collection.

    Exception Handling:
    - Exception: Raised if any error occurs while filtering duplicates.
    """
    try:
        # Convert _id values to ObjectId if they are not already ObjectId
        ids_to_check = [ObjectId(_id) for _id in df["_id"]]

        # Query to find existing IDs in MongoDB collection
        query = {"_id": {"$in": ids_to_check}}

        # Use MongoDB's distinct to fetch existing id values and convert them to strings
        existing_ids = {str(id) for id in collection.distinct("_id", query)}

        # Ensure DataFrame _id values are in string format for comparison and filter out existing IDs
        filtered_df = df[~df["_id"].astype(str).isin(existing_ids)]

        # Log duplicate information if found
        duplicate_count = len(existing_ids)
        if duplicate_count > 0:
            logger.warning(
                "Skipping %d duplicate ID(s) found in '%s' collection for '%s' query ID.",
                duplicate_count,
                collection.name,
                query_id,
            )

        return filtered_df

    except Exception as e:
        raise Exception("Error while filtering out duplicates: %s", e) from e


def insert_dataframe_into_collection(
    collection: Collection,
    query_id: str,
    df: pd.DataFrame,
) -> bool:
    """
    Insert a pandas DataFrame into a MongoDB collection.

    Parameters:
    - collection (pymongo.collection.Collection): The MongoDB collection.
    - query_id (str): The string representation of the query_id.
    - df (pandas.DataFrame): The DataFrame containing the data to be inserted.

    Returns:
    - bool: True if data insertion is successful, False otherwise.

    Exception Handling:
    - Exception: If any error occurs during the insertion process, the error message is sent to the diagnostic engine.
    """
    try:
        # Replace null-like values in df with None
        df.replace(
            {
                np.nan: None,
                pd.NA: None,
                pd.NaT: None,
            },
            inplace=True,
        )
        data = df.to_dict(orient="records")
        collection.insert_many(data)
        logger.info(
            "Successfully inserted '%d' record(s) into the '%s' collection for query ID '%s'.",
            len(df),
            collection.name,
            query_id,
        )
        return True

    except Exception as e:
        message = f"An error occurred during data insertion for '{query_id}' query_id in '{collection.name}' collection: {str(e)}"
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.FAILED.value,
            message,
        )
        return False


def migrate_documents(
    source_collection: Collection,
    target_collection: Collection,
    query: dict,
    query_id: str,
) -> None:
    """
    Fetch documents from the source collection based on the query and insert them into the target collection.

    Parameters:
    - source_collection (pymongo.collection.Collection): The source MongoDB collection.
    - target_collection (pymongo.collection.Collection): The target MongoDB collection.
    - query (dict): The query to fetch documents from the source collection.
    - query_id (str): The string representation of the Query ID.

    Returns:
    - None

    Exception Handling:
    - None
    """
    # Fetch documents from the source collection based on the query
    df = pd.DataFrame(source_collection.find(query))
    df = filter_duplicates(target_collection, df, query_id)
    documents = df.to_dict(orient="records")

    if documents:
        # Insert documents into the target collection
        target_collection.insert_many(documents)
        logger.info(
            "Successfully migrated %d documents from organization '%s' collection to client API '%s' collection for query ID '%s'.",
            len(documents),
            source_collection.name,
            target_collection.name,
            query_id,
        )
    else:
        logger.warning(
            "No documents to migrate from the source collection '%s' for query ID '%s'. All records already exist in the target collection.",
            source_collection.name,
            query_id,
        )


# Triggered from a message on a Cloud Pub/Sub topic.
@functions_framework.cloud_event
def main(cloud_event: CloudEvent):
    """
    Cloud Function that is triggered by a Cloud Pub/Sub message. It retrieves data from the transform MongoDB collection
    and inserts the data into a target client API MongoDB collection.

    Steps:
    - Decode the cloud event payload to extract relevant data.
    - Retrieve organization and query information from the payload.
    - Toggle diagnostic status to 'PENDING'.
    - Retrieve MongoDB connection details from the organization vault using the organization ID.
    - Fetch the query configuration from the MongoDB database.
    - Retrieve the RAC transform collection name from query metadata.
    - Establish connections to client API's MongoDB collections.
    - Retrieve the RAC transform collection volume.
    - Process data in chunks while the offset is less than the loopback threshold:
        - Fetch query data in chunks using the provided offset and chunk size.
        - Filter out duplicates from the DataFrame.
        - Insert the DataFrame into the client API MongoDB collection.
        - Update the query migrator offset in query's metadata.
        - If there are more documents remaining (offset >= loopback_threshold),
            loop back with the offset by sending pub sub message to query migrator topic id.
        - If all documents are processed (offset equals total_documents), send a
            loopback message to the query migrator Pub/Sub topic for context data and query data migration.
    - Migrate context data collection documents.
    - Migrate query collection document.
    - Complete the query migration process by publishing a message to the sequence coordinator and toggling diagnostic status to 'COMPLETED'.
    - If an error occurs during the migration, toggle diagnostic status to 'FAILED'.

    Parameters:
    - cloud_event (CloudEvent): The CloudEvent containing the Pub/Sub message data.

    Returns:
    - tuple: A tuple containing a success message and HTTP status code.

    Exception Handling:
    - Exception: If any error occurs during the migration process.
    """
    mongodb_client = None
    client_api_mongodb_connection = None
    try:
        payload = json.loads(
            base64.b64decode(cloud_event.data["message"]["data"]).decode("utf-8")
        )

        logger.info("Message received successfully: %s", payload)
        payload["publisher_type"] = CONSUMER_TYPE
        meta_container.set_payload_info(payload)

        organization_id = payload["organization_id"]
        query_id = payload["query_id"]

        message = f"Capabilities query migrator script started successfully for query_id: {query_id}"
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.PENDING.value,
            message,
        )

        org_account_info = OrganizationAccountInfo(organization_id)
        mongodb_url = org_account_info.mongodb_url
        organization_db_name = org_account_info.organization_db_name

        mongodb_client = get_mongodb_client(mongodb_url)
        organization_db = get_mongodb_db(mongodb_client, organization_db_name)

        context_data_collection = get_mongodb_collection(
            organization_db, CONTEXT_DATA_COLLECTION_NAME
        )
        query_collection = get_mongodb_collection(
            organization_db, QUERY_COLLECTION_NAME
        )

        query_config = get_query_config(
            query_collection, query_id, QUERY_PROJECTION_ATTRIBUTES
        )

        if not query_config:
            logger.warning("No configuration found for query_id: '%s'", query_id)
            return

        query_meta_data = query_config["meta_data"]
        query_meta_data = (
            query_meta_data[0] if isinstance(query_meta_data, list) else query_meta_data
        )

        offset = query_meta_data.get(QUERY_MIGRATOR_OFFSET_METADATA_KEY, 0)
        query_migrator_status = query_meta_data.get(QUERY_MIGRATOR_STATUS_METADATA_KEY)
        rac_transform_collection_name = query_meta_data.get(
            RAC_TRANSFORM_COLLECTION_NAME_METADATA_KEY
        )

        if query_migrator_status == DiagnosticStatus.COMPLETED.value:
            logger.warning(
                "Query migration with query ID '%s' is already completed.",
                query_id,
            )
            return "Success", 200

        rac_transform_collection = get_mongodb_collection(
            organization_db, rac_transform_collection_name
        )

        # Establishing connections to client API's MongoDB collections
        client_api_mongodb_url = org_account_info.client_api_mongodb_url
        client_api_mongodb_connection = get_mongodb_client(client_api_mongodb_url)
        client_api_organization_db = get_mongodb_db(
            client_api_mongodb_connection, organization_db_name
        )
        client_api_context_data_collection = get_mongodb_collection(
            client_api_organization_db, CONTEXT_DATA_COLLECTION_NAME
        )
        client_api_rac_transform_collection = get_mongodb_collection(
            client_api_organization_db, rac_transform_collection_name
        )
        client_api_query_collection = get_mongodb_collection(
            client_api_organization_db, QUERY_COLLECTION_NAME
        )

        # Retrieve the RAC transform collection volume
        total_documents = (
            query_meta_data.get(RAC_TRANSFORM_VOLUME_METADATA_KEY)
            or rac_transform_collection.estimated_document_count()
        )

        loopback_threshold = offset + int(BASE_LOOPBACK_THRESHOLD)

        while offset < loopback_threshold:
            rac_transform_df = fetch_collection_data(
                rac_transform_collection, {}, {}, offset, int(CHUNK_SIZE)
            )
            batch_count = len(rac_transform_df)
            if not rac_transform_df.empty:
                rac_transform_df = filter_duplicates(
                    client_api_rac_transform_collection, rac_transform_df, query_id
                )

            if not rac_transform_df.empty:
                success = insert_dataframe_into_collection(
                    client_api_rac_transform_collection, query_id, rac_transform_df
                )
                if success:
                    offset = update_offset_and_publish(
                        offset,
                        QUERY_MIGRATOR_OFFSET_METADATA_KEY,
                        total_documents,
                        loopback_threshold,
                        payload,
                        batch_count,
                        query_collection,
                        query_id,
                        QUERY_MIGRATOR_TOPIC_ID,
                    )
                    if offset == total_documents:
                        # Update the query metadata with client api rac transform volume
                        client_api_rac_transform_volume = (
                            client_api_rac_transform_collection.count_documents(
                                {}, hint="_id_"
                            )
                        )
                        update_query_metadata(
                            query_collection,
                            query_id,
                            CLIENT_API_RAC_TRANSFORM_VOLUME_METADATA_KEY,
                            client_api_rac_transform_volume,
                        )
                        publish_pubsub_message(
                            PROJECT_ID, QUERY_MIGRATOR_TOPIC_ID, payload
                        )
                        logger.info(
                            "Published loopback message for query_id %s related to context data and query data",
                            query_id,
                        )
                        return
                else:
                    return

            else:
                if (
                    total_documents
                    == client_api_rac_transform_collection.count_documents(
                        {}, hint="_id_"
                    )
                ):
                    # Update the query metadata with the new offset
                    update_query_metadata(
                        query_collection,
                        query_id,
                        QUERY_MIGRATOR_OFFSET_METADATA_KEY,
                        total_documents,
                    )
                    break

                # Handling the case where all records in the batch are duplicates
                if batch_count:
                    offset = update_offset_and_publish(
                        offset,
                        QUERY_MIGRATOR_OFFSET_METADATA_KEY,
                        total_documents,
                        loopback_threshold,
                        payload,
                        batch_count,
                        query_collection,
                        query_id,
                        QUERY_MIGRATOR_TOPIC_ID,
                    )
                else:
                    break

        if total_documents == client_api_rac_transform_collection.count_documents(
            {}, hint="_id_"
        ):
            # Migrate context data collection documents
            migrate_documents(
                context_data_collection,
                client_api_context_data_collection,
                {"query_id": ObjectId(query_id)},
                query_id,
            )
            # Update the query metadata with query migrator status
            update_query_metadata(
                query_collection,
                query_id,
                QUERY_MIGRATOR_STATUS_METADATA_KEY,
                DiagnosticStatus.COMPLETED.value,
            )
            # Migrate query collection document
            migrate_documents(
                query_collection,
                client_api_query_collection,
                {"_id": ObjectId(query_id)},
                query_id,
            )
            # Reset index status
            update_query_metadata(
                client_api_query_collection,
                query_id,
                meta_data_key=INDEX_STATUS_METADATA_KEY,
                value={},
            )
            # Complete the query migration process
            complete_query_migration(query_id, payload)

        return "Success", 200

    except Exception as e:
        message = f"An error occurred during capabilities query migrator: {e}"
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.FAILED.value,
            message,
            str(type(e)),
        )

    finally:
        if mongodb_client is not None:
            mongodb_client.close()
        if client_api_mongodb_connection is not None:
            client_api_mongodb_connection.close()
