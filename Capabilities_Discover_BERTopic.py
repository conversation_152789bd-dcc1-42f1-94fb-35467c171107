# -*- coding: utf-8 -*-
"""
Created on Mon Mar 18 12:48:40 2024

@author: nitin
"""

import os
import numpy as np
import pandas as pd
from pandas import ExcelWriter
from collections import Counter
from sentence_transformers import SentenceTransformer
from hdbscan import HDBSCAN #from cuml.cluster import HDBSCAN # RAPIDS requires a Linux host. cuml part of rapids
from umap import UMAP #from cuml.manifold import UMAP
# from cuml.preprocessing import normalize
from sklearn.feature_extraction.text import CountVectorizer
from bertopic.vectorizers import ClassTfidfTransformer
from bertopic import BERTopic #https://maartengr.github.io/BERTopic/algorithm/algorithm.html
#
## Speeding up UMAP 
#
from sklearn.decomposition import PCA
def rescale(x, inplace=False):
    """ Rescale an embedding so optimization will not have convergence issues.
    """
    if not inplace:
        x = np.array(x, copy=True)
    x /= np.std(x[:, 0]) * 10000
    return x
#
### IMPORTING DATA FILES
#
In='C:\\Users\\<USER>\\Documents\\NeurIPS_2024\\Raw_Data\\Data_For_Analyses\\SSA\\chatgpt_streaming_tweets_300k.json' # DataSet2.xlsx
Data=pd.read_json(In, orient = 'records', lines = True)
Data1=Data.loc[:,['id','date','tweet_body','Lemitized','Keyword','Hashtag',]] 
#
### INFREQUENT WORDS SCALING
#
if len(Data1)<=100:
    min_count=5
elif len(Data1)<=1000 and len(Data1)>100:
    min_count=10
elif len(Data1)<=10000 and len(Data1)>1000:
    min_count=15
elif len(Data1)<=100000 and len(Data1)>10000:
    min_count=20 
elif len(Data1)<=1000000 and len(Data1)>100000:
    min_count=25 
elif len(Data1)<=5000000 and len(Data1)>1000000:
    min_count=30                  
elif len(Data1)<=10000000 and len(Data1)>5000000:
    min_count=35
elif len(Data1)>10000000:
    min_count=40  # min value     
#
### Hashtag Frequency
#
Token1=Data1['Hashtag']
Total_Hashtag_Count=Counter()
for line in Token1:
    for x in line.split():
        if '#' in x:
            Total_Hashtag_Count[x] +=1 
#removing min count            
if len(Total_Hashtag_Count)>0:
    for x in list(Total_Hashtag_Count.keys()):     
        if Total_Hashtag_Count[x]<=min_count:
            del Total_Hashtag_Count[x]       
else:
    Hashtag=pd.DataFrame()       
##next loop after removal of min count
if len(Total_Hashtag_Count)>0:   
    Total_Hashtag_Count=sorted(Total_Hashtag_Count.items(), key=lambda pair: pair[1], reverse=True)# sorting converts to list 
    Total_Hashtag_Count= [x for x in Total_Hashtag_Count if x[0]!= ('#')]
    Hashtag=pd.DataFrame(Total_Hashtag_Count) # list of tuples to dataframe
    Hashtag=Hashtag.rename(columns={0:'Word',  1:'Frequency'})
    Hashtag['Type'] ='Hashtag'
else:
    Hashtag=pd.DataFrame()         
#
### Keyword Frequency
#      
Token2=Data1['Keyword'].apply(word_tokenize) #.to_list() for later to identify directional co-occurance
Total_KW_Count=Counter()
for text in Token2:
    for x in text:
        Total_KW_Count[x] +=1
#removing min count
for x in list(Total_KW_Count.keys()):     
    if Total_KW_Count[x]<min_count:
        del Total_KW_Count[x]          
Total_KW_Count=sorted(Total_KW_Count.items(), key=lambda pair: pair[1], reverse=True)# sorting converts to list   
Total_KW_Count= [x for x in Total_KW_Count if len(x[0])>2]     
KW=pd.DataFrame(Total_KW_Count) # list of tuples to dataframe
KW=KW.rename(columns={0:'Word',  1:'Frequency'}) # Freq is occurance frequency
KW['Type'] ='KW'    
del [Token1,Token2,x,Total_KW_Count,Total_Hashtag_Count,text,line]       
#
### Subsampling 
#
Word_Index=pd.concat([KW,Hashtag], ignore_index=True) #,Emoji
Word_Index=Word_Index.sort_values(by=['Frequency'], ascending=False)
Word_Index=Word_Index.reset_index(drop=True)
Word_Index['Prob']=Word_Index['Frequency']/len(Data1) #1 - np.sqrt(0.00001/Word_Index['Frequency'])
Word_Index=Word_Index[Word_Index['Prob']<0.5]    
Word_Index=Word_Index.reset_index(drop=True)
VoCab=Word_Index['Word'] 
del [Word_Index,KW,Hashtag,min_count]    
#
### BERT TOPIC MODEL - Hyperparameter Tuning https://medium.com/grabngoinfo/hyperparameter-tuning-for-bertopic-model-in-python-104445778347
#
embedding_model = SentenceTransformer("all-MiniLM-L6-v2") #Extract embeddings
embeddings = embedding_model.encode(Data1['Lemitized'], show_progress_bar=False)
# embeddings = normalize(embeddings) # while using cuml...normalize them first to force a cosine-related distance metric in UMAP:
# Initialize and rescale PCA embeddings - Start UMAP from PCA embeddings to speed up UMAP and avoid convergence issues
pca_embeddings = rescale(PCA(n_components=5).fit_transform(embeddings)) # starting point to UMPA
umap_model = UMAP(n_neighbors=15, n_components=5, min_dist=0.0, metric='cosine',init=pca_embeddings,random_state=100) #Reduce dimensionality
hdbscan_model = HDBSCAN(min_cluster_size=100,min_samples=5, metric='euclidean', cluster_selection_method='eom', prediction_data=True) #Cluster reduced embeddings min_df=min_count
vectorizer_model = CountVectorizer(stop_words="english",token_pattern=r'\b\w\w+\b|(?<!\w)@\w+|(?<!\w)#\w+',vocabulary=VoCab) #Tokenize topics
ctfidf_model = ClassTfidfTransformer(reduce_frequent_words=True) #Create topic representation
#
### Putting all hyper-parameters together
#
topic_model = BERTopic(embedding_model=embedding_model,umap_model=umap_model,hdbscan_model=hdbscan_model,vectorizer_model=vectorizer_model,ctfidf_model=ctfidf_model,diversity=0.8,top_n_words=4,nr_topics='auto',low_memory=True, calculate_probabilities=False)
#
### Themes
#
topics, probs = topic_model.fit_transform(Data1['Lemitized'],embeddings) # 
Themes=topic_model.get_topic_info()
#Theme_Tweets=topic_model.get_document_info(Data1['Lemitized'])   
#
### Outlier Reduction 
#
if  Themes.loc[0,'Topic']==-1:
    topics_New=topic_model.reduce_outliers(Data1['Lemitized'], topics, probabilities=probs,threshold=0.05, strategy="c-tf-idf")
    topic_model.update_topics(Data1['Lemitized'], topics=topics_New)
    Themes_New=topic_model.get_topic_info()
    #Theme_Tweets_New=topic_model.get_document_info(Data1['Lemitized'])
else:
    topics_New= topics
    Themes_New= Themes
del [topics,probs,Themes]
#
### Topic Reduction
#
if len(Themes_New)>11:
    topic_model.reduce_topics(Data1['Lemitized'], nr_topics=15)
    Themes_New_R=topic_model.get_topic_info()
    topics_New_R = topic_model.topics_
    #Theme_Tweets_New=topic_model.get_document_info(Data1['Lemitized'])
else:
    topics_New_R= topics_New
    Themes_New_R= Themes_New
    #Theme_Tweets_New=Theme_Tweets
del [topics_New,Themes_New]
#Theme_Tweets_New_R=topic_model.get_document_info(Data1['Lemitized'])
Keywords10_Theme_weight=topic_model.topic_representations_
Data1['Themes'] = topics_New_R  
#
### Stories topic_model,, Keywords10_Theme_weight
#
for x2 in range(len(Themes_New_R)): # x2=0
    My_DF2=Data1[Data1['Themes']==x2].reset_index()
    #
    ### Hyperparameter Tuning
    #
    embedding_model1 = SentenceTransformer("all-MiniLM-L6-v2") #Extract embeddings
    embeddings1 = embedding_model1.encode(My_DF2['Lemitized'], show_progress_bar=False)
    # Initialize and rescale PCA embeddings - Start UMAP from PCA embeddings to speed up UMAP and avoid convergence issues
    pca_embeddings1 = rescale(PCA(n_components=5).fit_transform(embeddings1)) # starting point to UMPA
    umap_model1 = UMAP(n_neighbors=15, n_components=5, min_dist=0.0, metric='cosine',init=pca_embeddings1,random_state=100) #Reduce dimensionality
    hdbscan_model1 = HDBSCAN(min_cluster_size=25,min_samples=5, metric='euclidean', cluster_selection_method='eom', prediction_data=True) #Cluster reduced embeddings min_df=min_count
    vectorizer_model1 = CountVectorizer() #Tokenize topics
    ctfidf_model1 = ClassTfidfTransformer(reduce_frequent_words=True) #Create topic representation
    #
    ### Putting all hyper-parameters together
    #
    topic_model1 = BERTopic(embedding_model=embedding_model1,umap_model=umap_model1,hdbscan_model=hdbscan_model1,vectorizer_model=vectorizer_model1,ctfidf_model=ctfidf_model1,diversity=0.8,top_n_words=4,nr_topics=12,low_memory=True, calculate_probabilities=False)
    #
    #
    ### Stories  
    #
    topics1, probs1 = topic_model1.fit_transform(My_DF2['Lemitized'],embeddings1) # 
    Stories=topic_model1.get_topic_info()
    #Stories_Tweets=topic_model1.get_document_info(My_DF2['Lemitized'])   
    #
    ### Outlier Reduction
    #
    if  Stories.loc[0,'Topic']==-1:
        topics_New1=topic_model1.reduce_outliers(My_DF2['Lemitized'], topics1, probabilities=probs1,threshold=0.05, strategy="c-tf-idf")
        topic_model1.update_topics(My_DF2['Lemitized'], topics=topics_New1)
        Stories_New=topic_model1.get_topic_info()
        #Theme_Tweets_New=topic_model.get_document_info(My_DF1['Lemitized'])
    else:
        topics_New1= topics1
        Stories_New= Stories
    #del [topics1,probs1,Stories]
    #
    ### Topic Reduction
    #
    if len(Stories_New)>11:
        topic_model1.reduce_topics(My_DF2['Lemitized'], nr_topics=10)
        Stories_New_R=topic_model1.get_topic_info()
        topics_New_R1 = topic_model1.topics_
        #Theme_Tweets_New=topic_model.get_document_info(My_DF1['Lemitized'])
    else:
        topics_New_R1= topics_New1
        Stories_New_R= Stories_New
        #Theme_Tweets_New=Theme_Tweets
    #del [topics_New1,Stories_New,topics1,probs1,Stories]
    #Stories_Tweets_New_R=topic_model.get_document_info(My_DF2['Lemitized'])
    Keywords10_Stories_weight=topic_model1.topic_representations_
    #
    ### Inputing Story Data
    #
    ind=My_DF2['index'].tolist()
    Data1.loc[ind,'Stories']=topics_New_R1  
    del [My_DF2,embedding_model1,embeddings1,pca_embeddings1,umap_model1,hdbscan_model1,vectorizer_model1,ctfidf_model1,topic_model1,topics1, probs1,Stories,topics_New1,Stories_New,topics_New_R1,Stories_New_R,Keywords10_Stories_weight,ind]
del [embedding_model,embeddings,pca_embeddings,umap_model,hdbscan_model,vectorizer_model,ctfidf_model,topic_model,Keywords10_Theme_weight,x2,VoCab,topics_New_R,Themes_New_R]  #Data,  
#
### Output    
#
Out='C:\\Users\\<USER>\\Documents\\NeurIPS_2024\\'
Folder_Out= 'Results' 
Out_Path=os.path.join(Out,Folder_Out) 
if not os.path.exists(Out_Path):
    os.makedirs(Out_Path)
#Excel
Output_File=os.path.join(Out_Path,'chatgpt_streaming_tweets_300k_TSC.xlsx')
writer = ExcelWriter (Output_File)
Data1.to_excel(writer,'Sheet1')
writer.save()
#json
url_out2=os.path.join(Out_Path,'chatgpt_streaming_tweets_300k_TSC.json')
Out2=Data1.to_json(orient='records',lines = 'True', default_handler=str) #
open(url_out2,"w").write(Out2)  

