"""
This module defines classes and methods for managing MongoDB connections and 
performing operations on organization data within the database.
"""

from bson import ObjectId
from utils.mongo_db import get_mongodb_client, get_mongodb_collection, get_mongodb_db
from organization_vault.const import (
    MONGO_URL,
    ORGANIZATION_VAULT_DB_NAME,
    ORGANIZATION_COLLECTION_NAME_SUFFIX,
)


class MongoDBConnection:
    """
    MongoDB connection manager.

    This class manages the connection to the MongoDB database.
    """

    def __init__(self):
        self.mongo_db_client = get_mongodb_client(MONGO_URL)
        self.organization_vault_db = get_mongodb_db(
            self.mongo_db_client, ORGANIZATION_VAULT_DB_NAME
        )


class OrganizationModel:
    """
    Organization data model.

    This class provides methods to interact with organization data in the MongoDB database.
    """

    def __init__(self, db, organization_id):
        organization_collection_name = (
            f"{organization_id}_{ORGANIZATION_COLLECTION_NAME_SUFFIX}"
        )
        self.organization_collection = get_mongodb_collection(
            db, organization_collection_name
        )

    def find_one_by_id(self, organization_id, projection):
        """
        Retrieve organization data by ID.

        Parameters:
        - organization_id (str): The ID of the organization.
        - projection (dict): Projection for query results.

        Returns:
        - dict: Organization data document.
        """
        return self.organization_collection.find_one(
            {"organization_id": ObjectId(organization_id)}, projection=projection
        )
