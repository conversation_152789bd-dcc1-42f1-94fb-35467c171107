"""
Module for defining routes related to organization functionality in the Flask application.
"""

from flask import Blueprint, request
from organization.controllers import get_organization_data

# Create a new Blueprint for organization-related routes
organization_blueprint = Blueprint("organization", __name__)


# Define routes using the Blueprint
@organization_blueprint.route("/", methods=["GET"])
def get_organization_route():
    """
    Route handler for fetching organization data based on the provided organization ID.

    Returns:
    - tuple: A tuple containing the JSON response and the HTTP status code.
             The JSON response contains the organization data or an error message.
             The HTTP status code indicates the success or failure of the request.
    """
    organization_id = request.args.get("organization_id")
    return get_organization_data(organization_id)
