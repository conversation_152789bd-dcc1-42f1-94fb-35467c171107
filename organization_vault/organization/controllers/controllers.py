"""
This module provides functions to validate the organization ID, interact with the 
database to retrieve organization data, and handle various error scenarios."""

from flask import jsonify
from bson import ObjectId
from organization.models import MongoDBConnection, OrganizationModel
from utils.logger import logger


def handle_invalid_id(organization_id: str) -> None:
    """
    This function checks if the organization ID is missing or invalid.

    Parameters:
    - organization_id (str): The organization ID to validate.

    Raises:
    - ValueError: If the organization ID is missing.
    - ValueError: If the organization ID is invalid.
    """
    if not organization_id:
        raise ValueError("Organization ID is missing")

    if not ObjectId.is_valid(organization_id):
        raise ValueError("Invalid organization ID")


def get_organization_data(organization_id: str) -> tuple:
    """
    Retrieve organization data based on the provided organization ID.

    Parameters:
    - organization_id (str): The organization ID to retrieve data for.

    Returns:
    - tuple: A tuple containing the JSON response and the HTTP status code.
             The JSON response contains the organization data or an error message.
             The HTTP status code indicates the success or failure of the request.

    Raises:
    - ValueError: If organization ID is missing or invalid.
    - Exception: If there is an error during data retrieval from the database.
    """
    try:
        mongo_conn = MongoDBConnection()

        # Validate organization ID
        handle_invalid_id(organization_id)

        organization_model = OrganizationModel(
            mongo_conn.organization_vault_db, organization_id
        )

        projection = {"_id": 0, "organization_id": 0}

        organization_data = organization_model.find_one_by_id(
            organization_id, projection
        )

        if not organization_data:
            return jsonify({"error": "Organization not found"}), 404

        return jsonify(organization_data), 200

    except ValueError as ve:
        logger.error(str(ve))
        return jsonify({"error": str(ve)}), 400

    except Exception as e:
        logger.exception(
            "Failed to fetch data for organization ID '%s': %s", organization_id, e
        )
        return jsonify({"error": "Failed to fetch data for organization ID"}), 500
