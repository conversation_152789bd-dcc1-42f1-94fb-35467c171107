import os
from datetime import datetime, timezone, timedelta
from bson import ObjectId
import pandas as pd
import functions_framework
from utils.common_utils import convert_objectid_to_string
from utils.logger import logger
from utils.mongo_db import (
    get_mongodb_client,
    get_mongodb_collection,
    get_mongodb_db,
)
from utils.utilities import Diagnostic<PERSON>tatus, MessageType
from utils.pubsub_publisher import (
    create_notification_pubsub_payload,
    publish_pubsub_message,
)

CAPABILITIES_MONGO_URI = os.getenv("CAPABILITIES_MONGO_URI")
CAPABILITIES_DB_NAME = os.getenv("CAPABILITIES_DB_NAME")
CAPABILITIES_DIAGNOSTIC_COLLECTION = os.getenv("CAPABILITIES_DIAGNOSTIC_COLLECTION")

PROJECT_ID = os.getenv("PROJECT_ID")
RELATIVE_TIME_DELTA = os.getenv("RELATIVE_TIME_DELTA")
NOTIFICATION_TOPIC_ID = os.getenv("NOTIFICATION_TOPIC_ID")


def fetch_diagnostic_logs(collection, current_date):
    """
    Fetch diagnostic logs from MongoDB diagnostic collection for the current day with specific statuses.

    - This function queries the MongoDB diagnostic collection to retrieve diagnostic logs created within the specified current date.
    - It filters logs with statuses indicating incomplete processing, including INACTIVE, PENDING, or FAILED.

    Parameters:
    - collection (pymongo.collection.Collection): The MongoDB diagnostic collection object.
    - current_date (date): The current date.

    Returns:
    - logs_df (pd.Dataframe): DataFrame of diagnostic logs.
    """
    try:
        start_of_day = datetime.combine(current_date, datetime.min.time())
        end_of_day = datetime.combine(current_date, datetime.max.time())
        query = {
            "created_at": {"$gte": start_of_day, "$lt": end_of_day},
            "status": {
                "$in": [
                    DiagnosticStatus.INACTIVE.value,
                    DiagnosticStatus.PENDING.value,
                    DiagnosticStatus.FAILED.value,
                ]
            },
        }

        logs = list(collection.find(query))
        logs_df = pd.DataFrame(logs)
        return logs_df

    except Exception as e:
        logger.error(f"Error occurred while fetching diagnostic logs: {str(e)}")
        return None


def review_diagnostic_logs(logs_df, current_datetime):
    """
    Review diagnostic logs to identify incomplete consumers.

    - This function takes a DataFrame of diagnostic logs and the current datetime in UTC.
    - It identifies incomplete consumers based on the time difference between the current datetime and the 'updated_at' field in the logs.
    - The time difference threshold is defined by the RELATIVE_TIME_DELTA environment variable.

    Parameters:
    - logs_df (pd.Dataframe): DataFrame of diagnostic logs.
    - current_datetime (datetime): The current datetime in UTC.

    Returns:
    - incomplete_consumer_logs (list): A list of incomplete consumer logs.
    """
    try:
        time_difference_threshold = timedelta(hours=int(RELATIVE_TIME_DELTA))
        logs_df = logs_df.apply(lambda x: x.map(convert_objectid_to_string))
        incomplete_consumer_logs = logs_df[
            (
                current_datetime
                - (pd.to_datetime(logs_df["updated_at"]).dt.tz_localize(timezone.utc))
            )
            > time_difference_threshold
        ].to_dict(orient="records")

        return incomplete_consumer_logs

    except Exception as e:
        logger.error("An error occurred while reviewing diagnostic logs: '%s'", e)
        return None


def update_reviewer_status(collection, query_id, consumer_type, reviewer_status):
    """
    Update the reviewer status for a consumer type of a particular query_id in MongoDB diagnostic collection.

    Parameters:
    - collection (pymongo.collection.Collection): The MongoDB diagnostic collection object.
    - query_id (str): The ID of the query.
    - consumer_type (str): The type of consumer.
    - reviewer_status (int): The reviewer status to update (0 or 1).
    """
    try:
        query = {"query_id": ObjectId(query_id), "consumer_type": consumer_type}
        update_query = {"$set": {"reviewer_status": reviewer_status}}
        result = collection.update_one(query, update_query)
        logger.info(
            "Reviewer status updated for %s consumer_type and query_id: %s to %s with modified_count: '%d'",
            consumer_type,
            query_id,
            reviewer_status,
            result.modified_count,
        )

    except Exception as e:
        logger.error("An error occurred while updating reviewer status: %s", e)


# Triggered by an HTTP request.
@functions_framework.http
def main(_):
    """
    Main function to review diagnostic logs and publish message to the notification topic for incomplete consumers.

    - This function serves as the entry point for processing diagnostic logs and handling incomplete consumers.
    - It fetches diagnostic logs from the MongoDB collection for the current day, identifies incomplete consumers based on these logs,
    generates notification payloads for incomplete consumers, and publishes these payloads to a specified Pub/Sub topic.
    - If an incomplete consumer is found, its reviewer status is updated to 1 in the MongoDB collection to indicate that it has been reviewed.

    Returns:
    - dict: A dictionary indicating the status of the function execution, with a status code of 200 for success and 500 for errors.
    """
    try:
        mongo_db_client = get_mongodb_client(CAPABILITIES_MONGO_URI)
        capabilities_db = get_mongodb_db(mongo_db_client, CAPABILITIES_DB_NAME)
        diagnostic_collection = get_mongodb_collection(
            capabilities_db, CAPABILITIES_DIAGNOSTIC_COLLECTION
        )

        current_datetime = datetime.now(timezone.utc)
        current_date = current_datetime.date()

        logs_df = fetch_diagnostic_logs(diagnostic_collection, current_date)
        if logs_df is not None and not logs_df.empty:
            incomplete_consumer_logs = review_diagnostic_logs(logs_df, current_datetime)
            if incomplete_consumer_logs:
                for consumer_log in incomplete_consumer_logs:
                    consumer_type = consumer_log.get("consumer_type")
                    reviewer_status = consumer_log.get("reviewer_status")
                    consumer_status = consumer_log.get("status")
                    data_source_id = consumer_log.get("data_source_id")
                    query_id = consumer_log.get("query_id")
                    user_id = consumer_log.get("user_id")
                    message_detail = consumer_log.get("message")
                    organization_id = consumer_log.get("organization_id")
                    if not reviewer_status or pd.isna(reviewer_status):
                        status_messages = {
                            DiagnosticStatus.INACTIVE.value: f"{consumer_type} consumer is inactive and didn't start the processing.",
                            DiagnosticStatus.PENDING.value: f"{consumer_type} started successfully but is still processing.",
                            DiagnosticStatus.FAILED.value: f"{consumer_type} consumer failed to complete the processing due to: {message_detail}",
                        }
                        status_to_message_type = {
                            DiagnosticStatus.INACTIVE.value: MessageType.INACTIVE.value,
                            DiagnosticStatus.PENDING.value: MessageType.PENDING.value,
                            DiagnosticStatus.FAILED.value: MessageType.ERROR.value,
                        }

                        message = status_messages.get(consumer_status)
                        message_type = status_to_message_type.get(consumer_status)

                        payload = create_notification_pubsub_payload(
                            data_source_id,
                            query_id,
                            organization_id,
                            user_id,
                            consumer_type,
                            message_type,
                            message,
                            {},
                        )
                        publish_pubsub_message(
                            PROJECT_ID, NOTIFICATION_TOPIC_ID, payload
                        )
                        update_reviewer_status(
                            diagnostic_collection, query_id, consumer_type, 1
                        )
                    else:
                        logger.info(
                            "Message already sent to notification engine for query_id: '%s', and consumer_type: '%s'",
                            query_id,
                            consumer_type,
                        )
            else:
                logger.info(
                    "No incomplete consumer logs found for the current_datetime: '%s'.",
                    current_datetime,
                )
        else:
            logger.info(
                "No diagnostic logs found for the current_datetime: '%s' with statuses 'INACTIVE', 'PENDING', or 'FAILED'.",
                current_datetime,
            )

        return {"status": 200}

    except Exception as e:
        logger.error("An error occurred in diagnostic reviewer: %s", e)
        return {"status": 500}
