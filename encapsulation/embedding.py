"""
This module is responsible for generating and updating text 
embeddings in a MongoDB collection
"""

from typing import Union, Any, List
import os
import time
import pandas as pd
import numpy as np
from pymongo import UpdateOne
from pymongo.collection import Collection
from bson import ObjectId
import ratelimit
import backoff
import dask.dataframe as dd
from google.api_core import exceptions
from vertexai.language_models import TextEmbeddingModel, TextEmbeddingInput
from vertexai.generative_models import GenerativeModel
from encapsulation.const import (
    MAX_INPUT_TEXTS,
    MAX_RECORD_COUNT,
    MAX_TOKEN_COUNT,
    ZERO_VECTOR_SIZE,
    CALL_LIMIT,
    ONE_MINUTE,
    NINE_MINUTE,
    TASK_TYPE,
)
from utils.logger import logger

embedding_model = TextEmbeddingModel.from_pretrained("textembedding-gecko@003")
generative_model = GenerativeModel(model_name="gemini-1.0-pro-002")


def get_count_tokens(texts: List[str]) -> str:
    """This Function counts the token for the array of input texts"""
    # Prompt tokens count
    response = generative_model.count_tokens(texts)
    logger.info("Prompt Token Count: %s", {response.total_tokens})
    logger.info("Prompt Character Count: %s", {response.total_billable_characters})

    return response.total_billable_characters


def backoff_hdlr(details):
    """Function to print a wait message when it is retrying"""
    logger.info(
        "Backing off %s seconds after %s tries", details["wait"], details["tries"]
    )


# Decorate the function to retry on exceptions
@backoff.on_exception(  # Retry with exponential backoff strategy when exceptions occur
    backoff.expo,
    (
        exceptions.ResourceExhausted,  # Exceptions to retry on GRPC error code 429
        exceptions.InternalServerError,  # 503 error
        exceptions.FailedPrecondition,  # unknown server error
        exceptions.ServiceUnavailable,  # Exceptions to retry on GRPC server errors,
        ratelimit.RateLimitException,  # Exceptions to retry on rate limit exceeded
    ),
    max_time=NINE_MINUTE,  # Maximum time to retry
    on_backoff=backoff_hdlr,  # Function to call when retrying
)

# Decorate the function to limit the number of calls per minute
@ratelimit.limits(  # Limit the number of calls to the model per minute
    calls=CALL_LIMIT, period=ONE_MINUTE
)
# Union[pd.Series, Any]
def generate_embeddings(texts: List[str]) -> Union[np.ndarray, Any]:
    """
    This function returns the embeddings for the given input row

    Parameters:
    - texts (List(str)) : List of the texts that need to be embedded

    Return:
    - vector : Embedding for the text
    """
    try:
        embeddings = embedding_model.get_embeddings(texts)  # get embeddings
        vector = [embedding.values for embedding in embeddings]  # get embeddings vector
        return vector  # return embeddings vector
    except Exception as e:
        print("Exception during get_embeddings: ", e)
        raise  # Raising the exception to trigger backoff


def normalize(embeddings):
    """This function normalize the embedding of the"""
    if embeddings is None:
        embeddings = np.zeros(ZERO_VECTOR_SIZE)
    try:
        normalized_embedding = embeddings / np.linalg.norm(
            embeddings
        )  # normalize embeddings
    except ZeroDivisionError:
        normalized_embedding = embeddings
    return normalized_embedding


def update_embeddings(collection: Collection, encapsulation_marker: str) -> bool:
    """
    The function
    - Check for the number of records and skip if the record count is zero
        else fetch the max record count
    - Process the text parallel with the Dask with processes schedular and
        partition equal to cpu count
    - Split the input text from token count and or the maximum number of the
        texts per batch
    - get the embedding and concate the batches
    - Normalize the embedding for a partition of the dask
    - Update the embedding in RAC collection
    - Check if all the records are done and set the is_all_embedded to true

    Parameters:
    - collection (MongoDB.Collection) : Rac collection object of the query
    - encapsulation_marker (string): encapsulation marker of the collection

    Return
    - is_all_embedded (bool): Boolen for the if embedding generated of the all the
        records of an encapsulation marker

    Exception:
    - Catches and log any exception while prcocessing text and generating embedding
    """
    try:
        record_limit = MAX_RECORD_COUNT

        # Start time measurement
        start_time = time.perf_counter()

        # Define function to apply embedding in parallel
        def process_partition(partitioned_df):
            list_texts = partitioned_df["text"].tolist()
            texts = [
                TextEmbeddingInput(task_type=TASK_TYPE, text=text)
                for text in list_texts
            ]
            token_count = get_count_tokens(list_texts)

            # Calculate the number of the split from token count and or the maximum number of the texts per batch
            n = max(
                token_count // MAX_TOKEN_COUNT + 100,
                len(list_texts) // MAX_INPUT_TEXTS + 1,
            )

            # create the batches for embedding
            batches = np.array_split(texts, n)

            # Generate the embedding for each batch
            processesed_batches = [
                generate_embeddings(batch.tolist())
                for batch in batches
                if batch.tolist()
            ]

            # concate to get the embeddings for all the text
            embeddings = np.concatenate(processesed_batches)
            partitioned_df["embedding"] = embeddings.tolist()

            # normarlize the embedding
            partitioned_df["embedding"] = partitioned_df["embedding"].apply(normalize)
            return partitioned_df

        records = []

        query = {"encapsulation_marker": encapsulation_marker, "is_embedded": False}

        cursor = collection.find(query, {"_id": 1, "text": 1}).limit(record_limit)

        # Convert to DataFrame
        df = pd.DataFrame(cursor)
        record_count = len(df)
        if record_count != 0:

            # Convert DataFrame to Dask DataFrame
            ddf = dd.from_pandas(df, npartitions=os.cpu_count())

            result = ddf.map_partitions(
                process_partition,
                meta={"_id": "object", "text": "object", "embedding": "object"},
            )

            # Compute the result with schedular
            df = result.compute()

            results = df.to_dict(orient="records")

            for record in results:
                records.append(
                    UpdateOne(
                        {"_id": ObjectId(record["_id"])},
                        {
                            "$set": {
                                "embedding": record["embedding"].tolist(),
                                "is_embedded": True,
                            }
                        },
                    )
                )
            collection.bulk_write(records)
            is_all_embedded = (
                collection.count_documents(
                    {
                        "encapsulation_marker": encapsulation_marker,
                        "embedding": {"$exists": False},
                    }
                )
                == 0
            )
        else:
            is_all_embedded = True

        # End time measurement
        end_time = time.perf_counter()

        # Calculate the total time taken
        total_time = end_time - start_time

        logger.info(
            "Time taken to process %d records: %.2f seconds for encapsulation marker: %s",
            record_count,
            total_time,
            encapsulation_marker,
        )

        return is_all_embedded

    except Exception as e:
        message = f"An error occured while updating embedding {e}"
        logger.error(message)
        raise
