"""
This module contains constants used in files of encapsulation consumer
"""

import os

PROJECTION = {"source.data_source_name": 1, "meta_data": 1}
QUERY_COLLECTION = os.environ.get("QUERY_COLLECTION")
PROJECT_ID = os.environ.get("PROJECT_ID")
ENCAPSULATION_TOPIC_ID = os.getenv("ENCAPSULATION_TOPIC_ID")
SEQUENCE_COORDINATOR_TOPIC_ID = os.getenv("SEQUENCE_COORDINATOR_TOPIC_ID")

ENCAPSULATION_MARKER_COLLECTION_NAME_METADATA_KEY = (
    "ENCAPSULATION_MARKER_COLLECTION_NAME"
)

MAX_RECORD_COUNT = 50_000
MAX_TOKEN_COUNT = 20_000
MAX_INPUT_TEXTS = 250
ZERO_VECTOR_SIZE = 768

CALL_LIMIT = 1500  # Number of calls to allow within a period
ONE_MINUTE = 60  # One minute in seconds
NINE_MINUTE = 540  # Nine minutes in seconds
TASK_TYPE = "SEMANTIC_SIMILARITY"
