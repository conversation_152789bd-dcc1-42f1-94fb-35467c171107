"""
This module defines a Cloud Function that orchestrates the encapsulation process for a 
given query. It is triggered by a Cloud Pub/Sub message and performs the following tasks:

1. Parses the incoming payload to extract `query_id` and `organization_id`.
2. Retrieves query configuration and related metadata from MongoDB.
3. Generates and inserts encapsulation markers into the database.
4. Tracks and updates the embedding status of markers.
5. Publishes messages to Cloud Pub/Sub to coordinate further processing.
6. Handles errors and sends diagnostic updates.

The function integrates MongoDB, Cloud Pub/Sub, and various utilities to manage the 
encapsulation workflow for data processing
"""

from datetime import datetime
import base64
import json
from bson import ObjectId
import numpy as np
import pandas as pd
import functions_framework
from cloudevents.http import CloudEvent
from embedding import update_embeddings
import encapsulation
from encapsulation.const import (
    ENCAPSULATION_MARKER_COLLECTION_NAME_METADATA_KEY,
    PROJECTION,
    QUERY_COLLECTION,
    PROJECT_ID,
    ENCAPSULATION_TOPIC_ID,
    SEQUENCE_COORDINATOR_TOPIC_ID,
)
from utils.utilities import (
    CONSUMER_TYPE,
    DiagnosticActionType,
    DiagnosticStatus,
    EncapsulationMarkerStatus,
    EncapsulationMarkerStatusColumn,
    MetaContainer,
    OrganizationAccountInfo,
)
from utils.logger import logger
import utils.mongo_db as mongo
from utils.pubsub_publisher import publish_pubsub_message
import utils.common_utils as common_utils


def insert_encapsulation_markers(collection, encapsulation_markers, query_id):
    """
    Insert encapsulation markers into the MongoDB collection and returns the inserted data
    with encapsulation_markers_id's generated by MongoDB.

    Parameters:
    - collection (pymongo.collection.Collection): The MongoDB encapsulation
        marker collection to insert data into.
    - encapsulation_markers (list of dict): List of dictionaries containing
        encapsulation markers.
    - query_id (str): The query_id of a query collection document.

    Returns:
    - list or None: List of inserted documents with their _ids if successful, None otherwise.
    """
    try:
        inserted_ids = collection.insert_many(encapsulation_markers).inserted_ids
        logger.info(
            "Successfully inserted encapsulation markers for query_id: '%s' in '%s' collection.",
            query_id,
            collection.name,
        )

        # Fetch inserted data with their _ids and convert _id, query_id, start_date, and end_date to string
        pipeline = [
            {"$match": {"_id": {"$in": inserted_ids}}},
            {
                "$addFields": {
                    "_id": {"$toString": "$_id"},
                    "query_id": {"$toString": "$query_id"},
                    "encapsulation_start_date": {
                        "$dateToString": {
                            "format": "%Y-%m-%dT%H:%M:%S.%LZ",
                            "date": "$encapsulation_start_date",
                        }
                    },
                    "encapsulation_end_date": {
                        "$dateToString": {
                            "format": "%Y-%m-%dT%H:%M:%S.%LZ",
                            "date": "$encapsulation_end_date",
                        }
                    },
                }
            },
        ]

        # Execute the aggregation pipeline to get inserted data
        inserted_data = list(collection.aggregate(pipeline))

        return list(inserted_data)

    except Exception as e:
        message = f"Error inserting encapsulation markers for {query_id} query_id in {collection.name} collection: {e}"

        meta_container = MetaContainer()
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.FAILED.value,
            message,
        )
        return None


@functions_framework.cloud_event
def main(cloud_event: CloudEvent):
    """
    Cloud Function triggered by a Cloud Pub/Sub message.

    This function encapsulates data based on the payload received from a Pub/Sub message, including:
        - Parsing the payload and extracting query_id and organization_id.
        - Fetching organization account information.
        - Establishing MongoDB connection.
        - Retrieving query configuration.
        - Generating ISO calendar dates.
        - Generating markers based on query configuration and dates.
        - Updating markers in the collection.
        - Insert the marker, embedding_status, query_optimizer_status, sentiment_multi_status,
            sentiment_status, similarity_status, summarization_status, categorization_tool_status
            and capabilities sequence_coordinator_status in encapsulation_marker collection for query.
        - Iterate over all the encapsulation marker
          - Toggle the status to encapsulation pending for embedding
          - Update the embedding in RAC collection
          - Check if all the embeddings are completed for an encapsulation marker
            - Mark the embedding_status as completed for the encapsulation
            - Publishing message to Capabilities sequence coordinator Pub/Sub topic.
          - if all the encapsulation markers are embedded create vector index and toggle the status
            of diagnostic to complete else loopback for the remaining encapsulation marker

    Args:
    - cloud_event (CloudEvent): The CloudEvent object representing the incoming event.

    Returns:
    - Dict with status and message
        - status: 200 if success or 400 if any error occurs
        - message: message of success or error

    Exception:
     - If any error occurs during the execution process it will log and send it diagnostic.
    """
    mongodb_client = None
    try:
        meta_container = MetaContainer()

        # parse the payload and extract query_id and organization_id from payload
        payload = json.loads(
            base64.b64decode(cloud_event.data["message"]["data"]).decode("utf-8")
        )
        payload["publisher_type"] = CONSUMER_TYPE

        logger.info("Message received successfully: %s", payload)

        meta_container.set_payload_info(payload)

        query_id, organization_id = payload["query_id"], payload["organization_id"]

        message = f"Capabilities Encapsulation script started successfully for query_id: {query_id}"
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.PENDING.value,
            message,
        )

        # Fetch organization account information
        org_info = OrganizationAccountInfo(organization_id)
        mongodb_connection_string = org_info.mongodb_url
        db_name = org_info.organization_db_name

        # Establish MongoDB connection
        mongodb_client = mongo.get_mongodb_client(mongodb_connection_string)
        db = mongo.get_mongodb_db(mongodb_client, db_name)

        query_collection = mongo.get_mongodb_collection(db, QUERY_COLLECTION)
        query_config = common_utils.get_query_config(
            query_collection, query_id, PROJECTION
        )

        if query_config is not None:
            # Retrieve collection name
            meta_container.set_meta_data(query_config["meta_data"])
            rac_collection_name = meta_container.meta_data["RAC_COLLECTION_NAME"]
            encapsulation_marker_collection_name = meta_container.meta_data[
                ENCAPSULATION_MARKER_COLLECTION_NAME_METADATA_KEY
            ]
            rac_collection = mongo.get_mongodb_collection(db, rac_collection_name)

            encapsulation_marker_collection = mongo.get_mongodb_collection(
                db, encapsulation_marker_collection_name
            )

            # Generate ISO calendar dates
            start_date, end_date = encapsulation.generate_iso_calendar_for_date(
                rac_collection
            )

            marker_counts = encapsulation_marker_collection.count_documents(
                {"query_id": ObjectId(query_id)}
            )

            markers = {}

            if marker_counts == 0:
                # Generate markers based on query configuration and dates
                markers = encapsulation.generate_marker(
                    query_config, meta_container, start_date, end_date
                )

                # Update markers in rac_collection
                encapsulation.update_markers_in_collection(markers, rac_collection)
                rac_collection.create_index(
                    [("encapsulation_marker", 1), ("is_embedded", 1)]
                )

            result = rac_collection.aggregate(
                [
                    {
                        "$group": {
                            "_id": {
                                "marker": "$encapsulation_marker",
                                "record_type": "$record_type",
                            },
                            "count": {"$sum": 1},
                        }
                    },
                    {
                        "$group": {
                            "_id": "$_id.marker",
                            "record_count": {"$sum": "$count"},
                            "original_count": {
                                "$sum": {
                                    "$cond": [
                                        {"$eq": ["$_id.record_type", "comment"]},
                                        "$count",
                                        0,
                                    ]
                                }
                            },
                            "replies_count": {
                                "$sum": {
                                    "$cond": [
                                        {"$eq": ["$_id.record_type", "reply"]},
                                        "$count",
                                        0,
                                    ]
                                }
                            },
                        }
                    },
                ]
            )
            encapulation_marker_counts = pd.DataFrame(result)

            # Transform markers into a list of dictionaries
            encapsulation_markers_list = []
            for marker, details in markers.items():
                count_df = encapulation_marker_counts.loc[
                    encapulation_marker_counts["_id"] == marker,
                    ["record_count", "original_count", "replies_count"],
                ]

                count_dict = {}

                if not count_df.empty:
                    count_dict = dict(count_df.iloc[0])

                record_count = int(count_dict.get("record_count", 0))
                original_count = int(count_dict.get("original_count", 0))
                replies_count = int(count_dict.get("replies_count", 0))

                encapsulation_markers_list.append(
                    {
                        "encapsulation_marker": marker,
                        "query_id": ObjectId(query_id),
                        EncapsulationMarkerStatusColumn.CATEGORIZATION_TOOL_STATUS.value: EncapsulationMarkerStatus.INACTIVE.value,
                        EncapsulationMarkerStatusColumn.DISCOVER_STATUS.value: EncapsulationMarkerStatus.INACTIVE.value,
                        EncapsulationMarkerStatusColumn.EMBEDDING_STATUS.value: EncapsulationMarkerStatus.INACTIVE.value,
                        EncapsulationMarkerStatusColumn.QUERY_OPTIMIZER_STATUS.value: EncapsulationMarkerStatus.INACTIVE.value,
                        EncapsulationMarkerStatusColumn.SENTIMENT_STATUS.value: EncapsulationMarkerStatus.INACTIVE.value,
                        EncapsulationMarkerStatusColumn.SENTIMENT_MULTI_STATUS.value: EncapsulationMarkerStatus.INACTIVE.value,
                        EncapsulationMarkerStatusColumn.SEQUENCE_COORDINATOR_STATUS.value: EncapsulationMarkerStatus.INACTIVE.value,
                        EncapsulationMarkerStatusColumn.SIMILARITY_STATUS.value: EncapsulationMarkerStatus.INACTIVE.value,
                        EncapsulationMarkerStatusColumn.SUMMARIZATION_STATUS.value: EncapsulationMarkerStatus.INACTIVE.value,
                        "record_count": record_count,
                        "original_count": original_count,
                        "replies_count": replies_count,
                        "encapsulation_start_date": datetime.fromisoformat(
                            details["start_date"]
                        ),
                        "encapsulation_end_date": datetime.fromisoformat(
                            details["end_date"]
                        ),
                    }
                )

            encapsulation_markers_with_ids = None

            if marker_counts == 0:
                encapsulation_markers_with_ids = insert_encapsulation_markers(
                    encapsulation_marker_collection,
                    encapsulation_markers_list,
                    query_id,
                )

            else:
                encapsulation_markers_with_ids = encapsulation_marker_collection.find(
                    {
                        "query_id": ObjectId(query_id),
                        EncapsulationMarkerStatusColumn.EMBEDDING_STATUS.value: {
                            "$ne": EncapsulationMarkerStatus.COMPLETED.value
                        },
                    },
                    {
                        "_id": {"$toString": "$_id"},
                        "query_id": {"$toString": "$query_id"},
                        EncapsulationMarkerStatusColumn.EMBEDDING_STATUS.value: 1,
                        "encapsulation_marker": 1,
                        "record_count": 1,
                    },
                )

            if encapsulation_markers_with_ids is not None:
                # Publish message to Pub/Sub topic
                for encapsulation_payload in encapsulation_markers_with_ids:
                    # Get the encapsulation marker
                    encapsulation_marker = encapsulation_payload["encapsulation_marker"]
                    if encapsulation_payload["record_count"] == 0:
                        # Update the encapsulation marker
                        encapsulation_marker_collection.update_one(
                            {"_id": ObjectId(encapsulation_payload["_id"])},
                            {
                                "$set": {
                                    EncapsulationMarkerStatusColumn.CATEGORIZATION_TOOL_STATUS.value: EncapsulationMarkerStatus.COMPLETED.value,
                                    EncapsulationMarkerStatusColumn.DISCOVER_STATUS.value: EncapsulationMarkerStatus.COMPLETED.value,
                                    EncapsulationMarkerStatusColumn.EMBEDDING_STATUS.value: EncapsulationMarkerStatus.COMPLETED.value,
                                    EncapsulationMarkerStatusColumn.QUERY_OPTIMIZER_STATUS.value: EncapsulationMarkerStatus.COMPLETED.value,
                                    EncapsulationMarkerStatusColumn.SENTIMENT_STATUS.value: EncapsulationMarkerStatus.COMPLETED.value,
                                    EncapsulationMarkerStatusColumn.SENTIMENT_MULTI_STATUS.value: EncapsulationMarkerStatus.COMPLETED.value,
                                    EncapsulationMarkerStatusColumn.SEQUENCE_COORDINATOR_STATUS.value: EncapsulationMarkerStatus.COMPLETED.value,
                                    EncapsulationMarkerStatusColumn.SIMILARITY_STATUS.value: EncapsulationMarkerStatus.COMPLETED.value,
                                    EncapsulationMarkerStatusColumn.SUMMARIZATION_STATUS.value: EncapsulationMarkerStatus.COMPLETED.value,
                                }
                            },
                        )
                        break

                    # Update the encapsulation marker
                    encapsulation_marker_collection.update_one(
                        {"_id": ObjectId(encapsulation_payload["_id"])},
                        {
                            "$set": {
                                EncapsulationMarkerStatusColumn.EMBEDDING_STATUS.value: EncapsulationMarkerStatus.PENDING.value
                            }
                        },
                    )

                    # generate and update the embedding
                    is_all_embedded = update_embeddings(
                        rac_collection, encapsulation_marker
                    )

                    # is all embedded
                    if is_all_embedded:
                        rac_collection.update_many(
                            {
                                "encapsulation_marker": encapsulation_marker,
                                "embedding": {"$exists": False},
                            },
                            {"$set": {"embedding": list(np.zeros(768))}},
                        )

                        encapsulation_marker_collection.update_one(
                            {"_id": ObjectId(encapsulation_payload["_id"])},
                            {
                                "$set": {
                                    EncapsulationMarkerStatusColumn.EMBEDDING_STATUS.value: EncapsulationMarkerStatus.COMPLETED.value
                                }
                            },
                        )

                        payload.update(
                            {
                                "encapsulation": encapsulation_payload,
                                "encapsulation_marker": encapsulation_marker,
                            }
                        )

                        logger.info(
                            "Publishing message for encapsulation marker '%s' to Pub/Sub topic: %s",
                            encapsulation_marker,
                            SEQUENCE_COORDINATOR_TOPIC_ID,
                        )

                        publish_pubsub_message(
                            PROJECT_ID, SEQUENCE_COORDINATOR_TOPIC_ID, payload
                        )
                    break

                is_all_encapsulation_markers_completed = (
                    encapsulation_marker_collection.count_documents(
                        {
                            "query_id": ObjectId(query_id),
                            EncapsulationMarkerStatusColumn.EMBEDDING_STATUS.value: {
                                "$ne": EncapsulationMarkerStatus.COMPLETED.value
                            },
                        }
                    )
                    == 0
                )

                if is_all_encapsulation_markers_completed:

                    message = f"Capabilities encapsulation script completed successfully for query_id: {query_id}"
                    meta_container.send_diagnostic(
                        DiagnosticActionType.UPDATE.value,
                        DiagnosticStatus.COMPLETED.value,
                        message,
                    )

                else:

                    logger.info(
                        "Capabilities encapsulation script looping back for query_id: %s",
                        query_id,
                    )

                    publish_pubsub_message(PROJECT_ID, ENCAPSULATION_TOPIC_ID, payload)

                return {"status": 200, "message": message}

        else:
            message = f"Query details not found for id {query_id}"
            logger.warning(message)
            return {"status": 400, "message": message}

    except Exception as e:
        message = f"An error occurred during capabilities encapsulation: {e}"
        logger.exception(message)
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.FAILED.value,
            message,
            str(type(e)),
        )
        return {"status": 400, "message": message}

    finally:
        if mongodb_client is not None:
            mongodb_client.close()
