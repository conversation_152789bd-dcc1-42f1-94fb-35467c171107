"""
This module contains constants used in the main.py file of append consumer.
"""

import os
from utils.utilities import Sentiment<PERSON>rocessing<PERSON>ield, SummaryProcessingField

APPEND_TOPIC_ID = os.getenv("APPEND_TOPIC_ID")
SEQUENCE_COORDINATOR_TOPIC_ID = os.getenv("SEQUENCE_COORDINATOR_TOPIC_ID")
BASE_LOOPBACK_THRESHOLD = os.getenv("BASE_LOOPBACK_THRESHOLD")
CHUNK_SIZE = os.getenv("CHUNK_SIZE")
PROJECT_ID = os.getenv("PROJECT_ID")
QUERY_COLLECTION_NAME = os.getenv("QUERY_COLLECTION_NAME")
DATE_UTC_COLUMN = "date_utc"
APPEND_OFFSET_METADATA_KEY = "APPEND_OFFSET"
RAC_TRANSFORM_COLLECTION_NAME_METADATA_KEY = "RAC_TRANSFORM_COLLECTION_NAME"
BIG_QUERY_SCHEMA_METADATA_KEY = "BIG_QUERY_SCHEMA"
RAC_TRANSFORM_VOLUME_METADATA_KEY = "RAC_TRANSFORM_VOLUME"
RAC_TRANSFORM_QUERY_PROJECTION = {
    "embedding": 0,
    "is_embedded": 0,
    "POS": 0,
    SentimentProcessingField.CLUSTER.value: 0,
    SentimentProcessingField.STORY.value: 0,
    SentimentProcessingField.THEME.value: 0,
    SummaryProcessingField.CLUSTER.value: 0,
    SummaryProcessingField.STORY.value: 0,
    SummaryProcessingField.THEME.value: 0,
}
QUERY_PROJECTION_ATTRIBUTES = {
    f"meta_data.{APPEND_OFFSET_METADATA_KEY}": 1,
    f"meta_data.{RAC_TRANSFORM_COLLECTION_NAME_METADATA_KEY}": 1,
    f"meta_data.{RAC_TRANSFORM_VOLUME_METADATA_KEY}": 1,
    "source.meta_data": 1,
}
TYPE_MAPPING = {
    "snippet_canRate": "boolean",
    "snippet_canReply": "boolean",
    "snippet_isPublic": "boolean",
    "snippet_topLevelComment_snippet_canRate": "boolean",
    "video_info_contentDetails_licensedContent": "boolean",
    "date": "datetime64[ns]",
    "Hashtag_Position": "float64",
    "cluster_id": "int64",
    "EMOJIS_Unique_Count": "int64",
    "Hashtag_Unique_Count": "int64",
    "snippet_topLevelComment_snippet_likeCount": "int64",
    "snippet_totalReplyCount": "int64",
    "Stories": "int64",
    "Themes": "int64",
    "Unique_Cluster_ID": "int64",
    "Unique_Story_ID": "int64",
    "video_info_statistics_commentCount": "int64",
    "video_info_statistics_favoriteCount": "int64",
    "video_info_statistics_likeCount": "int64",
    "video_info_statistics_viewCount": "int64",
}
