"""
This module contains constants used in files of file_upload consumer.
"""

import os

BASE_LOOPBACK_THRESHOLD = os.getenv("BASE_LOOPBACK_THRESHOLD")
CHUNK_SIZE = os.getenv("CHUNK_SIZE")
PROJECT_ID = os.getenv("PROJECT_ID")
QUERY_COLLECTION_NAME = os.getenv("QUERY_COLLECTION_NAME")
CONSUMER_TYPE = os.getenv("CONSUMER_TYPE")
SEQUENCE_COORDINATOR_TOPIC_ID = os.getenv("SEQUENCE_COORDINATOR_TOPIC_ID")

CLEANSING_TOPIC_ID = os.getenv("CLEANSING_TOPIC_ID")
DATA_UNIFIER_TOPIC_ID = os.getenv("DATA_UNIFIER_TOPIC_ID")

DATA_UNIFIER_OFFSET_METADATA_KEY = "DATA_UNIFIER_OFFSET"
RAC_COLLECTION_NAME_METADATA_KEY = "RAC_COLLECTION_NAME"
RAC_VOLUME_METADATA_KEY = "RAC_VOLUME"
UNIFIER_META_METADATA_KEY = "UNIFIER_META"
QUERY_PROJECTION_ATTRIBUTES = {
    f"meta_data.{DATA_UNIFIER_OFFSET_METADATA_KEY}": 1,
    f"meta_data.{RAC_COLLECTION_NAME_METADATA_KEY}": 1,
    f"meta_data.{RAC_VOLUME_METADATA_KEY}": 1,
    f"meta_data.{UNIFIER_META_METADATA_KEY}": 1,
}
