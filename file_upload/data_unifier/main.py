"""
Cloud Function for unifying data from a query collection. Triggered by a Cloud Pub/Sub message, 
it processes and unifies data in chunks using the provided offset, and updates the 
MongoDB collection. The function manages diagnostics, tracks progress, and publishes messages 
to other Pub/Sub topics for further processing.
"""

import json
import base64
import functions_framework
from cloudevents.http import CloudEvent
from file_upload.data_unifier.const import (
    BASE_LOOPBACK_THRESHOLD,
    CHUNK_SIZE,
    DATA_UNIFIER_OFFSET_METADATA_KEY,
    DATA_UNIFIER_TOPIC_ID,
    PROJECT_ID,
    QUERY_COLLECTION_NAME,
    QUERY_PROJECTION_ATTRIBUTES,
    RAC_COLLECTION_NAME_METADATA_KEY,
    RAC_VOLUME_METADATA_KEY,
    UNIFIER_META_METADATA_KEY,
    CONSUMER_TYPE,
    SEQUENCE_COORDINATOR_TOPIC_ID
)
from utils.mongo_db import get_mongodb_collection, get_mongodb_db, get_mongodb_client
from utils.utilities import (
    DiagnosticActionType,
    DiagnosticStatus,
    MetaContainer,
    OrganizationAccountInfo,
)
from utils.common_utils import (
    common_data_unifer,
    create_projection,
    fetch_collection_data,
    get_query_config,
    update_offset_and_publish,
)
from utils.logger import logger
from utils.pubsub_publisher import publish_pubsub_message


@functions_framework.cloud_event
def main(cloud_event: CloudEvent):
    """
    Cloud Function triggered from a message on a Cloud Pub/Sub topic.

    This cloud function unifies data for file upload consumer. It uses the
    organization vault to get the organization and query_id to get RAC collection.
    It uses the UNIFIER_META and calls the common_data_unifier method to map
    the fields specified.

    Steps:
    - Decode the cloud event payload to extract relevant data.
    - Retrieve organization and query information from the payload.
    - Toggle diagnostic status to 'PENDING'.
    - Retrieve MongoDB connection details from the organization vault using the organization ID.
    - Fetch the query configuration from the MongoDB database.
    - Retrieve the RAC collection volume.
    - Process data in chunks while the offset is less than the loopback threshold:
        - Fetch query data in chunks using the provided offset and chunk size.
        - Unifies the data with mapping and updates the mapped field to
            destination collection.
        - If there are more documents to process (new_offset >= loopback_threshold),
            loop back with the new offset by sending pub sub message to data unifier topic id.
        - If all documents are processed (new_offset equals total_documents), send a
            message to the data cleansing Pub/Sub topic.
        - Toggle diagnostic status to 'COMPLETED'.
    - If an error occurs during the data unification, toggle diagnostic status to 'FAILED'.

    Parameters:
    - cloud_event (CloudEvent): The CloudEvent object representing the incoming event.

    Returns:
    - tuple: A success message and the corresponding HTTP status code.

    Exception Handling:
    - Exception: If any error occurs during the data unification process.
    """
    mongodb_client = None
    try:
        meta_container = MetaContainer()

        # Parse the payload and extract query_id and organization_id from payload
        payload = json.loads(
            base64.b64decode(cloud_event.data["message"]["data"]).decode("utf-8")
        )
        logger.info("Message received successfully: %s", payload)
        meta_container.set_payload_info(payload)

        query_id, organization_id = payload["query_id"], payload["organization_id"]

        message = f"Capabilities file upload data unifier script started successfully for query_id: {query_id}"
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.PENDING.value,
            message,
        )

        # Get organization details from organization_id
        organization = OrganizationAccountInfo(organization_id)

        # Set the database connection and get the query collection
        mongodb_client = get_mongodb_client(organization.mongodb_url)
        db = get_mongodb_db(mongodb_client, organization.organization_db_name)
        query_collection = get_mongodb_collection(db, QUERY_COLLECTION_NAME)

        # Get query details and check if not None
        query = get_query_config(
            query_collection, query_id, QUERY_PROJECTION_ATTRIBUTES
        )
        if not query:
            logger.warning("No configuration found for query_id: '%s'", query_id)
            return

        # Set the meta data
        meta_container.set_meta_data(query["meta_data"])
        offset = meta_container.meta_data.get(DATA_UNIFIER_OFFSET_METADATA_KEY, 0)

        # Set the query collection name
        rac_collection_name = meta_container.meta_data[RAC_COLLECTION_NAME_METADATA_KEY]
        rac_collection = get_mongodb_collection(db, rac_collection_name)
        unifier_meta = meta_container.meta_data[UNIFIER_META_METADATA_KEY]
        rac_query_projection = create_projection(unifier_meta)

        # Retrieve the RAC collection volume
        total_documents = meta_container.meta_data.get(
            RAC_VOLUME_METADATA_KEY
        ) or rac_collection.count_documents({}, hint="_id_")

        loopback_threshold = offset + int(BASE_LOOPBACK_THRESHOLD)
        while offset < loopback_threshold:
            # Load data in DataFrame and call the common_data_unifier with UNIFIER_META mapping
            rac_df = fetch_collection_data(
                rac_collection,
                {},
                rac_query_projection,
                offset,
                int(CHUNK_SIZE),
            )

            batch_count = len(rac_df)

            if not rac_df.empty:
                common_data_unifer(rac_df, unifier_meta, rac_collection)
                offset = update_offset_and_publish(
                    offset,
                    DATA_UNIFIER_OFFSET_METADATA_KEY,
                    total_documents,
                    loopback_threshold,
                    payload,
                    batch_count,
                    query_collection,
                    query_id,
                    DATA_UNIFIER_TOPIC_ID,
                )

                if offset == total_documents:
                    payload["publisher_type"] = CONSUMER_TYPE
                    publish_pubsub_message(PROJECT_ID, SEQUENCE_COORDINATOR_TOPIC_ID, payload)
                    message = f"Capabilities file upload data unifier script completed successfully for query_id: {query_id}"
                    meta_container.send_diagnostic(
                        DiagnosticActionType.UPDATE.value,
                        DiagnosticStatus.COMPLETED.value,
                        message,
                    )
                    break

            else:
                break

        return "Success", 200

    except Exception as e:
        message = f"An error occurred during capabilities file upload data unifier while updating the field mapping: {e}"
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.FAILED.value,
            message,
            str(type(e)),
        )

    finally:
        if mongodb_client is not None:
            mongodb_client.close()
