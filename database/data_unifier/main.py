"""
Module for handling data unification for capabilities database in response to 
Cloud Pub/Sub events.

This module contains a Cloud Function that is triggered by Cloud Pub/Sub messages, 
processes the data, and unifies database records by applying field mappings 
specified in the query metadata
"""

import json
import base64
import pandas as pd
import functions_framework
from cloudevents.http import CloudEvent
from database.const import (
    QUERY_COLLECTION,
    QUERY_COLLECTION_PROJECTION,
    PROJECT_ID,
    CLEANSING_TOPIC_ID,
)
from utils.mongo_db import get_mongodb_collection, get_mongodb_db, get_mongodb_client
from utils.utilities import (
    DiagnosticActionType,
    DiagnosticStatus,
    MetaContainer,
    OrganizationAccountInfo,
)
from utils.common_utils import get_query_config, common_data_unifer
from utils.logger import logger
from utils.pubsub_publisher import publish_pubsub_message


@functions_framework.cloud_event
def main(cloud_event: CloudEvent):
    """
    This cloud function unifies data for database consumer.
    It uses the organization vault to get the organization and query_id to get RAC collection.
    It uses the UNIFIER_META and calls the common_data_unifier method to map the fields specified

    Args:
    - cloud_event (CloudEvent): The CloudEvent object representing the incoming event.

    Returns:
    - Dict with status and message
        - status: 200 if success or 400 if any error occurs
        - message: message of success or error

    Exception Handling:
    - If any error occurs during the capabilities database data unifier execution process.
    """
    try:
        meta_container = MetaContainer()

        payload = json.loads(
            base64.b64decode(cloud_event.data["message"]["data"]).decode("utf-8")
        )

        logger.info("Message received successfully: %s", payload)

        meta_container.set_payload_info(payload)

        query_id, organization_id = payload["query_id"], payload["organization_id"]

        message = f"Capabilities database data unifier script started successfully for query_id: {query_id}"
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.PENDING.value,
            message,
        )

        # get organization details from organization_id
        organization = OrganizationAccountInfo(organization_id)

        # set the database connection and get the query collection
        client = get_mongodb_client(organization.mongodb_url)
        organization_db = get_mongodb_db(client, organization.organization_db_name)

        query_collection = get_mongodb_collection(organization_db, QUERY_COLLECTION)
        query = get_query_config(
            query_collection, query_id, QUERY_COLLECTION_PROJECTION
        )

        if query is not None:
            # set the meta data
            meta_container.set_meta_data(query["meta_data"])

            # set the query collection name
            collection_name = meta_container.meta_data["RAC_COLLECTION_NAME"]
            logger.info("collection name %s", collection_name)
            collection = get_mongodb_collection(organization_db, collection_name)

            # load data in DataFrame
            df = pd.DataFrame(list(collection.find({})))

            # call the common_data_unifer with UNIFIER_META mapping
            common_data_unifer(df, meta_container.meta_data["UNIFIER_META"], collection)
            publish_pubsub_message(PROJECT_ID, CLEANSING_TOPIC_ID, payload)

            message = f"Capabilities database data unifier script completed successfully for query_id: {query_id}"
            meta_container.send_diagnostic(
                DiagnosticActionType.UPDATE.value,
                DiagnosticStatus.COMPLETED.value,
                message,
            )
            return {"status": 200, "message": message}

        message = f"Query details not found for id: {query_id}"
        raise Exception(message) from e

    except Exception as e:
        message = f"An error occurred during capabilities database data unifier while updating the field mapping: {e}"
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.FAILED.value,
            message,
            str(type(e)),
        )
        return {"status": 400, "message": message}
