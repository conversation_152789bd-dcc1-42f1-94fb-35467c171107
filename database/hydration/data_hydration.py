"""
Module for transferring records between SQL and MongoDB databases.

This module contains functions to fetch records from either an SQL database or a 
source MongoDB collection and insert them into a target MongoDB collection in batches. 
The functions handle retries for failed insertions and provide detailed exception 
handling in case of errors. The processes ensure that the data transfer happens 
efficiently and reliably
"""

from typing import Dict, Optional
import numpy as np
import pandas as pd
from pymongo.collection import Collection
from sqlalchemy import text
from database.const import BATCH_SIZE, PROJECT_ID, TOPIC_ID, LOOPBACK_THRESHOLD
from utils.db_connection import MongoDBConnection, SQLDBConnection
from utils.logger import logger
from utils.pubsub_publisher import publish_pubsub_message


def fetch_and_insert_mongodb_records(
    query_id: str,
    organization_id: str,
    meta_data: Dict,
    target_collection: Collection,
    last_successful_offset: int,
    max_records: int,
    batch_size: Optional[int] = int(BATCH_SIZE),
    max_retries: Optional[int] = 5,
) -> bool:
    """
    Fetch records from a source MongoDB collection and insert them into a
    target MongoDB collection in batches.

    This function connects to a source MongoDB collection, retrieves documents
    in specified batch sizes, and inserts them into a target collection.
    If an insertion fails, it retries up to a specified number of times.

    Parameters:
    - query_id (str): Unique identifier for the query.
    - organization_id (str): Identifier for the organization.
    - meta_data (dict): A dictionary containing connection details for the source
        MongoDB database, including
      host, port, username, password, and the source collection name.
    - target_collection (pymongo.collection.Collection): The target MongoDB collection
        where the documents will be inserted.
    - last_successful_offset (int): The offset of the last successful insertion.
    - max_records (int): The maximum number of records to fetch and insert.
    - batch_size (int, optional): The number of records to fetch and insert per batch.
        Defaults to the value of the BATCH_SIZE constant.
    - max_retries (int, optional): The maximum number of retry attempts for failed insertions.
        Defaults to 5.

    Returns:
    - bool: Returns True if all documents are inserted successfully, False otherwise.

    Exception Handling:
    - Raises an exception if an error occurs during the fetching or insertion process, providing
    details about the source and target collections, as well as the specific error encountered.
    """
    try:
        connection = MongoDBConnection(meta_data)
        collection = connection.connect()
        total_docs = collection.count_documents({})
        logger.info("Total documents to fetch: %s", total_docs)

        if max_records is None:
            max_records = last_successful_offset + int(LOOPBACK_THRESHOLD)

        for batch_start in range(last_successful_offset, total_docs, batch_size):
            batch = list(
                collection.find().skip(last_successful_offset).limit(batch_size)
            )
            retry_count = 0

            while retry_count < max_retries:
                try:
                    target_collection.insert_many(batch)
                    inserted_count = target_collection.count_documents({})
                    last_successful_offset += len(batch)
                    logger.info(
                        "Inserted batch %s with %s records",
                        batch_start // batch_size + 1,
                        len(batch),
                    )
                    break

                except Exception as e:
                    retry_count += 1
                    logger.error(
                        "Failed to insert batch %s on attempt %s: %s",
                        batch_start // batch_size + 1,
                        retry_count,
                        e,
                    )

                    if retry_count == max_retries:
                        logger.error(
                            "Max retries reached for batch %s. Halting process.",
                            batch_start // batch_size + 1,
                        )
                        raise e

            if last_successful_offset >= max_records:
                max_records += int(LOOPBACK_THRESHOLD)
                payload = {
                    "query_id": query_id,
                    "organization_id": organization_id,
                    "db_hydration_loopback_checkpoint": {
                        "last_successful_offset": last_successful_offset,
                        "max_records": max_records,
                    },
                }
                publish_pubsub_message(PROJECT_ID, TOPIC_ID, payload)
                logger.info(
                    "Pub/Sub message published at offset %s.", last_successful_offset
                )
                break

        if total_docs == inserted_count:
            logger.info("All documents inserted successfully.")
            return True

        else:
            logger.warning(
                "Inserted count (%s) does not match fetched count (%s).",
                inserted_count,
                total_docs,
            )

    except Exception as e:
        message = f"Error occurred while fetching records from '{collection.name}' collection and insert into '{target_collection.name}' collection: {e}"
        raise Exception(message) from e

    return False


def fetch_and_insert_sql_records(
    query_id: str,
    organization_id: str,
    meta_data: Dict,
    target_collection: Collection,
    last_successful_offset: int,
    max_records: int,
    batch_size: Optional[int] = int(BATCH_SIZE),
    max_retries: Optional[int] = 5,
) -> bool:
    """
        Fetch records from an SQL database and insert them into a MongoDB collection in batches.

        This function connects to an SQL database, fetches records from a specified table,
        and inserts them into a MongoDB collection in batches. If an insertion fails,
        it retries up to a specified number of times.

        Parameters:
        - query_id (str): Unique identifier for the query.
        - organization_id (str): Identifier for the organization.
        - meta_data (dict): Metadata related to the SQL database connection.
            This dictionary should contain information such as host, port, username, password,
            database name, and the table name from which records will be fetched.
        - target_collection (pymongo.collection.Collection): The target MongoDB collection
            where the documents will be inserted.
        - last_successful_offset (int): The offset of the last successful insertion.
        - max_records (int): The maximum number of records to fetch and insert.
        - batch_size (int, optional): The number of records to fetch and insert per batch.
            Defaults to the value of the BATCH_SIZE constant.
        - max_retries (int, optional): The maximum number of retry attempts for failed insertions.
            Defaults to 5.

        Returns:
        - bool: Returns True if all documents are inserted successfully, False otherwise.
    count_documents
        Exception Handling:
        - Raises an exception if an error occurs during the fetching or insertion process,
            providing details about the source SQL table and the MongoDB collection,
            as well as the specific error encountered.
    """
    try:
        connection = SQLDBConnection(meta_data)
        engine, table_name = connection.connect()
        query = f"SELECT * FROM {table_name}"

        # Read the SQL data in batches
        with engine.connect() as conn:
            # return a single scalar value count
            total_records = conn.execute(
                text(f"SELECT COUNT(*) FROM {table_name}")
            ).scalar()
            logger.info("Total records to fetch: %s", total_records)

            if max_records is None:
                max_records = last_successful_offset + int(LOOPBACK_THRESHOLD)

            for offset in range(last_successful_offset, total_records, batch_size):
                batch_query = text(f"{query} LIMIT {batch_size} OFFSET {offset}")
                df = connection.select(batch_query)
                df.replace(
                    {
                        np.nan: None,
                        pd.NA: None,
                        pd.NaT: None,
                    },
                    inplace=True,
                )
                records_df = df.to_dict(orient="records")

                # Insert the records into MongoDB with retry mechanism
                retry_count = 0
                inserted_count = 0
                while retry_count < max_retries:
                    try:
                        if records_df:
                            target_collection.insert_many(records_df)
                            inserted_count = target_collection.count_documents({})
                            last_successful_offset += len(records_df)
                            logger.info(
                                "Inserted batch starting at offset %s with %s records",
                                offset,
                                len(records_df),
                            )
                        else:
                            logger.info(
                                "No records found in the batch starting at offset %s",
                                offset,
                            )
                        break

                    except Exception as e:
                        retry_count += 1
                        logger.error(
                            "Failed to insert batch %s on attempt %s: %s",
                            offset // batch_size + 1,
                            retry_count,
                            e,
                        )

                        if retry_count == max_retries:
                            logger.error(
                                "Max retries reached for batch %s. Halting process.",
                                offset // batch_size + 1,
                            )
                            raise e

                if last_successful_offset >= max_records:
                    max_records += int(LOOPBACK_THRESHOLD)
                    payload = {
                        "query_id": query_id,
                        "organization_id": organization_id,
                        "loopback_checkpoint": {
                            "last_successful_offset": last_successful_offset,
                            "max_records": max_records,
                        },
                    }

                    publish_pubsub_message(PROJECT_ID, TOPIC_ID, payload)
                    logger.info(
                        "Pub/Sub message published at offset %s.",
                        last_successful_offset,
                    )
                    break

            if total_records == inserted_count:
                logger.info("All documents inserted successfully.")
                return True

            logger.warning(
                "Inserted count (%s) does not match fetched count (%s).",
                inserted_count,
                total_records,
            )

    except Exception as e:
        message = f"Error occurred while fetching records from '{table_name}' table and insert into '{target_collection.name}' collection: {e}"
        raise Exception(message) from e

    return False
