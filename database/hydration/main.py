"""
This module contains function that processes the CloudEvent payload,
retrieves organization info, establishes a MongoDB connection, and handles data 
insertion.
"""

import base64
import json
import functions_framework
from cloudevents.http import CloudEvent
from data_hydration import (
    fetch_and_insert_mongodb_records,
    fetch_and_insert_sql_records,
)
from database.const import (
    PROJECTION,
    QUERY_COLLECTION,
    PROJECT_ID,
    DATA_UNIFIER_TOPIC_ID,
    RAC_COLLECTION_NAME_METADATA_KEY,
)
from utils.pubsub_publisher import publish_pubsub_message
from utils.logger import logger
from utils.mongo_db import (
    get_mongodb_client,
    get_mongodb_collection,
    get_mongodb_db,
)
from utils.common_utils import (
    get_query_config,
    generate_collection_name,
    update_query_metadata,
    update_volume,
)
from utils.utilities import (
    DiagnosticActionType,
    DiagnosticStatus,
    MetaContainer,
    OrganizationAccountInfo,
)

meta_container = MetaContainer()


@functions_framework.cloud_event
def main(cloud_event: CloudEvent):
    """
    Main function where execution starts after receiving a CloudEvent from a pubsub queue.
    It takes a payload as input, which is a dictionary containing organization_id and query_id.
    The function retrieves organization account information, establishes a MongoDB connection,
    finds the data source, and processes and inserts data accordingly.
    If any error occurs during the process, it logs the error and raises an exception.

    Parameters:
    - cloud_event (CloudEvent): The CloudEvent object representing the incoming event.

    Returns:
    - status: 200 if success or 400 if any error occurs

    Exception Handling:
    - If any error occurs during the capabilities database hydration.
    """
    try:
        payload = json.loads(
            base64.b64decode(cloud_event.data["message"]["data"]).decode("utf-8")
        )
        logger.info("Message received successfully: %s", payload)
        meta_container.set_payload_info(payload)

        query_id, organization_id = payload["query_id"], payload["organization_id"]

        # Retrieve organization account information
        organization_vault = OrganizationAccountInfo(organization_id)

        # Establish MongoDB connection
        client = get_mongodb_client(organization_vault.mongodb_url)
        organization_db = get_mongodb_db(
            client, organization_vault.organization_db_name
        )

        query_collection = get_mongodb_collection(organization_db, QUERY_COLLECTION)
        query_meta_data = get_query_config(query_collection, query_id, PROJECTION)

        meta_container.set_meta_data(
            {
                "data_source_name": query_meta_data["source"]["data_source_name"],
            }
        )
        message = f"Capabilities database hydration script started successfully for query_id: {query_id}"

        meta_container.send_diagnostic(
            DiagnosticActionType.INSERT.value,
            DiagnosticStatus.INACTIVE.value,
            message,
        )

        query_name = query_meta_data["name"]
        rac_collection_suffix = organization_vault.rac_collection_suffix

        # Find the data source
        org_data_source_meta_data = query_meta_data["source"]["meta_data"]
        rac_collection_name = generate_collection_name(
            query_id, query_name, rac_collection_suffix
        )

        update_query_metadata(
            query_collection,
            query_id,
            RAC_COLLECTION_NAME_METADATA_KEY,
            rac_collection_name,
        )

        target_collection = get_mongodb_collection(
            organization_db, query_meta_data["meta_data"]["RAC_COLLECTION_NAME"]
        )

        last_successful_offset = payload.get(
            "db_hydration_loopback_checkpoint", {}
        ).get("last_successful_offset", 0)

        max_records = payload.get("db_hydration_loopback_checkpoint", {}).get(
            "max_records", None
        )
        is_all_completed = False

        # Process and insert data
        if org_data_source_meta_data:
            match org_data_source_meta_data["DATABASE_TYPE"].lower():

                case "mongodb":
                    is_all_completed = fetch_and_insert_mongodb_records(
                        query_id,
                        organization_id,
                        org_data_source_meta_data,
                        target_collection,
                        last_successful_offset,
                        max_records,
                    )

                case "mysql" | "postgresql" | "oracle":
                    is_all_completed = fetch_and_insert_sql_records(
                        query_id,
                        organization_id,
                        org_data_source_meta_data,
                        target_collection,
                        last_successful_offset,
                        max_records,
                    )

                case _:
                    message = f"Unsupported database type: {org_data_source_meta_data["DATABASE_TYPE"]}"
                    raise Exception(message)

            if is_all_completed:
                payload.pop("db_hydration_loopback_checkpoint", None)
                update_volume(target_collection, query_collection, query_id)
                publish_pubsub_message(PROJECT_ID, DATA_UNIFIER_TOPIC_ID, payload)
                message = f"Capabilities database hydration script completed successfully for query_id: {query_id}"
                meta_container.send_diagnostic(
                    DiagnosticActionType.UPDATE.value,
                    DiagnosticStatus.COMPLETED.value,
                    message,
                )
            return {"status": 200}

        message = f"No data source found with query_id: {query_id} and organization_id: {organization_id}"
        raise Exception(message)

    except Exception as e:
        message = f"An error occurred during capabilities database hydration: {str(e)}"
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.FAILED.value,
            message,
            str(type(e)),
        )
        return {"status": 400}
