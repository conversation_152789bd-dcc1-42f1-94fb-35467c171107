"""
This module contains constants used in files of database consumer
"""

import os

PROJECTION = {
    "_id": 0,
    "meta_data": 1,
    "source.data_source_name": 1,
    "source.meta_data": 1,
    "name": 1,
}
RAC_COLLECTION_NAME_METADATA_KEY = "RAC_COLLECTION_NAME"
QUERY_COLLECTION = os.getenv("QUERY_COLLECTION")
BATCH_SIZE = os.getenv("BATCH_SIZE")
PROJECT_ID = os.getenv("PROJECT_ID")
TOPIC_ID = os.getenv("TOPIC_ID")
DATA_UNIFIER_TOPIC_ID = os.getenv("DATA_UNIFIER_TOPIC_ID")
LOOPBACK_THRESHOLD = os.getenv("LOOPBACK_THRESHOLD")

PROJECT_ID = os.getenv("PROJECT_ID")
CLEANSING_TOPIC_ID = os.getenv("CLEANSING_TOPIC_ID")
QUERY_COLLECTION_PROJECTION = {"_id": 1, "name": 1, "meta_data": 1}
