import base64
import os
import json
from const import (
    CLEANSING_TOPIC_ID,
    DATA_CLEANSING_OFFSET_METADATA_KEY,
    LOOPBACK_THRESHOLD,
    PROJECT_ID,
    QUERY_COLLECTION_NAME,
    QUERY_PROJECTION_ATTRIBUTES,
    RAC_VOLUME_METADATA_KEY,
    RAW_TEXT_FIELD_NAME,
    SEQUENCE_COORDINATOR_TOPIC_ID,
    CONSUMER_TYPE
)
import dask.dataframe as dd
import functions_framework
import numpy as np
import pandas as pd
from bson import ObjectId
import pymongo
from handlers.methods import (
    raw_filter,
    extract_hastags,
    extract_emojis,
    lemmatization,
    extract_keywords,
    extract_phrases,
    get_stop_words,
)
from utils.common_utils import (
    get_query_config,
    check_query_status,
    toggle_batch_status,
    update_offset_and_publish,
)
from utils.logger import logger
from utils.mongo_db import get_mongodb_client, get_mongodb_collection, get_mongodb_db
from utils.pubsub_publisher import publish_pubsub_message
from utils.utilities import (
    BatchStatus,
    BatchStatusColumn,
    DiagnosticActionType,
    DiagnosticStatus,
    MetaContainer,
    OrganizationAccountInfo,
)


meta_container = MetaContainer()

# Get stop words
stop_words = get_stop_words()


def start_common_cleansing(query_id):
    """
    Initiates the common data cleansing process.

    Parameters:
    - query_id (str): The ID of the query for which the cleansing process is initiated.
    """
    message = f"Capabilities common cleansing script started successfully for query_id: {query_id}"
    meta_container.send_diagnostic(
        DiagnosticActionType.UPDATE.value,
        DiagnosticStatus.PENDING.value,
        message,
    )


def fetch_collection_data(collection, offset, limit, batch_id=None):
    """
    Fetches data from a MongoDB collection associated with a specific batch if a batch_id is provided.
    Otherwise, retrieves all available data and returns it as a Pandas DataFrame.

    Parameters:
    - collection (pymongo.collection.Collection): The MongoDB collection to fetch data from.
    - offset (int): The number of documents to skip before starting to fetch.
    - limit (int): The maximum number of documents to fetch.
    - batch_id (str, optional): The batch ID to filter data. Defaults to None.

    Returns:
    - pandas.DataFrame or None: DataFrame containing the fetched data, or None if an error occurs.
    """
    try:
        query = {}  # Default query to fetch all documents
        if batch_id is not None:
            query["batch_id"] = ObjectId(batch_id)

        # Specify projection to fetch only specific columns
        projection = {"_id": 1, "text": 1}

        cursor = (
            collection.find(query, projection).sort("_id", 1).skip(offset).limit(limit)
        )

        # Convert cursor to DataFrame
        df = pd.DataFrame(cursor)
        logger.info(
            f"Successfully fetched {len(df)} record(s) from '{collection.name}' collection with (offset={offset}, limit={limit})"
            + (f" and '{batch_id}' batch_id" if batch_id else "")
        )
        return df

    except Exception as e:
        message = (
            f"An error occurred while fetching data for '{collection.name}' collection"
            + (f" and '{batch_id}' batch_id" if batch_id else "" + f": {e}")
        )
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.FAILED.value,
            message,
        )
        return None


def common_cleansing(raw_text_df, partition_info=None):
    """
    Perform common text cleansing operations on a DataFrame.

    - This function applies a series of common text cleansing operations on the specified DataFrame,
    including raw filtering, hashtag and emoji extraction, lemmatization, keyword extraction,
    phrase extraction, and replace null like values.

    Parameters:
    - raw_text_df (pd.DataFrame): The DataFrame containing raw text data.
    - partition_info (dict, optional): Information about the current partition being processed.
        Expected keys:
        - 'number': The index of the partition.
        - 'division': The starting index or position of the partition within the entire DataFrame.

    Returns:
    - pd.DataFrame: The cleansed partition of the raw text data.
    """
    # Replace None values in the "text" column with empty strings
    raw_text_df[RAW_TEXT_FIELD_NAME] = raw_text_df[RAW_TEXT_FIELD_NAME].replace(
        {None: ""}
    )

    # Apply raw filtering
    raw_filter(raw_text_df)
    logger.info("Raw Text Filtered Successfully for partition: %s", partition_info)

    # Extract hashtags
    extract_hastags(raw_text_df)
    logger.info("Hashtags Extracted Successfully for partition: %s", partition_info)

    # Extract emojis
    extract_emojis(raw_text_df)
    logger.info("Emojis Extracted Successfully for partition: %s", partition_info)

    # Lemmatize text
    lemmatization(raw_text_df, stop_words)
    logger.info("Text Lemmatized Successfully for partition: %s", partition_info)

    # Extract keywords
    extract_keywords(raw_text_df, stop_words)
    logger.info("Keywords Extracted Successfully for partition: %s", partition_info)

    # Extract phrases
    extract_phrases(raw_text_df)
    logger.info("Phrases Extracted Successfully for partition: %s", partition_info)

    # Replace null-like values in raw_text_df with None, preserving non-null values
    raw_text_df.replace(
        {
            np.nan: None,
            pd.NA: None,
            pd.NaT: None,
        },
        inplace=True,
    )

    # List of columns to check and update
    columns_to_check = ["BODY1", "Keyword", "Lemitized"]
    # Filter columns that exist in the DataFrame
    existing_columns = [col for col in columns_to_check if col in raw_text_df.columns]
    # Replace null values with empty strings in the existing columns
    raw_text_df[existing_columns] = raw_text_df[existing_columns].fillna("")

    return raw_text_df


def process_data_with_dask(raw_text_df):
    """
    Process raw text data using Dask DataFrame with specified cleansing function.

    Parameters:
    - raw_text_df (pd.DataFrame): Input Pandas DataFrame containing raw text data.

    Returns:
    - pd.DataFrame: Processed Pandas DataFrame containing the final results after computation using dask.
    """
    # Define metadata for the output Dask DataFrame, this dictionary specifies the expected data types for each column in the processed DataFrame.
    ddf_output_meta = {
        "_id": "object",
        "text": "object",
        "BODY1": "object",
        "Hashtag": "object",
        "Hashtag_Unique": "object",
        "Hashtag_Unique_Count": "int64",
        "Hashtag_Position": "float64",
        "EMOJIS": "object",
        "EMOJIS_Unique": "object",
        "EMOJIS_Unique_Count": "int64",
        "POS": "object",
        "Lemitized": "object",
        "Keyword": "object",
        "Phrase": "object",
    }
    # Convert DataFrame to Dask DataFrame with partitions based on CPU count
    ddf = dd.from_pandas(raw_text_df, npartitions=os.cpu_count())
    # Apply common cleansing function to each partition and compute the result
    raw_text_df = ddf.map_partitions(common_cleansing, meta=ddf_output_meta).compute(
        scheduler="processes"
    )
    logger.info(
        f"Successfully processed DataFrame using Dask. Length: {len(raw_text_df)}, Partitions: {ddf.npartitions}"
    )

    return raw_text_df


def update_mongodb_collection(collection, dataframe, batch_id=None):
    """
    Updates a MongoDB collection with data from a Pandas DataFrame using a bulk write operation.

    Parameters:
    - collection (pymongo.collection.Collection): The MongoDB collection to update.
    - dataframe (pandas.DataFrame): The DataFrame containing data to append.
    - batch_id (str, optional): The batch ID for a particular batch. Defaults to None.

    Returns:
    - bool: True if the collection update was successful, False otherwise.

    Raises:
    - ValueError: If the DataFrame does not contain an '_id' column.
    """
    try:
        # Check for mandatory '_id' column
        if "_id" not in dataframe.columns:
            raise ValueError(
                "DataFrame must contain an '_id' column for matching documents in MongoDB."
            )

        bulk_updates = []
        for _, row in dataframe.iterrows():
            mongo_id = ObjectId(row["_id"])
            update_data = {
                "$set": {col: row[col] for col in dataframe.columns if col != "_id"}
            }
            bulk_updates.append(pymongo.UpdateOne({"_id": mongo_id}, update_data))

        # Execute bulk updates
        result = collection.bulk_write(bulk_updates)
        matched_count = result.matched_count
        updated_count = result.modified_count
        logger.info(
            f"Updated {updated_count} documents in '{collection.name}' collection"
            + (f" for '{batch_id}' batch_id" if batch_id else "")
        )
        logger.info(
            "%d documents were not modified (already matched).",
            matched_count - updated_count,
        )
        return True

    except Exception as e:
        message = (
            f"An error occurred while updating the '{collection.name}' collection"
            + (f" for '{batch_id}' batch_id" if batch_id else "" + f": {e}")
        )
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.FAILED.value,
            message,
        )
        return False


def complete_common_cleansing(payload, query_id):
    """
    Completes the common data cleansing process.

    - This function publishes a message to a Pub/Sub topic with the payload
    and logs a success message indicating the completion of the cleansing process.

    Parameters:
    - payload (dict): The data payload to be published.
    - query_id (str): The ID of the query for which the cleansing process is completed.
    """
    payload["publisher_type"] = CONSUMER_TYPE
    publish_pubsub_message(PROJECT_ID, SEQUENCE_COORDINATOR_TOPIC_ID, payload)
    message = f"Capabilities common cleansing script completed successfully for query_id: {query_id}"
    meta_container.send_diagnostic(
        DiagnosticActionType.UPDATE.value,
        DiagnosticStatus.COMPLETED.value,
        message,
    )


@functions_framework.cloud_event
def main(cloud_event):
    """
    This function is the entry point that processes incoming cloud events, extracts relevant data, and initiates
    data cleansing operations based on the provided payload.

    Steps:
    - Decode the payload of the cloud event to extract relevant data.
    - Retrieve organization and query information from the payload.
    - Retrieve MongoDB connection details based on the organization ID from organization vault.
    - Fetch query configuration from the MongoDB database.
    - Determine if batch processing is enabled for the query.
    - If batch processing is enabled:
        - Start common cleansing for the query.
        - Toggle batch status to 'PENDING'.
        - Fetch raw text data associated with the batch ID.
        - Parallelize common cleansing on the batch data using Dask.
        - Update the 'RAC' collection with cleansed data.
        - Update batch status based on the success of cleansing.
        - If `new_offset` is less than `total_batch_documents`, loop back with the new offset.
        - If all batches are completed and `new_offset equals` the `total_batch_documents`, send a pub/sub message to the encapsulation consumer.
    - If batch processing is not enabled:
        - Start common cleansing for the query.
        - Fetch raw text data from the MongoDB collection.
        - Parallelize common cleansing operations using Dask.
        - Update the 'RAC' collection with cleansed data.
        - If `new_offset` is less than `total_documents`, loop back with the new offset.
        - If cleansing is successful, and `new_offset equals` the `total_documents` sends pub/sub message to encapsulation consumer.
    - Log any errors encountered during the process for troubleshooting purposes.

    Parameters:
    - cloud_event (google.cloud.functions.Context): CloudEvent data provided by the function framework.

    Returns:
    - {"Status":200}: For successfully execution.
    """
    mongodb_client = None
    try:
        payload = json.loads(
            base64.b64decode(cloud_event.data["message"]["data"]).decode("utf-8")
        )
        logger.info("Message received successfully: %s", payload)
        meta_container.set_payload_info(payload)

        organization_id_str = payload["organization_id"]
        query_id_str = payload["query_id"]

        org_account_info = OrganizationAccountInfo(organization_id_str)
        mongodb_url = org_account_info.mongodb_url
        organization_db_name = org_account_info.organization_db_name

        mongodb_client = get_mongodb_client(mongodb_url)
        organization_db = get_mongodb_db(mongodb_client, organization_db_name)
        query_collection = get_mongodb_collection(
            organization_db, QUERY_COLLECTION_NAME
        )

        query_config = get_query_config(
            query_collection, query_id_str, QUERY_PROJECTION_ATTRIBUTES
        )
        if not query_config:
            logger.warning("No configuration found for query_id: '%s'", query_id_str)
            return

        query_meta_data = query_config["meta_data"]
        query_meta_data = (
            query_meta_data[0] if isinstance(query_meta_data, list) else query_meta_data
        )
        source_meta_data = query_config["source"]["meta_data"]
        is_batch_enabled = source_meta_data["IS_BATCH_ENABLED"]
        rac_collection_name = query_meta_data["RAC_COLLECTION_NAME"]
        rac_collection = get_mongodb_collection(organization_db, rac_collection_name)
        offset = query_meta_data.get(DATA_CLEANSING_OFFSET_METADATA_KEY, 0)

        if is_batch_enabled:
            batch = payload["batch"]
            batch_id_str = batch["_id"]

            batch_collection_name = query_meta_data["BATCH_COLLECTION_NAME"]
            batch_collection = get_mongodb_collection(
                organization_db, batch_collection_name
            )

            # Fetch total batch document count
            total_batch_documents = rac_collection.count_documents(
                {"batch_id": ObjectId(batch_id_str)}
            )

            if check_query_status(
                batch_collection,
                query_id_str,
                BatchStatusColumn.CLEANSING_STATUS.value,
                BatchStatus.INACTIVE.value,
            ):
                start_common_cleansing(query_id_str)

            toggle_batch_status(
                batch_collection,
                batch_id_str,
                query_id_str,
                BatchStatusColumn.CLEANSING_STATUS.value,
                BatchStatus.PENDING.value,
            )
            raw_text_batch_df = fetch_collection_data(
                rac_collection, offset, int(LOOPBACK_THRESHOLD), batch_id_str
            )
            if raw_text_batch_df is not None and not raw_text_batch_df.empty:
                raw_text_batch_df = process_data_with_dask(raw_text_batch_df)
                batch_count = len(raw_text_batch_df)
                success = update_mongodb_collection(
                    rac_collection, raw_text_batch_df, batch_id_str
                )
                if success:
                    new_offset = update_offset_and_publish(
                        offset,
                        DATA_CLEANSING_OFFSET_METADATA_KEY,
                        total_batch_documents,
                        int(LOOPBACK_THRESHOLD),
                        payload,
                        batch_count,
                        query_collection,
                        query_id_str,
                        CLEANSING_TOPIC_ID,
                    )
                    if new_offset == total_batch_documents:
                        toggle_batch_status(
                            batch_collection,
                            batch_id_str,
                            query_id_str,
                            BatchStatusColumn.CLEANSING_STATUS.value,
                            BatchStatus.COMPLETED.value,
                        )
                        if check_query_status(
                            batch_collection,
                            query_id_str,
                            BatchStatusColumn.CLEANSING_STATUS.value,
                            BatchStatus.COMPLETED.value,
                        ):
                            complete_common_cleansing(payload, query_id_str)
                else:
                    toggle_batch_status(
                        batch_collection,
                        batch_id_str,
                        query_id_str,
                        BatchStatusColumn.CLEANSING_STATUS.value,
                        BatchStatus.FAILED.value,
                    )

        else:
            start_common_cleansing(query_id_str)
            # Retrieve the RAC collection volume
            total_documents = query_meta_data.get(
                RAC_VOLUME_METADATA_KEY
            ) or rac_collection.count_documents({}, hint="_id_")
            raw_text_df = fetch_collection_data(
                rac_collection, offset, int(LOOPBACK_THRESHOLD)
            )
            batch_count = len(raw_text_df)
            if raw_text_df is not None and not raw_text_df.empty:
                raw_text_df = process_data_with_dask(raw_text_df)
                success = update_mongodb_collection(rac_collection, raw_text_df)
                if success:
                    new_offset = update_offset_and_publish(
                        offset,
                        DATA_CLEANSING_OFFSET_METADATA_KEY,
                        total_documents,
                        int(LOOPBACK_THRESHOLD),
                        payload,
                        batch_count,
                        query_collection,
                        query_id_str,
                        CLEANSING_TOPIC_ID,
                    )
                    if new_offset == total_documents:
                        complete_common_cleansing(payload, query_id_str)

        mongodb_client.close()
        return {"Status": 200}

    except Exception as e:
        message = f"An error occurred during capabilities common cleansing: {e}"
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.FAILED.value,
            message,
        )
    finally:
        if mongodb_client is not None:
            mongodb_client.close()
