import os

QUERY_COLLECTION_NAME = os.getenv("QUERY_COLLECTION_NAME")
PROJECT_ID = os.getenv("PROJECT_ID")
CLEANSING_TOPIC_ID = os.getenv("CLEANSING_TOPIC_ID")
ENCAPSULATION_TOPIC_ID = os.getenv("ENCAPSULATION_TOPIC_ID")
LOOPBACK_THRESHOLD = os.getenv("LOOPBACK_THRESHOLD")
SEQUENCE_COORDINATOR_TOPIC_ID = os.getenv("SEQUENCE_COORDINATOR_TOPIC_ID")
CONSUMER_TYPE = os.getenv("CONSUMER_TYPE")

DATA_CLEANSING_OFFSET_METADATA_KEY = "DATA_CLEANSING_OFFSET"
RAC_VOLUME_METADATA_KEY = "RAC_VOLUME"
RAW_TEXT_FIELD_NAME = "text"
QUERY_PROJECTION_ATTRIBUTES = {
    "meta_data.BATCH_COLLECTION_NAME": 1,
    "meta_data.RAC_COLLECTION_NAME": 1,
    f"meta_data.{DATA_CLEANSING_OFFSET_METADATA_KEY}": 1,
    f"meta_data.{RAC_VOLUME_METADATA_KEY}": 1,
    "source.meta_data.IS_BATCH_ENABLED": 1,
}
