import os
import string
import pandas as pd
import yake
import nltk

# nltk dir path where nltk modules and files are stored
download_dir = os.path.abspath("data_files/nltk_dir")
nltk.data.path.append(download_dir)

from nltk.tokenize import regexp_tokenize, word_tokenize
from nltk.corpus import wordnet
from nltk.stem import WordNetLemmatizer

RAW_TEXT_FIELD_NAME = "text"

# This dictionary maps the first letter of part-of-speech tags to the corresponding wordnet constants
pos_dict = {
    "J": wordnet.ADJ,  # Adjective
    "V": wordnet.VERB,  # Verb
    "N": wordnet.NOUN,  # Noun
    "R": wordnet.ADV,  # Adverb
}

# string.punctuation removing # from the list so as to preserve hashtags
punct = list("!\"$%&'()*+,-./:;‘<=>?@[\\]``^_`{|}~“’")

# Create an instance of WordNetLemmatizer
lemmatizer = WordNetLemmatizer()


def get_stop_words():
    """
    Read the stopwords file from data_files directory and return list.

    Returns:
    - stp_wrd (list) : list of the stop words.
    """
    stp_wrd = pd.read_excel(
        "data_files/stopwords_discover.xlsx", sheet_name="Sheet1", engine="openpyxl"
    )
    stp_wrd = stp_wrd["SW3"].tolist()
    return stp_wrd


def get_wordnet_pos(word, stp_wrd):
    """
    Get the WordNet POS (Part of Speech) tag for a given word.

    Parameters:
    - word (str): The word for which the POS tag is to be determined.
    - stp_wrd (list) : list of the stop words.

    Returns:
    - list: A list of tuples containing the word and its corresponding WordNet POS tag.
            The format of each tuple is (word, pos_tag), where pos_tag can be one of:
            - wordnet.ADJ for adjective
            - wordnet.VERB for verb
            - wordnet.NOUN for noun
            - wordnet.ADV for adverb
    """
    tags = nltk.pos_tag(word_tokenize(word))
    newlist = []
    for word, tag in tags:
        if word.lower() not in stp_wrd:
            newlist.append(tuple([word, pos_dict.get(tag[0])]))
    return newlist


def lemmatize(pos_data):
    """
    Lemmatize a list of words with their corresponding WordNet POS (Part of Speech) tags.

    Parameters:
    - pos_data (list): A list of tuples containing words and their corresponding WordNet POS tags.
                        Each tuple should be in the format (word, pos_tag), where pos_tag can be:
                        - None if the POS tag is unknown or not available
                        - 'a' for adjective
                        - 'v' for verb
                        - 'n' for noun
                        - 'r' for adverb

    Returns:
    - str: A string containing the lemmatized words joined together.
    """
    lemma_rew = " "
    for word, pos in pos_data:
        if not pos:
            lemma = word
            lemma_rew = lemma_rew + " " + lemma
        else:
            lemma = lemmatizer.lemmatize(word, pos=pos)
            lemma_rew = lemma_rew + " " + lemma
    return lemma_rew


def raw_filter(df):
    """
    Filters raw text by removing URLs, RTs, mentions, numbers, punctuation, and other characters.

    Parameters:
    - df (pandas.DataFrame): The input DataFrame.

    Raises:
    - Exception: If an error occurs during the filtering process.
    """
    try:
        df["BODY1"] = df[RAW_TEXT_FIELD_NAME].str.lower()  # convert all to small
        df["BODY1"] = df["BODY1"].str.replace(
            r"((https|http)?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w\.-]*)*\/?\S",
            "",
            regex=True,
        )  # remove all url
        df["BODY1"] = df["BODY1"].str.replace(
            r"\b(?:rt|RT)\b", "", regex=True
        )  # remove RT...it can be reply to RT
        # remove @mentions....keep space between' '
        df["BODY1"] = df["BODY1"].str.replace(
            r"(^|[^@\w])@(\w{1,25})", r"\1 ", regex=True
        )
        df["BODY1"] = df["BODY1"].apply(
            lambda x: x.translate(x.maketrans("", "", string.digits))
        )  # remove all numbers
        df["BODY1"] = df["BODY1"].str.replace("'", "", regex=True)
        df["BODY1"] = df["BODY1"].str.replace('"', "", regex=True)
        df["BODY1"] = df["BODY1"].apply(
            lambda x: " ".join(
                k for k in word_tokenize(x) if k not in punct or k == "#"
            )
        )
        df["BODY1"] = df["BODY1"].str.replace("# ", "#", regex=True)
        df["BODY1"] = df["BODY1"].str.replace("##", "#", regex=True)
        df["BODY1"] = df["BODY1"].str.replace(" # ", "", regex=True)
        df["BODY1"] = df["BODY1"].str.replace("-", "", regex=True)
        df["BODY1"] = df["BODY1"].str.replace(".", "")
        df["BODY1"] = df["BODY1"].str.lstrip()  # striping left leading space
        df["BODY1"] = df["BODY1"].str.rstrip()  # striping right ending space
        df["BODY1"] = df["BODY1"].str.replace("  ", " ", regex=True)

    except Exception as e:
        message = f"Error filtering raw text: {e}"
        raise Exception(message)


def extract_hastags(df):
    """
    Extract and process hashtags from a DataFrame.

    - This function extracts hashtags from the raw text column of the DataFrame,
    creates columns for hashtags, unique hashtags, and computes additional features
    related to hashtags.

    Parameters:
    - df (pandas.DataFrame): The input DataFrame containing raw text data with hashtags.

    Raises:
    - Exception: If an error occurs during the extraction process.
    """
    try:
        df["Hashtag"] = df[RAW_TEXT_FIELD_NAME].apply(
            lambda x: " ".join(i for i in x.split() if i.startswith("#"))
        )  # extracting hashtags
        df["Hashtag"] = df["Hashtag"].str.lower()
        df["Hashtag_Unique"] = df["Hashtag"].apply(
            lambda x: " ".join(dict.fromkeys(x.split()))
        )  # Unique Hastags
        df["Hashtag_Unique"] = df["Hashtag_Unique"].str.replace(".", "")
        df["Hashtag_Unique"] = df["Hashtag_Unique"].str.replace(":", "")
        df["Hashtag_Unique"] = df["Hashtag_Unique"].str.replace(",", "")
        df["Hashtag_Unique"] = df["Hashtag_Unique"].str.replace("!", "")
        df["Hashtag_Unique"] = df["Hashtag_Unique"].str.replace("##", "#")
        df["Hashtag_Unique"] = df["Hashtag_Unique"].str.replace("?", "")
        df["Hashtag_Unique_Count"] = df["Hashtag"].str.split().str.len()
        df["Hashtag_Position"] = (df["BODY1"].str.find("#") + 1) / (
            df["BODY1"].str.len() + 1
        )

    except Exception as e:
        message = f"Error extracting hashtags from raw text: {e}"
        raise Exception(message)


def extract_emojis(df):
    """
    Extract and process emojis from a DataFrame.

    - This function extracts emojis from the raw text column of the DataFrame,
    creates columns for emojis, unique emojis, and computes the count of unique emojis.

    Parameters:
    - df (pd.DataFrame): The DataFrame containing raw text data with emojis.

    Raises:
    - Exception: If an error occurs during the extraction process.
    """
    try:
        emoji_raw = open("data_files/emoji_code.txt", "r", encoding="utf8").read()
        df["EMOJIS"] = df[RAW_TEXT_FIELD_NAME].apply(
            lambda x: " ".join(regexp_tokenize(x, emoji_raw))
        )  # instead of '' put ' ' to join with space
        df["EMOJIS_Unique"] = df["EMOJIS"].apply(
            lambda x: " ".join(dict.fromkeys(x.split()))
        )  # Unique Emojis
        df["EMOJIS_Unique_Count"] = df["EMOJIS_Unique"].str.split().str.len()

    except Exception as e:
        message = f"Error extracting emojis from raw text: {e}"
        raise Exception(message)


def lemmatization(df, stop_words):
    """
    Perform lemmatization on text data in a DataFrame.

    - This function applies POS tagging, removes stop words, and then lemmatizes the text data in the specified column.
    It retains emojis and hashtags during the lemmatization process.

    Parameters:
    - df (pd.DataFrame): The DataFrame containing text data to be lemmatized.
    - stop_words (list) : list of the stop words.

    Raises:
    - Exception: If an error occurs during the lemmatization process.
    """
    try:
        # POS tagging and removing stop words
        df["POS"] = df["BODY1"].apply(lambda word: get_wordnet_pos(word, stop_words))
        # Lemmatization ...does retain emojis and hash tags
        df["Lemitized"] = df["POS"].apply(lemmatize)
        df["Lemitized"] = df["Lemitized"].str.replace("# ", "#", regex=True)
        # Striping left leading space
        df["Lemitized"] = df["Lemitized"].str.lstrip()
        # Striping right ending space
        df["Lemitized"] = df["Lemitized"].str.rstrip()
        df["Lemitized"] = df["Lemitized"].str.replace("  ", " ", regex=True)

    except Exception as e:
        message = f"Error during lemmatization: {e}"
        raise Exception(message)


def extract_keywords(df, stop_words):
    """
    Extract keywords from lemmatized text data in a DataFrame.

    - This function extracts keywords from lemmatized text data in the specified column of the DataFrame.
    It removes hashtags, punctuation, stop words, and filters keywords based on minimum length criteria.

    Parameters:
    - df (pd.DataFrame): The DataFrame containing lemmatized text data.
    - stop_words (list) : list of the stop words.

    Raises:
    - Exception: If an error occurs during the keyword extraction process.
    """
    try:
        df["Keyword"] = df["Lemitized"].apply(
            lambda x: " ".join(i for i in x.split() if not i.startswith("#"))
        )  # Removes all hashtags
        # Removes all punctuation and emoji only KW
        df["Keyword"] = df["Keyword"].str.replace(r"[^\w\s]", " ", regex=True)
        df["Keyword"] = df["Keyword"].apply(
            lambda x: " ".join(
                k
                for k in x.split()
                if k not in stop_words and k not in punct and len(k) >= 3
            )
        )  # or k =='#'
        # Striping left leading space
        df["Keyword"] = df["Keyword"].str.lstrip()
        # Striping right ending space
        df["Keyword"] = df["Keyword"].str.rstrip()
        df["Keyword"] = df["Keyword"].str.replace("  ", " ", regex=True)

    except Exception as e:
        message = f"Error extracting keywords: {e}"
        raise Exception(message)


def extract_phrases(df):
    """
    Extract key phrases from lemmatized text data in a DataFrame.

    - This function extracts key phrases from lemmatized text data in the specified column of the DataFrame
    using the YAKE keyword extraction algorithm.

    Parameters:
    - df (pd.DataFrame): The DataFrame containing lemmatized text data.

    Raises:
    - Exception: If an error occurs during the key phrase extraction process.
    """
    try:
        kw_extractor = yake.KeywordExtractor(
            lan="en",
            n=3,
            dedupLim=0.3,
            dedupFunc="seqm",
            windowsSize=1,
            top=4,
            features=None,
        )
        df["Phrase"] = df["Lemitized"].apply(
            lambda x: [k[0] for k in kw_extractor.extract_keywords(x)]
        )

    except Exception as e:
        message = f"Error extracting phrases: {e}"
        raise Exception(message)
