import json
from bson import ObjectId
from datetime import datetime, timezone
from utils.logger import logger
import gcsfs
import sys
from utils.utilities import (
    DiagnosticActionType,
    DiagnosticStatus,
)
from utils.common_utils import (
    update_offset_and_publish,
    update_query_metadata,
    publish_pubsub_message,
)
from const import (
    RETRIEVER_BASE_LOOPBACK_THRESHOLD,
    RAC_VOLUME_METADATA_KEY,
    QUERY_ARCHIVER_RETRIEVER_TOPIC_ID,
    INDEX_STATUS_METADATA_KEY,
    PROJECT_ID,
    SEQUENCE_COORDINATOR_TOPIC_ID,
)


def list_files_in_bucket(full_bucket_path):
    """
    List all JSON files in the specified GCP bucket directory.
    """
    fs = gcsfs.GCSFileSystem()
    try:
        if not full_bucket_path.endswith("/"):
            full_bucket_path = "/".join(full_bucket_path.split("/")[:-1]) + "/"
        logger.debug(f"Listing files in: {full_bucket_path}")

        files = fs.ls(full_bucket_path)
        json_files = [file for file in files if file.endswith(".json")]
        logger.info(f"Found {len(json_files)} JSON files in the bucket.")
        return json_files
    except FileNotFoundError:
        logger.error(f"The path does not exist: {full_bucket_path}")
    except Exception as e:
        logger.error(f"Failed to list files in bucket: {str(e)}")
    return []


def fetch_data_from_gcp(full_bucket_path):
    """
    Fetch data from a single JSON file in GCP.
    """
    fs = gcsfs.GCSFileSystem()
    try:
        with fs.open(full_bucket_path, "r") as f:
            data = json.load(f)
        return data
    except Exception as e:
        logger.error(f"Failed to fetch data from GCP file {full_bucket_path}: {str(e)}")
        return []


def validate_and_transform(record):
    """
    Validate and transform the record to match the MongoDB schema.
    Handles data type changes or fixes inconsistent data.
    """
    transformed = {}

    try:
        for key, value in record.items():
            # Handling ObjectId
            if key == "_id" and not isinstance(value, ObjectId):
                transformed["_id"] = (
                    ObjectId(value) if isinstance(value, str) else value
                )

            # Handling dates (timestamp in milliseconds)
            elif key == "date" and isinstance(value, int):
                # Convert milliseconds timestamp to datetime
                transformed[key] = datetime.fromtimestamp(value / 1000, timezone.utc)

            # Default case: Keep the value as is
            else:
                transformed[key] = value

    except Exception as e:
        logger.warning(f"Failed to transform record: {record}. Error: {e}")
        transformed = record  # Fallback to original record

    return transformed


def insert_data_to_mongo(collection, data):
    """
    Insert data into MongoDB, ensuring each record has a unique `_id` and correct data types.
    """
    try:
        # Transform records
        transformed_data = [validate_and_transform(record) for record in data]

        # Log count instead of full data
        logger.info(f"Number of records transformed: {len(transformed_data)}")

        # Insert records
        collection.insert_many(transformed_data)
        logger.info(f"Inserted {len(transformed_data)} records into MongoDB.")

    except Exception as e:
        logger.error(f"Failed to insert data into MongoDB: {str(e)}")
        raise e


def process_bucket_data(
    query_meta_data,
    collection_name,
    query_archive_bucket_name,
    query_collection,
    payload,
    collection,
    meta_container,
    offset,
    offset_metadata_key,
):
    """
    Processes JSON files from a GCP bucket and inserts their data into a MongoDB collection.

    Generate GCP bucket path, retrieve JSON files, loop through until the limit is hit, and report process status.

    Parameters:
    - query_meta_data (dict): Metadata for the query, including total volume and other configurations.
    - organization_id (str): The ID of the organization for which data is being processed.
    - user_id (str): The ID of the user associated with the query.
    - data_source_id (str): The ID of the data source linked to the query.
    - query_id (str): The unique identifier for the query.
    - collection_name (str): The name of the MongoDB collection to which data will be inserted.
    - query_archive_bucket_name (str): The name of the GCP bucket containing the archived JSON files.
    - query_collection (MongoDB Collection): The MongoDB collection holding query metadata.
    - payload (dict): The payload received for processing.
    - collection (MongoDB Collection): The MongoDB collection where the data is inserted.
    - meta_container (MetaContainer): An object for managing diagnostic updates and payload metadata.
    - offset (int): The starting offset for fetching JSON files from the bucket.
    - offset_metadata_key (str): The metadata key used to store and update the offset in the database.

    Usage:
    - This function is typically invoked as part of the query retrieval or archiving process,
    - where data is moved from GCP buckets to MongoDB for further use.

    Exception Handling:
    - Logs and sends diagnostics via `meta_container` in case of any errors.

    """
    try:
        organization_id = payload["organization_id"]
        user_id = payload["user_id"]
        data_source_id = payload["data_source_id"]
        query_id = payload["query_id"]

        message = (
            f"Capabilities query retriever script started successfully for query_id: {query_id}"
        )
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.PENDING.value,
            message,
        )

        logger.info(f"Starting loop with offset: {offset}")

        bucket_path = f"{organization_id}/{user_id}/{data_source_id}/{query_id}/{collection_name}/{collection_name}_{offset}.json"
        full_bucket_path = f"gs://{query_archive_bucket_name}/{bucket_path}"

        json_files = list_files_in_bucket(full_bucket_path)

        if not json_files:
            logger.info("No JSON files to process in the bucket.")
            return

        # Retrieve the RAC collection volume
        total_documents = query_meta_data.get(RAC_VOLUME_METADATA_KEY)
        loopback_threshold = offset + int(RETRIEVER_BASE_LOOPBACK_THRESHOLD)
        while offset < loopback_threshold:
            logger.debug(f"Before fetch, offset is: {offset}")

            bucket_path = f"{organization_id}/{user_id}/{data_source_id}/{query_id}/{collection_name}/{collection_name}_{offset}.json"
            full_bucket_path = f"gs://{query_archive_bucket_name}/{bucket_path}"

            data = fetch_data_from_gcp(full_bucket_path)

            chunk_size = sys.getsizeof(data) / (1024 * 1024)
            logger.info(f"Calculated chunk size: {chunk_size} MB")

            logger.info(f"Generated Bucket Path: {full_bucket_path}")
            logger.debug(f"After fetch, offset remains: {offset}")

            batch_count = int(len(data))

            if batch_count == 0:
                logger.warning("No more records to process. Exiting loop.")
                break

            insert_data_to_mongo(collection, data)

            offset = update_offset_and_publish(
                offset,
                offset_metadata_key,
                total_documents,
                loopback_threshold,
                payload,
                batch_count,
                query_collection,
                query_id,
                QUERY_ARCHIVER_RETRIEVER_TOPIC_ID,
            )
            del data
            if offset == total_documents:
                update_query_metadata(
                query_collection,
                query_id,
                meta_data_key= INDEX_STATUS_METADATA_KEY,
                value={},
                )
                logger.info(f"Successfully reset {INDEX_STATUS_METADATA_KEY} to an empty object.")
                publish_pubsub_message(PROJECT_ID, SEQUENCE_COORDINATOR_TOPIC_ID, payload)
                message = f"Capabilities query retriever script completed successfully for query_id: {query_id}"
                meta_container.send_diagnostic(
                    DiagnosticActionType.UPDATE.value,
                    DiagnosticStatus.COMPLETED.value,
                    message,
                )
                break

        logger.info("All data from the bucket has been processed.")
    except Exception as e:
        message = f"An error occurred during retrieving the data from GCP : {e}"
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.FAILED.value,
            message,
            e,
        )
