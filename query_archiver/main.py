import sys
import json
import base64
from cloudevents.http import CloudEvent
import functions_framework
from utils.logger import logger
from utils.mongo_db import (
    get_mongodb_client,
    get_mongodb_db,
    get_mongodb_collection,
)
from utils.common_utils import (
    get_query_config,
)
from utils.utilities import (
    CONSUMER_TYPE,
    OrganizationAccountInfo,
    MetaContainer,
    QueryOperationType,
    CollectionType,
    DiagnosticActionType,
    DiagnosticStatus,
)
from retriever import process_bucket_data
from archiver import insert_to_gcp

from const import (
    QUERY_COLLECTION_NAME,
    QUERY_PROJECTION_ATTRIBUTES,
    RAC_COLLECTION_NAME_METADATA_KEY,
    RAC_VOLUME_METADATA_KEY,
    QUERY_OPERATION_TYPE_METADATA_KEY,
    RAC_TRANSFORM_COLLECTION_NAME_METADATA_KEY,
    RAC_TRANSFORM_VOLUME_METADATA_KEY,
    QUERY_ARCHIVER_RAC_OFFSET_METADATA_KEY,
    QUERY_ARCHIVER_RAC_TRANSFORM_OFFSET_METADATA_KEY,
    QUERY_RETRIEVER_RAC_TRANSFORM_OFFSET_METADATA_KEY,
    QUERY_RETRIEVER_RAC_OFFSET_METADATA_KEY,
    RAC_CHUNK_SIZE,
    RAC_TRASFORM_CHUNK_SIZE,
    QUERY_ARCHIVER_RAC_STATUS_METADATA_KEY,
    QUERY_ARCHIVER_RAC_TRANSFORM_STATUS_METADATA_KEY,
)


@functions_framework.cloud_event
def main(cloud_event: CloudEvent):
    """
    Cloud Function to handle query archiver and retriever operations.

    - This function is triggered by a Cloud Pub/Sub message, processes the incoming payload, and performs operations
    - like archiving data to Google Cloud Storage (GCS) or retrieving data from GCS based on the query operation type.

    Steps:
    - Decode the CloudEvent payload to extract the relevant data.
    - Fetch organization account information such as MongoDB URL, database name, and GCS bucket details.
    - Retrieve the query configuration from the MongoDB query collection using the provided `query_id`.
    - Extract metadata from the query configuration, including operation type, collection name, and chunk size.
    - Perform the following operations based on the query operation type:
    - ARCHIVE:
        - Fetch data in chunks from the specified MongoDB collection.
        - Archive the data to GCS in JSON format with a structured bucket path.
        - Update the offset metadata and mark the query as deleted when completed.
        - Delete the corresponding MongoDB collection.
    - RETRIEVE:
        - Retrieve the archived data from GCS using the structured bucket path.
        - Process the retrieved data for further use or analysis.
    - Log details at various stages, including errors, diagnostics, and progress updates.
    - Send diagnostic messages to update the status (`COMPLETED` or `FAILED`) of the query operation.

    Parameters:
    - cloud_event (CloudEvent): The CloudEvent object containing the Pub/Sub message payload.

    Returns:
    - None: This function does not return a value but logs status updates and sends diagnostics.

    Exception Handling:
    - Exception: If any error occurs during the processing of the query operation.
    """

    try:
        meta_container = MetaContainer()
        payload = json.loads(
            base64.b64decode(cloud_event.data["message"]["data"]).decode("utf-8")
        )
        meta_container = MetaContainer()

        logger.info("Message received successfully: %s", payload)
        payload["publisher_type"] = CONSUMER_TYPE
        meta_container.set_payload_info(payload)

        organization_id = payload["organization_id"]
        query_id = payload["query_id"]
        collection_type = payload.get("collection_type")

        # Fetch organization account information
        org_account_info = OrganizationAccountInfo(organization_id)
        query_archive_bucket_name = org_account_info.query_archive_bucket_name

        mongodb_url = org_account_info.mongodb_url
        organization_db_name = org_account_info.organization_db_name

        mongo_db_client = get_mongodb_client(mongodb_url)
        organization_db = get_mongodb_db(mongo_db_client, organization_db_name)

        query_collection = get_mongodb_collection(
            organization_db,
            QUERY_COLLECTION_NAME,
        )
        query_config = get_query_config(
            query_collection, query_id, QUERY_PROJECTION_ATTRIBUTES
        )

        if not query_config:
            logger.warning("No configuration found for query_id: '%s'", query_id)
            return

        query_meta_data = query_config["meta_data"]
        query_meta_data = (
            query_meta_data[0] if isinstance(query_meta_data, list) else query_meta_data
        )

        query_operation_type = query_meta_data.get(QUERY_OPERATION_TYPE_METADATA_KEY)
        collection_name = query_meta_data.get(RAC_COLLECTION_NAME_METADATA_KEY)

        chunk_size = int(RAC_CHUNK_SIZE)
        if collection_type == CollectionType.RAC_TRANSFORM.value:
            collection_name = query_meta_data.get(
                RAC_TRANSFORM_COLLECTION_NAME_METADATA_KEY
            )
            chunk_size = int(RAC_TRASFORM_CHUNK_SIZE)

        if collection_type == CollectionType.RAC_TRANSFORM.value:
            collection_name = query_meta_data.get(
                RAC_TRANSFORM_COLLECTION_NAME_METADATA_KEY
            )

        collection = get_mongodb_collection(organization_db, collection_name)
        total_count = query_meta_data.get(RAC_VOLUME_METADATA_KEY)
        if collection_type == CollectionType.RAC_TRANSFORM.value:
            total_count = query_meta_data.get(RAC_TRANSFORM_VOLUME_METADATA_KEY)

        logger.info(f"Total documents in {collection_name}: {total_count}")

        match query_operation_type:
            case QueryOperationType.ARCHIVE.value:
                if collection_type == CollectionType.RAC_TRANSFORM.value:
                    offset = query_meta_data.get(
                        QUERY_ARCHIVER_RAC_TRANSFORM_OFFSET_METADATA_KEY, 0
                    )
                    offset_metadata_key = (
                        QUERY_ARCHIVER_RAC_TRANSFORM_OFFSET_METADATA_KEY
                    )
                    query_archiver_status_key = QUERY_ARCHIVER_RAC_TRANSFORM_STATUS_METADATA_KEY
                else:
                    offset = query_meta_data.get(
                        QUERY_ARCHIVER_RAC_OFFSET_METADATA_KEY, 0
                    )
                    offset_metadata_key = QUERY_ARCHIVER_RAC_OFFSET_METADATA_KEY
                    query_archiver_status_key = QUERY_ARCHIVER_RAC_STATUS_METADATA_KEY
                insert_to_gcp(
                    query_meta_data,
                    collection,
                    collection_name,
                    query_archive_bucket_name,
                    query_collection,
                    payload,
                    meta_container,
                    offset,
                    offset_metadata_key,
                    chunk_size,
                    query_archiver_status_key
                )
            case QueryOperationType.RETRIEVE.value:
                if collection_type == CollectionType.RAC_TRANSFORM.value:
                    offset = query_meta_data.get(
                        QUERY_RETRIEVER_RAC_TRANSFORM_OFFSET_METADATA_KEY, 0
                    )
                    offset_metadata_key = (
                        QUERY_RETRIEVER_RAC_TRANSFORM_OFFSET_METADATA_KEY
                    )
                else:
                    offset = query_meta_data.get(
                        QUERY_RETRIEVER_RAC_OFFSET_METADATA_KEY, 0
                    )
                    offset_metadata_key = QUERY_RETRIEVER_RAC_OFFSET_METADATA_KEY
                logger.info(
                    f"Retrieving data from bucket for organization ID: {organization_id}, query ID: {query_id}"
                )
                # Process bucket data
                process_bucket_data(
                    query_meta_data,
                    collection_name,
                    query_archive_bucket_name,
                    query_collection,
                    payload,
                    collection,
                    meta_container,
                    offset,
                    offset_metadata_key,
                )
            case _:
                logger.warning("Unknown Query Operation Type type!")

    except Exception as e:
        message = f"An error occurred while processing query archiver or retriver: {e}"
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.FAILED.value,
            message,
            e,
        )
