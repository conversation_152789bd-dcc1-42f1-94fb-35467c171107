import sys
import gc
from bson import ObjectId
from datetime import datetime, timezone
from utils.logger import logger
from utils.utilities import FileExtension
from utils.common_utils import (
    fetch_collection_data,
    update_offset_and_publish,
    update_query_metadata,
)
from utils.file_util import FileUtils
from utils.utilities import (
    DiagnosticActionType,
    DiagnosticStatus,
    QueryArchiverRetrieverStatus,
    QueryStatus
)
from const import (
    ARCHIVER_BASE_LOOPBACK_THRESHOLD,
    RAC_VOLUME_METADATA_KEY,
    QUERY_ARCHIVER_RETRIEVER_TOPIC_ID,
    QUERY_STATUS_METADATA_KEY,
)


def custom_serializer(obj):
    """
    Custom serializer function for non-serializable objects.
    """
    if isinstance(obj, ObjectId):
        return str(obj)
    raise TypeError(f"Type {type(obj)} not serializable")


def insert_to_gcp(
    query_meta_data,
    collection,
    collection_name,
    query_archive_bucket_name,
    query_collection,
    payload,
    meta_container,
    offset,
    offset_metadata_key,
    chunk_size,
    query_archiver_status_key,
):
    """
    Inserts data from a MongoDB collection into a GCP bucket in chunks.

    Retrieve metadata, fetch data in chunks, upload each chunk to GCP, and then update the offset and metadata accordingly. 

    Parameters:
    - query_meta_data (dict): Metadata for the query, including total document count.
    - collection (MongoDB Collection): The MongoDB collection to fetch data from.
    - collection_name (str): The name of the MongoDB collection.
    - query_archive_bucket_name (str): The name of the GCP bucket for storing archived data.
    - query_collection (MongoDB Collection): The collection for managing query metadata.
    - payload (dict): Payload containing additional details for processing.
    - meta_container (MetaContainer): Object for sending diagnostic messages and managing metadata.
    - offset (int): The starting offset for fetching data from the collection.
    - offset_metadata_key (str): The metadata key for tracking offsets.
    - chunk_size (int): The number of documents to fetch in each batch.

    Returns:
    - None: The function performs actions and logs results but does not return a value.

    Exception Handling:
    - Logs and raises an error if data upload or MongoDB operations fail.
    - Sends a diagnostic message with failure status if an exception occurs.
    """

    try:
        organization_id = payload["organization_id"]
        user_id = payload["user_id"]
        data_source_id = payload["data_source_id"]
        query_id = payload["query_id"]
        
        message = (
            f"Capabilities query archiver script started successfully for query_id: {query_id}"
        )
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.PENDING.value,
            message,
        )
        logger.info(f"Starting loop with offset: {offset}")

        # Retrieve the RAC collection volume
        total_documents = query_meta_data.get(RAC_VOLUME_METADATA_KEY)
        loopback_threshold = offset + int(ARCHIVER_BASE_LOOPBACK_THRESHOLD)
        while offset < loopback_threshold:
            logger.debug(f"Before fetch, offset is: {offset}")
            batch_df = fetch_collection_data(
                collection, {}, {}, offset, int(chunk_size)
            )
            batch_size = sys.getsizeof(batch_df) / (1024 * 1024)
            logger.info(f"Calculated chunk size: {batch_size} MB")

            bucket_path = f"{organization_id}/{user_id}/{data_source_id}/{query_id}/{collection_name}/{collection_name}_{offset}.json"
            full_bucket_path = f"gs://{query_archive_bucket_name}/{bucket_path}"

            logger.info(f"Bucket Path: {full_bucket_path}")
            logger.debug(f"After fetch, offset remains: {offset}")

            batch_count = int(len(batch_df))

            if batch_count == 0:
                logger.warning("No more records to process. Exiting loop.")
                break

            FileUtils.insert_data_to_gcsfs(
                full_bucket_path, batch_df, FileExtension.JSON.value, custom_serializer
            )

            del batch_df
            gc.collect()

            offset = update_offset_and_publish(
                offset,
                offset_metadata_key,
                total_documents,
                loopback_threshold,
                payload,
                batch_count,
                query_collection,
                query_id,
                QUERY_ARCHIVER_RETRIEVER_TOPIC_ID,
            )

            if offset == total_documents:
                update_query_metadata(
                    query_collection,
                    query_id,
                    meta_data_key = query_archiver_status_key,
                    value = QueryArchiverRetrieverStatus.COMPLETED.value,
                )
                # Delete the MongoDB Collection
                collection.drop()
                logger.info(f"Collection {collection.name} deleted successfully.")
                message = f"Capabilities query archiver script completed successfully for query_id: {query_id}"
                meta_container.send_diagnostic(
                    DiagnosticActionType.UPDATE.value,
                    DiagnosticStatus.COMPLETED.value,
                    message,
                )
                #Update query status as archived
                update_query_metadata(
                    query_collection,
                    query_id,
                    meta_data_key=QUERY_STATUS_METADATA_KEY,
                    value=QueryStatus.ARCHIVE.value,
                )
                break
    except Exception as e:
        # Archival failed: Update query status to ready
        update_query_metadata(
            query_collection,
            query_id,
            meta_data_key=QUERY_STATUS_METADATA_KEY,
            value=QueryStatus.READY.value,
        )
        message = f"An error occurred during archiving the data to GCP : {e}"
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.FAILED.value,
            message,
            e,
        )
