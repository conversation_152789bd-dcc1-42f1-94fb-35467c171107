"""
This module processes and generates summaries for different contexts (Cluster, Story, Theme) 
in a MongoDB-based pipeline. It defines a base `Context` class with common logic for 
summarizing and updating MongoDB records, and subclasses (`Cluster`, `Story`, `Theme`) that 
handle specific contexts.
"""

import dask
from pymongo import UpdateMany
from pymongo.collection import Collection
from helper import clean_summary_text
from summary import generate_summary
from summarization.const import MAX_CLUSTER_IDS
from utils.utilities import SummaryContext, SummaryProcessingField
from utils.llm_settings import SUMMARIES_TO_IGNORE


class Context:
    """Class to define context object"""

    def __init__(self, payload, meta_container, lanchain_util):
        """This is constructor for the intialize of the context object

        Args:
            payload (dict): message recieved by the pubsub
            meta_container (_type_): _description_
            lanchain_util (_type_): _description_
        """
        self.payload = payload
        self.pipeline = []
        self.encapsulation_marker = payload.get("encapsulation_marker")
        self.meta_container = meta_container
        self.langchain_util = lanchain_util
        self.context = meta_container.meta.get("CONTEXT")
        self.summary_column = self.context + "_summary"
        self._get_context_attr()

    def _get_context_attr(self):
        pass

    def process_context(self, texts, context_id):
        """
        Generic function to process cluster,story and theme summaries.

        Parameters:
        - texts : Aggeragate texts array of the context id
        - filter_doc : filter doc for the updating the records based on context
        - context : context value of the cluster,story or theme
        - context_id : context id is the cluster,story or theme id we want to process

        Returns:
        - UpdateMany object with processed combined summary
        """
        context_id_column = getattr(self, "context_id_column")

        filter_doc = {
            "encapsulation_marker": self.encapsulation_marker,
            context_id_column: context_id,
        }

        combined_summary = clean_summary_text(
            generate_summary(self.langchain_util, texts)
        )
        return UpdateMany(filter_doc, {"$set": {self.summary_column: combined_summary}})

    def process_data(self, records):
        """This function process the data using dask delayed

        Args:
        - records : records containing context ids and texts

        Returns:
        - UpdateMany object with processed combined summary
        """
        tasks = [
            dask.delayed(self.process_context)(
                record["texts"],
                record["_id"],
            )
            for record in records
        ]
        updated_fields = dask.compute(*tasks)
        return updated_fields

    def get_next_context(self, collection: Collection):
        """Get the next context after processing of the data

        Args:
            collection (Collection): RAC collection of the query

        Returns:
            context : return the context based on the checks
        """
        processing_field = getattr(self, "processing_field")
        count_query = {
            "encapsulation_marker": self.encapsulation_marker,
            processing_field: False,
        }
        num_records = collection.count_documents(count_query)
        next_context = getattr(self, "next_context")
        if num_records == 0:
            return next_context

        return self.context


class Cluster(Context):
    """Class to define cluster context"""

    def _get_context_attr(self):
        """To get the cluster attributes"""
        self.processing_field = SummaryProcessingField.CLUSTER.value
        self.context_id_column = "Unique_cluster_id"
        self.next_context = SummaryContext.STORY.value

    def get_context_data(self):
        """To get context data for cluster using pipeline"""
        pipeline = [
            {
                "$match": {
                    "encapsulation_marker": self.encapsulation_marker,
                    SummaryProcessingField.CLUSTER.value: False,
                }
            },
            {
                "$group": {
                    "_id": "$Unique_Cluster_ID",
                    "texts": {"$addToSet": "$BODY1"},
                }
            },
            {"limit": MAX_CLUSTER_IDS},
        ]
        return pipeline


class Story(Context):
    """Class to define cluster context"""

    def _get_context_attr(self):
        """To get story attributes"""
        self.processing_field = SummaryProcessingField.STORY.value
        self.context_id_column = "Unique_Story_ID"
        self.next_context = SummaryContext.THEME.value

    def get_context_data(self):
        """To get context data for Story using pipeline"""

        pipeline = [
            {
                "$match": {
                    "encapsulation_marker": self.encapsulation_marker,
                    SummaryProcessingField.STORY.value: False,
                    "cluster_summary": {"$nin": SUMMARIES_TO_IGNORE},
                }
            },
            {
                "$group": {
                    "_id": "$Unique_Story_ID",
                    "texts": {"$addToSet": "$cluster_summary"},
                }
            },
        ]
        return pipeline


class Theme(Context):
    """Class to define Theme context"""

    def _get_context_attr(self):
        """To get theme attributes"""

        self.processing_field = SummaryProcessingField.THEME.value
        self.context_id_column = "Themes"
        self.next_context = SummaryContext.ALL_PROCESSED.value

    def get_context_data(self):
        """To get context data for Theme using pipeline"""

        pipeline = [
            {
                "$match": {
                    "encapsulation_marker": self.encapsulation_marker,
                    SummaryProcessingField.THEME.value: False,
                    "story_summary": {"$nin": SUMMARIES_TO_IGNORE},
                }
            },
            {
                "$group": {
                    "_id": "$Themes",
                    "texts": {"$addToSet": "$story_summary"},
                }
            },
        ]
        return pipeline
