"""
This module defines the main execution flow for processing summaries based on 
context (<PERSON><PERSON>, Story, Theme) in a MongoDB pipeline. It handles parsing 
the payload, interacting with the database, managing diagnostic 
status, and coordinating the processing tasks using Das<PERSON>.
"""

import json
import time
import base64
from bson import ObjectId
import functions_framework
from context import Cluster, Story, Theme
from summary import get_map_reduce_chain
from helper import add_unique_ids
from summarization.const import (
    ENCAPSULATION_MARKER_COLLECTION,
    PROJECT_ID,
    PROJECTION,
    QUERY_COLLECTION,
    SEQUENCE_COORDINATOR_TOPIC_ID,
    SUMMARIZATION_TOPIC_ID,
    ORG_CREDENTIALS_COLLECTION,
)
from utils.common_utils import get_query_config, update_query_metadata
from utils.langchain_utils import LangChainUtility
from utils.logger import logger
from utils.mongo_db import get_mongodb_client, get_mongodb_collection, get_mongodb_db
from utils.pubsub_publisher import publish_pubsub_message
from utils.utilities import (
    CONSUMER_TYPE,
    EncapsulationMarkerStatus,
    MetaContainer,
    DiagnosticActionType,
    DiagnosticStatus,
    OrganizationAccountInfo,
    SummaryContext,
)


# Triggered from a message on a Cloud Pub/Sub topic.
@functions_framework.cloud_event
def main(cloud_event):
    """
    This main function where execution start
    - Steps
      - Parse the payload and extract the query_id , organization_id , encapsulation_marker ,context
      - send the message to the diagnostic for pending status
      - Setup the database and collection object with Organization id and vault
      - check for the context and set context_ids
      - check if context exists
        - get the context data for an encapulation marker
        - create dask delay task with context data
      - Process the task with auto scheduler
      - else if the context is not set then add the Unique ids
      - check if the context was theme and if all the process are completed
      - if all the process id completed then send the message to capabilities sequence coordinator
            and toggle the diagnostic to completed
      - else set the context and loopback

    Parmeters:
    - cloud_event (CloudEvent): The CloudEvent object representing the incoming event.

    Returns:
    - Dict with status and message
        - status: 200 if success or 400 if any error occurs
        - message: message of success or error

    Exception:
     - If any error occurs during the summarization process it will log and send it diagnostic.

    """
    mongodb_client = None
    try:
        meta_container = MetaContainer()
        lanchain_util = LangChainUtility()

        # parse the payload and extract query_id and organization_id from payload
        payload = json.loads(
            base64.b64decode(cloud_event.data["message"]["data"]).decode("utf-8")
        )
        payload["publisher_type"] = CONSUMER_TYPE

        logger.info("Message received successfully: %s", payload)

        meta_container.set_payload_info(payload)

        query_id, organization_id, encapsulation_marker, context = (
            payload.get("query_id"),
            payload.get("organization_id"),
            payload.get("encapsulation_marker"),
            payload.get("context"),
        )

        message = f"Capabilities Summarization script started successfully for query_id: {query_id}"
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.PENDING.value,
            message,
        )

        start_time = time.perf_counter()

        # Fetch organization account information
        org_info = OrganizationAccountInfo(organization_id)
        mongodb_connection_string = org_info.mongodb_url
        db_name = org_info.organization_db_name

        # Establish MongoDB connection
        mongodb_client = get_mongodb_client(mongodb_connection_string)
        db = get_mongodb_db(mongodb_client, db_name)

        query_collection = get_mongodb_collection(db, QUERY_COLLECTION)

        query_config = get_query_config(query_collection, query_id, PROJECTION)

        if query_config is None:
            message = f"Query not found : {payload}"
            logger.warning(message)
            return {"status": 404, "message": message}, 404

        meta_container.set_meta_data(query_config["meta_data"])

        rac_collection_name = meta_container.meta_data.get("RAC_COLLECTION_NAME")
        context = meta_container.meta_data.get("CONTEXT")

        rac_collection = get_mongodb_collection(db, rac_collection_name)
        llm_config_collection = get_mongodb_collection(db, ORG_CREDENTIALS_COLLECTION)

        encapsulation_marker_collection = get_mongodb_collection(
            db, ENCAPSULATION_MARKER_COLLECTION
        )
        model_name = meta_container.meta_data.get("MODEL_NAME")
        lanchain_util.set_configuration(model_name, llm_config_collection)

        # set the map reduce chain
        get_map_reduce_chain(lanchain_util)

        context_obj = None
        match context:
            case SummaryContext.CLUSTER.value:
                context_obj = Cluster(payload, meta_container, lanchain_util)
            case SummaryContext.STORY.value:
                context_obj = Story(payload, meta_container, lanchain_util)
            case SummaryContext.THEME.value:
                context_obj = Theme(payload, meta_container, lanchain_util)

        if context:
            pipeline = context_obj.get_context_data()
            records = rac_collection.aggregate(pipeline)
            updated_fields = context_obj.process_data(records)
            rac_collection.bulk_write(updated_fields, ordered=False)

            if updated_fields:
                rac_collection.bulk_write(list(updated_fields))

            rac_collection.count_documents(context_obj)
            meta_container.meta_data["context"] = context_obj.get_next_context(
                rac_collection
            )

        else:
            encapsulation_marker_collection.update_one(
                {
                    "query_id": ObjectId(query_id),
                    "encapsulation_marker": encapsulation_marker,
                },
                {
                    "$set": {
                        "summarization_status": EncapsulationMarkerStatus.PENDING.value
                    }
                },
            )
            add_unique_ids(rac_collection, encapsulation_marker)
            meta_container.meta_data["context"] = SummaryContext.CLUSTER.value

            update_query_metadata(
                query_collection,
                query_id,
                "context",
                meta_container.meta_data["context"],
            )
        is_all_processed = (
            meta_container.meta_data["context"] == SummaryContext.ALL_PROCESSED.value
        )

        if is_all_processed:
            publish_pubsub_message(PROJECT_ID, SEQUENCE_COORDINATOR_TOPIC_ID, payload)
            encapsulation_marker_collection.update_one(
                {
                    "query_id": ObjectId(query_id),
                    "encapsulation_marker": encapsulation_marker,
                },
                {
                    "$set": {
                        "summarization_status": EncapsulationMarkerStatus.COMPLETED.value
                    }
                },
            )

            is_all_encapsulation_markers_completed = (
                encapsulation_marker_collection.count_documents(
                    {
                        "query_id": ObjectId(query_id),
                        "summarization_status": {
                            "$ne": EncapsulationMarkerStatus.COMPLETED.value
                        },
                    }
                )
                == 0
            )

            if is_all_encapsulation_markers_completed:
                meta_container.send_diagnostic(
                    DiagnosticActionType.UPDATE.value,
                    DiagnosticStatus.COMPLETED.value,
                    message,
                )
        else:
            logger.info("Publishing message to summarization for loop back")
            publish_pubsub_message(PROJECT_ID, SUMMARIZATION_TOPIC_ID, payload)

        end_time = time.perf_counter()
        total_time = end_time - start_time

        print(
            f"Time taken of the execution for encapsulation marker {encapsulation_marker} with context {context} : {total_time}"
        )
        return {"status": 200}

    except Exception as e:
        message = f"Error while process Capabilities Summarization: {e}"
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.FAILED.value,
            message,
            str(type(e)),
        )
        return {"status": 400, "error_message": message}

    finally:
        if mongodb_client is not None:
            mongodb_client.close()
