"""
This script adds 2 new columns "Unique_Story_ID" and "Unique_Cluster_ID" 
following the heirarchy of Themes-Stories-Clusters
"""

import re
from pymongo import UpdateMany
from pymongo.collection import Collection
from utils.logger import logger
from utils.utilities import SummaryProcessingField

# Pre-compile regular expressions
title_regex = re.compile(
    r"^\s*(#{0,2}\s*)?.*?:\s*\n?", re.MULTILINE
)  # Remove markdown-style titles and plain titles ending with a colon at the beginning of a line
notes_regex = re.compile(
    r"\n\s*\*{1,2}.*$", re.MULTILINE | re.DOTALL
)  # Remove notes at the end of the text starting with one or two asterisks
cleanup_regex = re.compile(
    r"#"
)  # Strip spaces and remove leading '#' characters from the remaining text


def add_unique_ids(rac_collection: Collection, encapsulation_marker: str) -> None:
    """
    Add unique ids function create index on the

    Parameters:
    - rac_collection : RAC Collection Object of the MongoDB
    - encapsulation_marker : Encapsulation marker of the collection

    Exception handling:
    - Log and raise the execption occured while adding unique ids
    """
    try:
        bulk_ops = []
        unique_story_id = 0  # Initialize a global Unique_Story_ID counter
        unique_cluster_id = 0  # Initialize a global Unique_Story_ID counter

        pipeline = [
            {"$match": {"encapsulation_marker": encapsulation_marker}},
            {
                "$group": {
                    "_id": {"Themes": "$Themes", "Stories": "$Stories"},
                    "cluster_ids": {"$addToSet": "$cluster_id"},
                }
            },
        ]
        records = rac_collection.aggregate(pipeline)

        for record in records:
            filter_doc = {"encapsulation_marker": encapsulation_marker}
            filter_doc.update(record["_id"])
            bulk_ops.append(
                UpdateMany(
                    filter_doc,
                    {
                        "$set": {
                            "Unique_Story_ID": unique_story_id,
                            SummaryProcessingField.CLUSTER.value: False,
                            SummaryProcessingField.STORY.value: False,
                            SummaryProcessingField.THEME.value: False,
                        }
                    },
                )
            )

            for cluster_id in record["cluster_ids"]:
                filter_doc = {"encapsulation_marker": encapsulation_marker}
                filter_doc.update(record["_id"])
                filter_doc["cluster_id"] = cluster_id
                bulk_ops.append(
                    UpdateMany(
                        filter_doc,
                        {"$set": {"Unique_Cluster_ID": unique_cluster_id}},
                    )
                )
                unique_cluster_id += 1  # Increment the Unique_Cluster_ID counter after processing each cluster

            unique_story_id += (
                1  # Increment the Unique_Story_ID counter after processing each story
            )

        if bulk_ops:
            logger.info("Updating the Unique ids")

            result = rac_collection.bulk_write(bulk_ops)  # Execute bulk update

            logger.info(
                "Bulk operation successful, documents modified: %s",
                result.modified_count,
            )

            rac_collection.create_index(
                [
                    ("encapsulation_marker", 1),
                    (SummaryProcessingField.STORY.value, 1),
                    ("Unique_Story_ID", 1),
                ],
                name=f"encapsulation_marker_{SummaryProcessingField.STORY.value}_Unique_Story_ID_index",
            )

            rac_collection.create_index(
                [
                    ("encapsulation_marker", 1),
                    (SummaryProcessingField.CLUSTER.value, 1),
                    ("Unique_Cluster_ID", 1),
                ],
                name=f"encapsulation_marker_{SummaryProcessingField.CLUSTER.value}_Unique_Cluster_ID_index",
            )

    except Exception as e:
        logger.error("Error occurred while adding unique ids. %s", e)
        raise


def clean_summary_text(text: str) -> str:
    """
    Cleans summary text by removing markdown-style titles and notes, and stripping excessive whitespace and hash characters.

    Parameters:
    - text : raw summary text from the model

    Returns:
    - Processed text : Cleaned text with regex
    """
    cleaned_text = title_regex.sub("", text)
    cleaned_text = notes_regex.sub("", cleaned_text)
    processed_text = cleanup_regex.sub("", cleaned_text.strip())

    return processed_text
