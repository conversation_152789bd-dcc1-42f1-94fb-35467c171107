apiVersion: v1
kind: Pod
metadata:
  name: vm-consumers
  namespace: discover
  labels:
    app: vm-consumers
spec:
  containers:
    - name: vm-consumers
      image: gcr.io/tellagence-platform/vm-consumers:latest
      ports:
        - containerPort: 8080  # Expose port for HTTP requests
      env:
        - name: ENVIRONMENT
          value: "development"  # Example environment variable
      resources:
        requests:
          memory: "4Gi"
          cpu: "4"
        limits:
          memory: "8Gi"
          cpu: "8"
  restartPolicy: Never