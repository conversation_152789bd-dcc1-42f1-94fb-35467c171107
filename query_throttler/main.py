"""
This module creates a query throttler to process queries accourding
to the resources in a controlled way.
"""

import time
import json
import base64
from bson import ObjectId
from cloudevents.http import CloudEvent
import functions_framework
import yaml
from kubernetes import client
from google.auth import default
from google.auth.transport.requests import Request
from google.cloud import container_v1
from utils.logger import logger
from utils.pubsub_publisher import publish_pubsub_message
from utils.mongo_db import (
    get_mongodb_client,
    get_mongodb_db,
    get_mongodb_collection,
)
from utils.utilities import OrganizationAccountInfo, MetaContainer, QueryStatus
from const import (
    CAPABILITIES_DB_NAME,
    CAPABILITIES_QUERY_THROTTLER_COLLECTION_NAME,
    CAPABILITIES_MONGO_URI,
    CONSUMER_TYPE,
    PROJECT_ID,
    QUERY_THROTTLER_TOPIC_ID,
    SEQUENCE_COORDINATOR_TOPIC_ID,
    NAMESPACE,
    DEFAULT_POD_NAME,
    MAX_RUNNING_QUERIES,
    RETRY_DELAY,
    QUERY_COLLECTION_NAME,
    POD_DEFINITION_YAML,
    REGION,
    CLUSTER_NAME,
)

gke_client = container_v1.ClusterManagerClient()


def get_gke_cluster_credentials(project_id, region, cluster_name):
    """
    Get GKE cluster credentials.
    """
    # Get the cluster details
    cluster = gke_client.get_cluster(
        name=f"projects/{project_id}/locations/{region}/clusters/{cluster_name}"
    )

    # Extract the endpoint and the authentication information
    endpoint = cluster.endpoint

    # Get default credentials and refresh the token
    credentials, _ = default()
    credentials.refresh(Request())
    token = credentials.token

    return endpoint, token


def create_k8s_client(endpoint, token):
    """
    Create a Kubernetes client using the provided endpoint and token.
    """
    configuration = client.Configuration()
    configuration.host = f"https://{endpoint}"
    configuration.verify_ssl = False  # Set to True in production
    configuration.api_key["authorization"] = (
        f"Bearer {token}"  # Keep this if using token-based auth
    )

    return client.CoreV1Api(client.ApiClient(configuration))


def fetch_ip(api, pod_name):
    """
    Wait for the pod to be in a 'Running' state and return its IP address.
    """
    while True:
        pod = api.read_namespaced_pod(name=pod_name, namespace=NAMESPACE)

        if pod.status.phase == "Running":
            return pod.status.pod_ip

        elif pod.status.phase == "Failed":
            logger.info(f"Pod '{pod_name}' creation is failed.")
            return None

        logger.info(f"Waiting for pod '{pod_name}' to be ready...")
        time.sleep(5)  # Wait before checking again


def create_pod_from_yaml(yaml_file, pod_name, api):
    """
    Function to create POD for each query.

    Parameters:
    - yaml_file: File having configurations of POD
    - pod_name(str): Name of POD to be created.
    """
    # Load the YAML file
    with open(yaml_file, "r") as f:
        pod_manifest = yaml.safe_load(f)

    pod_manifest["metadata"]["name"] = pod_name

    try:
        api.create_namespaced_pod(namespace=NAMESPACE, body=pod_manifest)
        pod_ip = fetch_ip(api, pod_name)

        if pod_ip:
            logger.info(f"Pod '{pod_name}' created with IP: {pod_ip}")
            return pod_ip
        else:
            logger.info(f"Pod '{pod_name}' failed to start, using default POD.")
            return fetch_ip(api, DEFAULT_POD_NAME)

    except Exception as e:
        logger.info(f"Error creating pod: {e}")
        return None


def can_start_query(organization_id, query_throttler_collection):
    """
    Check if there is an available slot for the given organization's query.
    """
    running_count = query_throttler_collection.count_documents(
        {"organization_id": organization_id, "query_status": QueryStatus.PENDING.value}
    )
    return running_count < MAX_RUNNING_QUERIES


def delete_pod_by_ip(namespace, pod_ip, api):
    """
    Deletes a Kubernetes pod using its namespace and IP address without a for loop.

    Parameters:
    - namespace (str): The namespace of the pod.
    - pod_ip (str): The IP address of the pod.

    Returns:
    - str: Success or failure message.
    """

    try:
        # Check it was using Default POD
        default_pod_ip = fetch_ip(api, DEFAULT_POD_NAME)

        if pod_ip == default_pod_ip:
            logger.info("Default POD was being used. Terminating POD deletion step")
            return

        # Fetch all pods in the namespace
        pods = api.list_namespaced_pod(namespace)

        # Use list comprehension to filter the pod with the given IP
        target_pod = next(
            (pod.metadata.name for pod in pods.items if pod.status.pod_ip == pod_ip),
            None,
        )

        if not target_pod:
            return f"No pod found with IP {pod_ip} in namespace {namespace}."

        # Delete the identified pod
        api.delete_namespaced_pod(name=target_pod, namespace=namespace)
        logger.info(f"Pod {target_pod} with IP {pod_ip} deleted successfully.")

    except Exception as e:
        logger.exception(f"Error deleting pod: {e}")


@functions_framework.cloud_event
def main(cloud_event: CloudEvent):
    meta_container = MetaContainer()

    payload = json.loads(
        base64.b64decode(cloud_event.data["message"]["data"]).decode("utf-8")
    )
    meta_container = MetaContainer()

    logger.info("Message received successfully: %s", payload)
    meta_container.set_payload_info(payload)

    organization_id = payload["organization_id"]
    query_id = payload["query_id"]

    # Fetch organization account information
    org_account_info = OrganizationAccountInfo(organization_id)

    mongodb_url = org_account_info.mongodb_url
    organization_db_name = org_account_info.organization_db_name

    mongo_db_client = get_mongodb_client(mongodb_url)
    organization_db = get_mongodb_db(mongo_db_client, organization_db_name)
    query_collection = get_mongodb_collection(organization_db, QUERY_COLLECTION_NAME)

    mongodb_client = get_mongodb_client(CAPABILITIES_MONGO_URI)
    capabilities_db = get_mongodb_db(mongodb_client, CAPABILITIES_DB_NAME)
    query_throttler_collection = get_mongodb_collection(
        capabilities_db, CAPABILITIES_QUERY_THROTTLER_COLLECTION_NAME
    )

    # Get GKE cluster credentials
    endpoint, token = get_gke_cluster_credentials(PROJECT_ID, REGION, CLUSTER_NAME)

    # Create a Kubernetes client
    api = create_k8s_client(endpoint, token)

    current_query = query_throttler_collection.find_one(
        {"query_id": ObjectId(query_id)}
    )

    current_query_status = current_query["query_status"]

    if current_query_status == QueryStatus.NOT_STARTED.value:

        if can_start_query(organization_id, query_throttler_collection):

            query_throttler_collection.update_one(
                {"query_id": ObjectId(query_id)},
                {"$set": {"query_status": QueryStatus.PENDING.value}},
            )
            logger.info(
                f"Starting query with id: {query_id} for organization with id: {organization_id}"
            )

            # Logic to create pod per query - and have a default POD
            pod_name = f"pod-{query_id}"
            pod_ip = create_pod_from_yaml(POD_DEFINITION_YAML, pod_name, api)

            # Add POD IP in metadata of query collection
            query_collection.update_one(
                {"_id": ObjectId(query_id)}, {"$set": {"meta_data.POD_IP": pod_ip}}
            )

            # Send message to sequence coordinator to get hydration started
            payload["publisher_type"] = CONSUMER_TYPE
            logger.info(
                f"Sending message to sequence coordinator to start query with payload: {payload}"
            )
            publish_pubsub_message(PROJECT_ID, SEQUENCE_COORDINATOR_TOPIC_ID, payload)

        else:
            logger.info(
                f"No available slots for organization with id: {organization_id}. Waiting before retrying..."
            )

    elif current_query_status in (
        QueryStatus.COMPLETED.value,
        QueryStatus.FAILED.value,
    ):
        # Delete the pod and record from collection
        logger.info(
            f"Query {query_id} for organization {organization_id} is {current_query_status}. Deleting POD and record from throttler..."
        )
        query_throttler_collection.delete_one({"query_id": ObjectId(query_id)})

        # retireve pod ip from query collection
        query = query_collection.find_one({"_id": ObjectId(query_id)})
        pod_ip = query["meta_data"]["POD_IP"]

        # logic to delete POD using ip upon completion of all vm consumers -- throttler will be called upon completion
        delete_pod_by_ip(NAMESPACE, pod_ip, api)

        logger.info(
            f"Checking for lined up queries after completion of query {query_id}"
        )

        next_query = query_throttler_collection.find_one(
            {"query_status": QueryStatus.NOT_STARTED.value}
        )

        if not next_query:
            logger.info(f"No lined up queries found after query_id: {query_id}")
            return

        next_payload = next_query.get("payload")
        logger.info(
            f"Sending message to throttler to start query with payload: {next_payload}"
        )

        publish_pubsub_message(PROJECT_ID, QUERY_THROTTLER_TOPIC_ID, next_payload)
