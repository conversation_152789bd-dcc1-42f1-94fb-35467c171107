import time
import base64
from datetime import datetime, timezone
import json
from bson import ObjectId
import functions_framework
from cloudevents.http import CloudEvent
from const import (
    ANALYSIS_STATUS_METADATA_KEY,
    BIGQUERY_STATUS_METADATA_KEY,
    CAPABILITIES_CONSUMER_LIST_COLLECTION,
    CAPABILITIES_DB_NAME,
    CAPABILITIES_DIAGNOSTIC_COLLECTION,
    CAPABILITIES_MONGO_URI,
    CONSUMERS_TO_EXCLUDE,
    NOTIFICATION_TOPIC_ID,
    PROJECT_ID,
    QUERY_COLLECTION_NAME,
    WAIT_TIME,
)
from utils.common_utils import update_query_metadata
from utils.error_utils import ErrorUtils
from utils.logger import logger
from utils.mongo_db import get_mongodb_client, get_mongodb_collection, get_mongodb_db
from utils.utilities import (
    DiagnosticActionType,
    DiagnosticStatus,
    MessageType,
    OrganizationAccountInfo,
    PublisherType,
)
from utils.pubsub_publisher import (
    create_notification_pubsub_payload,
    publish_pubsub_message,
)


def get_consumer_list(collection, data_source_id):
    """
    Retrieve consumer list for a specific data source with the provided data_source_id.

    Parameters:
    - collection (pymongo.collection.Collection): The MongoDB consumer list collection to query.
    - data_source_id (str): The identifier of the data source.

    Returns:
    - list: A list of consumer names associated with the data source.
    """
    try:
        document = collection.find_one({"data_source_id": ObjectId(data_source_id)})
        consumer_list = document.get("consumer_list", [])
        if not consumer_list:
            logger.warning(
                "No consumer list found for data source id: '%s'", data_source_id
            )
        return consumer_list

    except Exception as e:
        message = f"Error fetching consumer list: {str(e)}"
        raise Exception(message) from e


def insert_diagnostics(
    current_datetime,
    collection,
    consumer_list,
    query_id,
    data_source_id,
    status,
    consumer_type,
    additional_data,
    message,
    payload,
):
    """
    Insert diagnostics data into the diagnostic collection using consumer list for a specific query_id.

    Parameters:
    - current_datetime (datetime): A datetime object representing the current date and time in Coordinated Universal Time (UTC).
    - collection (pymongo.collection.Collection): The MongoDB diagnostic collection where the diagnostic data will be inserted.
    - consumer_list (list): A list of consumer types associated with the data source.
    - query_id (str): The unique identifier for the query.
    - data_source_id (str): The identifier of the data source.
    - status (str): The status of the consumer, which can be INACTIVE, PENDING, COMPLETED, or FAILED.
    - consumer_type (str): The type of consumer.
    - additional_data (dict): Additional data associated with the diagnostic record.
    - message(str): Message content to be inserted.
    - payload (dict): Payload received from Pub/Sub containing diagnostic information.

    Raises:
    - Exception: If an error occurs while inserting data into the MongoDB collection.
    """
    try:
        data_source_name = payload["data_source_name"]
        organization_id = payload["organization_id"]
        user_id = payload["user_id"]
        records = []
        for consumer in consumer_list:
            updated_status = (
                DiagnosticStatus.PENDING.value if consumer_type == consumer else status
            )
            updated_message = message if consumer_type == consumer else ""
            start_date = current_datetime if consumer_type == consumer else None

            record = {
                "additional_data": additional_data,
                "consumer_type": consumer,
                "data_source_id": ObjectId(data_source_id),
                "data_source_name": data_source_name,
                "message": updated_message,
                "organization_id": ObjectId(organization_id),
                "query_id": ObjectId(query_id),
                "user_id": ObjectId(user_id),
                "status": updated_status,
                "start_date": start_date,
                "created_at": current_datetime,
                "updated_at": current_datetime,
            }
            records.append(record)

        result = collection.insert_many(records)
        logger.info(
            "Diagnostic data for query_id '%s' inserted successfully into the '%s' collection with '%s' records.",
            query_id,
            collection.name,
            len(result.inserted_ids),
        )

    except Exception as e:
        message = f"Error while inserting data for query_id {query_id} into the {collection.name} collection.: {str(e)}"
        raise Exception(message) from e


def mark_query_as_failed(
    consumer_type: str, organization_id: str, publisher_type: str, query_id: str
):
    """
    Mark the specified query as failed in the organization's query metadata.

    Parameters:
    - consumer_type (str): The type of consumer.
    - organization_id (str): The ID of the organization for which to process the metadata.
    - publisher_type (str): The type of publisher.
    - query_id (str): The ID of the query to update metadata for.

    Returns:
    - None

    Exception Handling:
    - None
    """
    # Determine the appropriate status metadata key based on the consumer type.
    status_metadata_key = (
        BIGQUERY_STATUS_METADATA_KEY
        if PublisherType.APPEND.value in {consumer_type, publisher_type}
        else ANALYSIS_STATUS_METADATA_KEY
    )
    # Fetch organization account info
    org_account_info = OrganizationAccountInfo(organization_id)
    mongodb_url = org_account_info.mongodb_url
    organization_db_name = org_account_info.organization_db_name
    # Connect to Organization MongoDB
    mongo_db_client = get_mongodb_client(mongodb_url)
    organization_db = get_mongodb_db(mongo_db_client, organization_db_name)
    # Get the query collection
    query_collection = get_mongodb_collection(organization_db, QUERY_COLLECTION_NAME)
    # Update the query metadata
    update_query_metadata(
        query_collection,
        query_id,
        status_metadata_key,
        DiagnosticStatus.FAILED.value,
    )
    mongo_db_client.close()


def update_diagnostic(
    current_datetime,
    collection,
    query_id,
    status,
    consumer_type,
    message,
    additional_data,
):
    """
    Update the diagnostic data into the diagnostic collection for a specific query_id and consumer_type.

    It supports three status types:
    - PENDING: Indicates that the consumer's task has started.
    - COMPLETED: Indicates that the consumer's task has been completed.
    - FAILED: Indicates that the consumer's task has failed.

    Parameters:
    - current_datetime (datetime): A datetime object representing the current date and time in Coordinated Universal Time (UTC).
    - collection (pymongo.collection.Collection): The MongoDB diagnostic collection where the diagnostic data is stored.
    - query_id (str): The unique identifier for the query.
    - status (str): The status of the consumer, which can be PENDING, COMPLETED, or FAILED.
    - consumer_type (str): The type of consumer.
    - message(str): Message content to be inserted.
    - additional_data (dict): Additional data associated with the diagnostic record.

    Raises:
    - Exception: An error occurred while updating the diagnostic data in the MongoDB diagnostic collection.
    """
    try:
        update_fields = {}
        elapsed_time = 0
        filter_query = {
            "consumer_type": consumer_type,
            "query_id": ObjectId(query_id),
            "status": {"$ne": DiagnosticStatus.COMPLETED.value},
        }
        match status:
            case DiagnosticStatus.PENDING.value:
                existing_record = collection.find_one(
                    filter_query,
                    sort=[("_id", -1)],
                )
                update_fields = {
                    "status": status,
                    "message": message,
                    "updated_at": current_datetime,
                }
                # Check if 'start_date' is missing or has no value
                if existing_record is not None and not existing_record.get(
                    "start_date"
                ):
                    update_fields["start_date"] = current_datetime

            case DiagnosticStatus.COMPLETED.value:
                time.sleep(int(WAIT_TIME))
                existing_record = collection.find_one(
                    filter_query,
                    sort=[("_id", -1)],
                )
                if (
                    existing_record is not None
                    and existing_record.get("start_date") is not None
                ):
                    start_date = existing_record["start_date"].replace(
                        tzinfo=timezone.utc
                    )
                    diff = current_datetime - start_date
                    elapsed_time = int(diff.total_seconds())
                update_fields = {
                    "status": status,
                    "end_date": current_datetime,
                    "elapsed_time": elapsed_time,
                    "message": message,
                    "additional_data": additional_data,
                    "updated_at": current_datetime,
                }

            case DiagnosticStatus.FAILED.value:
                update_fields = {
                    "status": status,
                    "message": message,
                    "updated_at": current_datetime,
                }

            case _:
                raise Exception(f"Invalid status: {status}")

        result = collection.update_many(
            filter_query,
            {"$set": update_fields},
        )
        logger.info(
            "Successfully updated diagnostic data for query_id '%s' and consumer_type '%s' in the '%s' collection with modified_count: %s",
            query_id,
            consumer_type,
            collection.name,
            result.modified_count,
        )
    except Exception as e:
        message = f"Error while updating data for query_id {query_id} and consumer_type {consumer_type} in the {collection.name} collection.: {str(e)}"
        raise Exception(message) from e


@functions_framework.cloud_event
def main(cloud_event: CloudEvent):
    """
    Main method for inserting or updating the diagnostic data and sending message to notification pub sub topic.
    - This method also handles the logic for sending message to notification pub sub topic based on the status of the diagnostic action type.

    Decode and parse the payload from Pub/Sub, extracting necessary fields such as:
    - status: Status of the consumer (INACTIVE, PENDING, COMPLETED, or FAILED).
    - consumer_type: Type of the consumer.
    - action_type: Action to perform (insert, update or info).

    Parameters:
    - cloud_event (CloudEvent): The event received from Pub/Sub. It contains the payload in the 'data' field.

    Returns:
    - {"Status":200}: For successfully execution.
    """
    mongodb_client = None
    try:
        payload = json.loads(
            base64.b64decode(cloud_event.data["message"]["data"]).decode()
        )

        published_time = datetime.fromisoformat(
            cloud_event.data.get("message").get("publishTime")
        )

        logger.info("Message received successfully: %s", payload)

        # Extract payload fields
        action_type = payload["action_type"]
        additional_data = payload["additional_data"]
        consumer_type = payload["consumer_type"]
        publisher_type = payload.get("publisher_type")
        message = payload["message"]
        data_source_id = payload["data_source_id"]
        organization_id = payload["organization_id"]
        query_id = payload["query_id"]
        user_id = payload["user_id"]
        status = payload["status"]

        mongodb_client = get_mongodb_client(CAPABILITIES_MONGO_URI)
        capabilities_db = get_mongodb_db(mongodb_client, CAPABILITIES_DB_NAME)
        diagnostic_collection = get_mongodb_collection(
            capabilities_db, CAPABILITIES_DIAGNOSTIC_COLLECTION
        )

        message_type = None
        # Perform insert, update or info operation based on action type
        match action_type:
            case DiagnosticActionType.INSERT.value:
                consumer_list_collection = get_mongodb_collection(
                    capabilities_db, CAPABILITIES_CONSUMER_LIST_COLLECTION
                )
                consumer_list = get_consumer_list(
                    consumer_list_collection, data_source_id
                )
                insert_diagnostics(
                    published_time,
                    diagnostic_collection,
                    consumer_list,
                    query_id,
                    data_source_id,
                    status,
                    consumer_type,
                    additional_data,
                    message,
                    payload,
                )
            case DiagnosticActionType.UPDATE.value:
                update_diagnostic(
                    published_time,
                    diagnostic_collection,
                    query_id,
                    status,
                    consumer_type,
                    message,
                    additional_data,
                )
                if status == DiagnosticStatus.COMPLETED.value:
                    message_type = MessageType.SUCCESS.value

                if status == DiagnosticStatus.FAILED.value:
                    # Mark the query as failed if the consumer type is not excluded, Connect to the organization's MongoDB only if the query status is 'FAILED'.
                    if consumer_type not in CONSUMERS_TO_EXCLUDE:
                        mark_query_as_failed(
                            consumer_type, organization_id, publisher_type, query_id
                        )

                    message_type = MessageType.ERROR.value
                    error = base64.b64decode(additional_data.get("error"))
                    error_traceback = additional_data.get("error_traceback")
                    consumer_payload = additional_data.get("payload")
                    logger.info(
                        "Error type %s and Consumer payload : %s",
                        error,
                        consumer_payload,
                    )

                    error_utils = ErrorUtils(consumer_type, consumer_payload)
                    error_utils.insert_error_log(error, message, error_traceback)
            case DiagnosticActionType.INFO.value:
                if status == DiagnosticStatus.INFO.value:
                    message_type = MessageType.INFO.value

        if message_type is not None:
            notification_payload = create_notification_pubsub_payload(
                data_source_id,
                query_id,
                organization_id,
                user_id,
                consumer_type,
                message_type,
                message,
                additional_data,
            )
            publish_pubsub_message(
                PROJECT_ID, NOTIFICATION_TOPIC_ID, notification_payload
            )

        return {"Status": 200}

    except Exception as e:
        message = f"An error occurred in diagnostic engine: {str(e)}"
        logger.exception(message)
    finally:
        if mongodb_client is not None:
            mongodb_client.close()
