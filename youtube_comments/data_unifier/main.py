import json
import base64
from pymongo.collection import ObjectId
import functions_framework
import pandas as pd
from cloudevents.http import CloudEvent
from utils.mongo_db import (
    get_mongodb_collection,
    get_mongodb_db,
    get_mongodb_client,
)
from utils.utilities import (
    BatchStatus,
    BatchStatusColumn,
    DiagnosticActionType,
    DiagnosticStatus,
    MetaContainer,
    OrganizationAccountInfo,
)
from utils.common_utils import get_query_config, common_data_unifer, toggle_batch_status
from utils.logger import logger
from utils.pubsub_publisher import publish_pubsub_message
from const import (
    LOOPBACK_THRESHOLD_BATCH_COUNT,
    PROJECT_ID,
    REPLIES,
    YT_DATA_UNIFIER_TOPIC_ID,
    SEQUENCE_COORDINATOR_TOPIC_ID,
    CONSUMER_TYPE
)

meta_container = MetaContainer()


@functions_framework.cloud_event
def main(cloud_event: CloudEvent):
    """
    This cloud function unfies data for file upload consumer.
    - It uses the organization vault to get the oraganzation and query_id to get RAC collection.
    - Define a query filter to find documents where the data unifier status is not "COMPLETED".
    - Query the batch collection using the filter and limit the results to a specified number of batches using LOOPBACK_THRESHOLD_BATCH_COUNT.
    - Iterate over the batches for a query and fetch the data for the batch.
    - Updates the comment_id , video_id,reply_id from the batch details
    - It uses the UNIFIER_META and calls the common_data_unifer method to map the fields specified for each batch
    - If there are inactive batches:
        - Publish a loopback message to a YT data unifier Pub/Sub topic.
    - Else:
        - Publish a message to a Common Cleansing Pub/Sub topic.

    Args:
    - cloud_event (CloudEvent): The CloudEvent object representing the incoming event.

    Returns:
    - Dict with status and message
        - status: 200 if success or 400 if any error occurs
        - message: message of success or error

    Raises:
    - Exception: If any error occurs during the execution process.

    """
    mongodb_client = None
    try:
        # parse the payload and extract query_id and organization_id from payload
        payload = json.loads(
            base64.b64decode(cloud_event.data["message"]["data"]).decode("utf-8")
        )
        logger.info("Message received successfully: %s", payload)
        meta_container.set_payload_info(payload)

        query_id, organization_id = payload["query_id"], payload["organization_id"]

        message = f"Capabilities youtube comments data unifier script started successfully for query_id: {query_id}"
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.PENDING.value,
            message,
        )

        # get organization details from organization_id
        organization = OrganizationAccountInfo(organization_id)

        # set the database connection and get the query collection
        mongodb_client = get_mongodb_client(organization.mongodb_url)
        db = get_mongodb_db(mongodb_client, organization.organization_db_name)
        query_collection = get_mongodb_collection(db, "query")
        # get query details and check if not None
        query = get_query_config(
            query_collection, query_id, {"_id": 1, "name": 1, "meta_data": 1}
        )
        # Filter to find documents where data unifier status is not "COMPLETED"
        query_filter = {
            "query_id": ObjectId(query_id),
            BatchStatusColumn.DATA_UNIFIER_STATUS.value: {
                "$ne": BatchStatus.COMPLETED.value
            },
        }

        if query is not None:
            # set the meta data
            meta_container.set_meta_data(query["meta_data"])

            # set the query collection name
            collection_name = meta_container.meta_data["RAC_COLLECTION_NAME"]
            batch_collection_name = meta_container.meta_data["BATCH_COLLECTION_NAME"]
            logger.info("collection name %s", collection_name)
            collection = get_mongodb_collection(db, collection_name)
            batch_collection = get_mongodb_collection(db, batch_collection_name)

            # load data in DataFrame and call the common_data_unifer with UNIFIER_META mapping
            batches = batch_collection.find(query_filter).limit(
                int(LOOPBACK_THRESHOLD_BATCH_COUNT)
            )
            # Iterate over batches and update the field mapping
            for batch in batches:
                unifier_meta = meta_container.meta_data["UNIFIER_META"]
                batch_id = str(batch["_id"])
                message = f"Processing for batch {batch_id} and query {query_id}"
                logger.info(message)
                # status toggle for the batch to PENDING
                toggle_batch_status(
                    batch_collection,
                    batch_id,
                    query_id,
                    BatchStatusColumn.DATA_UNIFIER_STATUS.value,
                    BatchStatus.PENDING.value,
                )
                # Fetch the data for batch and unify with mapping
                df = pd.DataFrame(collection.find({"id": {"$in": batch["ids"]}}))

                # set video_id comment_id and reply_id
                df["video_id"] = batch["video_id"]
                df["comment_id"] = batch["comment_id"]
                if batch["batch_type"] == REPLIES:
                    df["reply_id"] = df["id"].str.split(".", expand=True)[1]
                    unifier_meta = unifier_meta["reply"]
                else:
                    unifier_meta = unifier_meta["comment"]
                common_data_unifer(df, unifier_meta, collection)

                # status toggle for the batch to complete
                toggle_batch_status(
                    batch_collection,
                    str(batch["_id"]),
                    query_id,
                    BatchStatusColumn.DATA_UNIFIER_STATUS.value,
                    BatchStatus.COMPLETED.value,
                )

            # Get the count of documents where data unifier status is not "COMPLETED"
            inactive_batch_count = batch_collection.count_documents(query_filter)
            if inactive_batch_count:
                publish_pubsub_message(PROJECT_ID, YT_DATA_UNIFIER_TOPIC_ID, payload)
                logger.info(
                    f"Published loopback message for '{inactive_batch_count}' inactive batches"
                )
                return "Success", 200
            
            payload["publisher_type"] = CONSUMER_TYPE
            publish_pubsub_message(PROJECT_ID, SEQUENCE_COORDINATOR_TOPIC_ID, payload)
            message = f"Capabilities youtube comments data unifier script completed successfully for query_id {query_id} and updated field mapping in {collection_name} collection"
            meta_container.send_diagnostic(
                DiagnosticActionType.UPDATE.value,
                DiagnosticStatus.COMPLETED.value,
                message,
            )
            return {"status": 200, "message": message}
        else:
            message = f"Query details not found for id {query_id}"
            logger.warning(message)
            return {"status": 400, "message": message}

    except Exception as e:
        message = f"An error occurred during capabilities youtube comments data unifier while updating the field mapping: {e}"
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.FAILED.value,
            message,
        )
        return {"status": 400, "message": message}
    finally:
        if mongodb_client is not None:
            mongodb_client.close()
