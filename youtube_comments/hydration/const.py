import os

QUERY_COLLECTION_NAME = os.getenv("QUERY_COLLECTION_NAME")
PROJECT_ID = os.getenv("PROJECT_ID")
DATA_UNIFIER_TOPIC_ID = os.getenv("DATA_UNIFIER_TOPIC_ID")
HYDRATION_TOPIC_ID = os.getenv("HYDRATION_TOPIC_ID")
BASE_LOOPBACK_THRESHOLD = os.getenv("BASE_LOOPBACK_THRESHOLD")
SEQUENCE_COORDINATOR_TOPIC_ID = os.getenv("SEQUENCE_COORDINATOR_TOPIC_ID")
CONSUMER_TYPE = os.getenv("CONSUMER_TYPE")

COMMENTS = "comments"
REPLIES = "replies"
PROJECTION = {
    "_id": 1,
    "name": 1,
    "meta_data": 1,
    "source.data_source_name": 1,
}

SLEEP_TIME = 5
RAC_COLLECTION_NAME_METADATA_KEY = "RAC_COLLECTION_NAME"
INVALID_VIDEO_IDS_METADATA_KEY = "INVALID_VIDEO_IDS"

INVALID_VIDEO_IDS_MSG_TEMPLATE = (
    "Hi There! We couldn't find the following video IDs: <b>{formatted_ids}</b> "
    "for your query titled '<b>{query_name}</b>' (ID: <b>{query_id}</b>)."
)
QUOTA_EXCEEDED_MSG_TEMPLATE = (
    "Hi There! We weren't able to process your query '<b>{query_name}</b>' "
    "(ID: <b>{query_id}</b>) for video ID '<b>{video_id}</b>' because your quota has been exceeded. "
    "Don't worry, we'll resume processing once your quota is restored. Thank you for your patience!"
)

LARGE_VOLUME = 300_000
