import time
import pandas as pd
import requests
from bson import ObjectId
from googleapiclient.errors import HttpError
from const import QUOTA_EXCEEDED_MSG_TEMPLATE, REPLIES, SLEEP_TIME

from utils.logger import logger
from utils.utilities import DiagnosticActionType, DiagnosticStatus


def create_batch_data(
    response,
    video_id,
    page_no,
    batch_type,
    query_id,
    current_page_cursor=None,
    next_page_cursor=None,
    comment_id=None,
    batches=[],
):
    """
    Creates a batch of data containing comment_ids, video_id, and current_page_cursor from comments response.

    Args:
        response (dict): Response containing comments/replies for a YouTube video.
        video_id (str): The ID of the YouTube video.
        page_no (int): Page number of the comments
        batch_type (str): batch type of the for youtube comments
        query_id (str | ObjectId): query_id of the query
        current_page_cursor (str, optional): Cursor representing the current page. Defaults to None.
        comment_id (str | ObjectId) : comment id of the replies

    Returns:
        list: List of dictionaries representing each comment thread in the batch.
              Each dictionary contains keys: 'comment_id', 'video_id', 'current_page_cursor'.

    Raises:
        Exception: If failed to create batches.
    """
    try:
        # check if there are comments/replies in page
        if "items" in response and response["items"]:
            ids = pd.DataFrame(response["items"])["id"].tolist()
            batch = {
                "ids": ids,
                "video_id": video_id,
                "comment_id": comment_id,
                "page_no": page_no,
                "current_page_cursor": current_page_cursor,
                "next_page_cursor": next_page_cursor,
                "batch_type": batch_type,
                "query_id": ObjectId(query_id),
            }
            batches.append(batch)
    except Exception as e:
        message = f"Failed to create batches: {e}"
        logger.exception(message)
        raise e

    return batches


def fetch_video_info(youtube_api, meta_container, video_id):
    """
    Fetch information for a single YouTube video using the provided YouTube API client and return as a dict.

    Parameters:
    - youtube (googleapiclient.discovery.Resource): An authenticated YouTube Data API service object.
    - video_id (str): The YouTube video ID to fetch information for.

    Returns:
    - dict: Response containing video information.

    Exception Handling:
    - Exception: Raised if failed to retrieve video information.
    """
    video_info = {}
    try:
        # Fetch video details using the API
        youtube_api_build = youtube_api

        response = (
            youtube_api_build.videos()
            .list(part="snippet,contentDetails,statistics", id=video_id)
            .execute()
        )
        # Check if the response contains items
        if "items" in response and response["items"]:
            video_info = response["items"][0]
            return video_info

        logger.warning(f"No information found for video ID: {video_id}")

    except HttpError as e:

        message = f"Failed to retrieve video information: {e}"
        logger.exception(message)
        raise e

    return video_info


def retrieve_replies(
    youtube_api,
    comment_id,
    video_id,
    query_id,
    batches,
):
    """
    Recursive function to retrieve all replies to a comment.

    Args:
        youtube (googleapiclient.discovery.Resource): An authenticated YouTube Data API service object.
        comment_id (str): comment id for the replies
        video_id (str): video_id of the comment/replies
        query_id (str): query_id of the query
        batch_collection (Collection): MongoDB collection for batches
        total_reply_count (int): The total number of replies that have been submitted in response to the top-level comment.
        meta_container (object): An instance of a class containing metadata and payload
                               for the query, including diagnostic methods.

    Return:
        replies (list) : list of the replies

    Raises:
        Exception: If failed to retrieve replies.
    """
    replies = []
    try:
        current_page_token = None
        actual_reply_count = 0
        page = 1
        while True:
            # Fetch replies to the comment
            youtube_api_build = youtube_api
            replies_response = (
                youtube_api_build.comments()
                .list(
                    part="id,snippet",
                    parentId=comment_id,
                    textFormat="plainText",
                    maxResults=100,  # Adjust maxResults per page as needed (max: 100)
                    pageToken=current_page_token,
                )
                .execute()
            )

            next_page_token = replies_response.get("nextPageToken")
            if next_page_token is not None and next_page_token == current_page_token:
                message = (
                    f"Next page token matches the current page token ({next_page_token}) "
                    f"while fetching replies for comment ID {comment_id} on video ID {video_id}."
                )
                raise Exception(message)

            create_batch_data(
                replies_response,
                video_id,
                page,
                REPLIES,
                query_id,
                current_page_token,
                next_page_token,
                comment_id,
                batches,
            )

            # Append each reply to the list and update actual reply count
            replies_response_data = replies_response["items"]

            replies.extend(replies_response_data)
            actual_reply_count += len(replies_response_data)

            # Update the current page token and increment page number
            current_page_token = next_page_token
            page += 1

            # If no more pages, break the loop
            if not next_page_token:
                break

    except HttpError as e:
        message = f"Failed to retrieve replies: {e}"
        logger.error(message)
        raise e

    return replies


def retrieve_comments(
    youtube_api,
    video_id,
    meta_container,
    current_page_token=None,
    max_retries=5,
):
    """
    Retrieves comments for a YouTube video using the YouTube Data API.

    Args:
        youtube (googleapiclient.discovery.Resource): An authenticated YouTube Data API service object.
        video_id (str): The ID of the YouTube video.
        meta_container (object): An instance of a class containing metadata and payload
                               for the query, including diagnostic methods.
        current_page_token (str, optional): Token to retrieve the current page of results. Defaults to None.
        max_retries (int, optional): Maximum number of retries in case of API request failures. Defaults to 3.

    Returns:
        dict: The response containing comments for the specified video.

    Exception Handling:
    - HTTP 404 Not Found: Returns None If the status code is 404 and the error reason is "videoNotFound", it logs a warning
                        indicating that the video ID was not found and skips processing by returning None.
    - Exception: Raised if maximum retries are reached and the API request still fails.
    """
    retries = 0
    while retries <= max_retries:
        try:
            youtube_api_build = youtube_api
            comments_response = (
                youtube_api_build.commentThreads()
                .list(
                    part="id,replies,snippet",
                    videoId=video_id,
                    textFormat="plainText",
                    maxResults=100,
                    pageToken=current_page_token,
                )
                .execute()
            )
            return comments_response

        except HttpError as e:
            if e.resp.status == 404:
                error_details = e.error_details[0] if e.error_details else {}
                if error_details.get("reason") == "videoNotFound":
                    logger.warning(f"Video ID '{video_id}' not found. Skipping.")
                    return None
            if e.resp.status == 403:
                error_details = e.error_details[0] if e.error_details else {}
                if error_details.get("reason") == "commentsDisabled":
                    logger.warning(
                        f"Video ID '{video_id}' comments are disabled. Skipping."
                    )
                    return None

                if error_details.get("reason") == "forbidden":
                    logger.warning(
                        f"Video ID '{video_id}' is forbidden (members only) . Skipping."
                    )
                    return None
                handle_quota_exceeded(video_id, meta_container, error_details)
            retries += 1
            if retries > max_retries:
                raise Exception(
                    f"Failed to retrieve comments after {max_retries} retries for video_id {video_id}: {e}"
                ) from e
            else:
                logger.error(f"Error occurred: {e}. Retrying in 5 seconds...")
                time.sleep(SLEEP_TIME)


def get_total_comment_volume(api_key, video_ids):
    """
    Calculate the total comment volume for a list of YouTube video IDs.

    This function uses the YouTube Data API to fetch statistics for multiple videos
    in a single request and calculates the total number of comments across all videos.

    Args:
        api_key (str): Your YouTube Data API key.
        video_ids (list): A list of YouTube video IDs.

    Returns:
        int: The total number of comments across all specified videos.

    """
    # Base URL for the YouTube Data API
    base_url = "https://www.googleapis.com/youtube/v3/videos"

    # Convert list of video IDs to a comma-separated string
    video_ids_str = ",".join(video_ids)

    # Parameters for the API request
    params = {"part": "statistics", "id": video_ids_str, "key": api_key}

    # Make the API request
    response = requests.get(base_url, params=params, timeout=60)
    data = response.json()

    # Check if the response contains video statistics
    if "items" not in data:
        return 0  # No data found

    # Convert the API response to a pandas DataFrame
    df = pd.DataFrame(data["items"])

    # Extract the 'statistics' column and normalize it into a DataFrame
    stats_df = pd.json_normalize(df["statistics"])

    # Convert the 'commentCount' column to numeric (handling missing values)
    stats_df["commentCount"] = pd.to_numeric(
        stats_df["commentCount"], errors="coerce"
    ).fillna(0)

    # Calculate the total comment volume
    total_comment_volume = stats_df["commentCount"].sum()
    return int(total_comment_volume)


def get_total_comment_volume_batched(api_key, video_ids, batch_size=50):
    """
    Calculate total comment volume for large lists of video IDs using batch processing.

    Args:
        api_key (str): Your YouTube Data API key.
        video_ids (list): List of YouTube video IDs.
        batch_size (int): Number of video IDs per batch (max 50).

    Returns:
        int: Total number of comments.
    """
    total_comments = 0
    for i in range(0, len(video_ids), batch_size):
        batch = video_ids[i : i + batch_size]
        total_comments += get_total_comment_volume(api_key, batch)
    return total_comments


def handle_quota_exceeded(video_id, meta_container, error_details):
    """
    Handles the scenario where a request exceeds the YouTube API quota limit and triggers diagnostic.

    Parameters:
    - video_id (str): The ID of the video that caused the quota exceeded error.
    - meta_container (object): An instance of a class containing metadata and payload
                            for the query, including diagnostic methods.
    - e (Exception): The exception object raised by the API call, containing
                    error details and response status.

    Returns:
    - None

    Exception Handling:
    - None
    """
    # Check if the exception has a 403 status and the reason is 'quotaExceeded'
    if error_details.get("reason") == "quotaExceeded":
        message = QUOTA_EXCEEDED_MSG_TEMPLATE.format(
            query_name=meta_container.meta_data["QUERY_NAME"],
            query_id=meta_container.payload["query_id"],
            video_id=video_id,
        )
        logger.warning(message)
        meta_container.send_diagnostic(
            DiagnosticActionType.INFO.value,
            DiagnosticStatus.INFO.value,
            message,
        )
    return False
