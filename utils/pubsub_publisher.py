"""
This module provides utilities for creating and publishing 
messages to Google Cloud Pub/Sub topics.
"""

from typing import Dict, Any
import json
from google.cloud import pubsub_v1
from utils.logger import logger

publisher = pubsub_v1.PublisherClient(
    publisher_options=pubsub_v1.types.PublisherOptions(
        enable_message_ordering=True,
    )
)


def create_notification_pubsub_payload(
    data_source_id: str,
    query_id: str,
    organization_id: str,
    user_id: str,
    consumer_type: str,
    message_type: str,
    message: str,
    additional_data: dict,
) -> Dict[str, Any]:
    """
    Creates a payload for Google Cloud Pub/Sub topic.
    Parameters:
    - data_source_id (str): The identifier of the data source.
    - query_id (str): Id of the query.
    - organization_id (str): Id of the organization.
    - user_id (str): The unique identifier for the user.
    - consumer_type (str): Type of the consumer generating the message.
    - message_type (str): Type of the message.
    - message (str): The main content of the message.
    - additional_data (dict): Additional data associated with the message.
    Returns:
    - dict: The generated payload.
    """
    return {
        "data_source_id": data_source_id,
        "query_id": query_id,
        "organization_id": organization_id,
        "user_id": user_id,
        "consumer_type": consumer_type,
        "message_type": message_type,
        "message": message,
        "additional_data": additional_data,
    }


def create_diagnostics_pubsub_payload(
    action_type: Dict,
    additional_data: Dict,
    consumer_type: str,
    message: str,
    query_id: str,
    status: str,
    data_source_id: str,
    user_id: str,
    data_source_name: str,
    organization_id: str,
) -> Dict:
    """
    Creates a payload for Google Cloud Pub/Sub topic.

    Parameters:
    - action_type (dict): Diagnostic action type that has two values insert or update.
    - additional_data (dict): Additional data to be inserted.
    - consumer_type (str): Type of the consumer generating the message.
    - message(str): Message to be inserted.
    - query_id (str): The query id of a query collection document.
    - status (str): Type of the status.
    - data_source_id (str): Id of the data source.
    - user_id (str): The unique identifier for the user.
    - data_source_name (str): Name of the data source.
    - organization_id (str): The ID of the organization.

    Returns:
    - dict: The generated payload.
    """
    return {
        "action_type": action_type,
        "additional_data": additional_data,
        "consumer_type": consumer_type,
        "message": message,
        "query_id": query_id,
        "status": status,
        "data_source_id": data_source_id,
        "user_id": user_id,
        "data_source_name": data_source_name,
        "organization_id": organization_id,
    }


def publish_pubsub_message(
    project_id: str, topic_id: str, payload: Dict, ordering_key: bool = None
) -> None:
    """
    Publishes a message to a Google Cloud Pub/Sub topic.

    - This function constructs the topic path using the provided project ID and topic ID.
    - It encodes the payload as JSON and publishes it to the specified Pub/Sub topic.
    - Logs information about the published message, including its ID and the topic path.
    - In case of an error during publishing, an exception is logged with details.

    Parameters:
    - project_id (str): The Google Cloud project ID.
    - topic_id (str): The id of the Pub/Sub topic.
    - payload (dict): The message payload.

    Raises:
    - Exception: If an error occurs while publishing the message.
    """
    try:
        topic_path = publisher.topic_path(project_id, topic_id)
        payload_json = json.dumps(payload).encode("utf-8")
        future = publisher.publish(topic_path, payload_json, ordering_key=ordering_key)
        message_id = future.result()

        logger.info(
            "Message published with ID: '%s' to topic: '%s'", message_id, topic_path
        )

    except Exception as e:
        message = f"Error publishing message to {topic_id} topic_id: {e}"
        raise Exception(message) from e
