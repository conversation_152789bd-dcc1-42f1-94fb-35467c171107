"""
Module to define error utils
"""

from datetime import datetime, timedelta
import json
import pickle
from bson import ObjectId
from pymongo import MongoClient
from utils.const import (
    CAPABILITIES_DB_NAME,
    CAPABILITIES_MONGO_URL,
    ERROR_CONFIG,
    ERROR_LOG_COLLECTION,
)


class ErrorUtils:
    """
    A utility class for logging and managing errors in the system.

    This class is responsible for managing errors, inserting error logs into MongoDB,
    and retrieving error configuration from the database.
    """

    def __init__(self, consumer_type, payload):
        self.consumer_type = consumer_type
        self.payload = json.dumps(payload)
        self.query_id = ObjectId(payload["query_id"])
        self.organization_id = ObjectId(payload["organization_id"])
        self.error = None
        self.error_type = None
        self.error_message = None
        self.error_reason = None
        self.mongo_client = MongoClient(CAPABILITIES_MONGO_URL)

    def insert_error_log(self, error_bytes, error_message, error_traceback):
        """
        Inserts an error log into the MongoDB collection.

        Parameters:
            error_bytes (bytes): Serialized error object.
            error_message (str): A descriptive message about the error.
            error_traceback (str): The traceback string of the error.
        """
        db = self.mongo_client[CAPABILITIES_DB_NAME]
        collection = db[ERROR_LOG_COLLECTION]
        self.error = pickle.loads(error_bytes)
        self.error_type = str(self.error)
        self.error_message = error_message


        error_details = (
            self.error.error_details[0]
            if getattr(self.error, "error_details",{}) and self.error.error_details
            else {}
        )

        self.error_reason = error_details.get("reason") or "Unknown"
        error_log = {
            "consumer_type": self.consumer_type,
            "payload": self.payload,
            "query_id": self.query_id,
            "organization_id":self.organization_id,
            "error": error_bytes,
            "error_message": error_message,
            "error_traceback": error_traceback,
            "error_reason": self.error_reason,
        }

        error_config = self.get_error_config()

        trigger_time = datetime.now()

        if error_config is not None:
            trigger_time += timedelta(minutes=int(error_config.get("trigger_interval")))

        error_log["trigger_time"] = trigger_time
        collection.insert_one(error_log)

    def get_error_config(self):
        """
        Retrieves the error handling configuration from the MongoDB collection.

        Returns:
        dict: The configuration document for the error, or None if no matching
            configuration is found.

        The function searches the `ERROR_CONFIG` collection for an error configuration
        document matching the error type, consumer type, and error reason.
        """
        db = self.mongo_client[CAPABILITIES_DB_NAME]
        collection = db[ERROR_CONFIG]

        return collection.find_one(
            {
                "error_type": self.error_type,
                "consumer_type": self.consumer_type,
                "error_reason": self.error_reason,
            }
        )
