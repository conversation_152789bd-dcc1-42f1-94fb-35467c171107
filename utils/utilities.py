"""
Module to define all the enums
"""

from datetime import datetime
import base64
from enum import Enum
import pickle
import time
import traceback
import requests
from utils.logger import logger
from utils.pubsub_publisher import (
    create_diagnostics_pubsub_payload,
    publish_pubsub_message,
)
from utils.const import (
    ORGANIZATION_VAULT_BASE_URL,
    ORGANIZATION_VAULT_ORG_ENDPOINT,
    VAULT_API_BASE_DELAY,
    VAULT_API_MAX_RETRIES,
    PROJECT_ID,
    DIAGNOSTIC_TOPIC_ID,
    CONSUMER_TYPE,
)


class BatchStatus(Enum):
    """Enumeration for batch status."""

    INACTIVE = "INACTIVE"
    PENDING = "PENDING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"


class BatchStatusColumn(Enum):
    """Enumeration for batch status column names."""

    HYDRATION_STATUS = "hydration_status"
    RAC_STATUS = "rac_status"
    CLEANSING_STATUS = "cleansing_status"
    DATA_UNIFIER_STATUS = "data_unifier_status"


class CategorizationProcessingField(Enum):
    """Enumeration for the Categorization processing field Name"""

    CLUSTER = "is_cluster_categorization_processed"
    STORY = "is_story_categorization_processed"
    THEME = "is_theme_categorization_processed"


class DataSourceId(Enum):
    """
    Enumeration representing data source ids.
    """

    DATABASE_AS_DATASOURCE = "665d973539608b9aa9f05a8f"
    FILE_UPLOAD = "66067d884469a10686feff37"
    SPRINKLR_API = "6605b690df8ad607995e9a97"
    YT_COMMENTS = "66211db016389fb92ea7c1b2"


class DiagnosticActionType(Enum):
    """Enumeration for diagnostic action types."""

    UPDATE = "update"
    INSERT = "insert"
    INFO = "info"


class DiagnosticStatus(Enum):
    """Enumeration for Diagnostic status."""

    INACTIVE = "INACTIVE"
    PENDING = "PENDING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"
    INFO = "INFO"


class EncapsulationMarkerStatus(Enum):
    """Enumeration for encapsulation marker status."""

    INACTIVE = "INACTIVE"
    PENDING = "PENDING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"


class EncapsulationMarkerStatusColumn(Enum):
    """Enumeration for encapsulation marker status column names."""

    SEQUENCE_COORDINATOR_STATUS = "sequence_coordinator_status"
    CATEGORIZATION_TOOL_STATUS = "categorization_tool_status"
    DISCOVER_STATUS = "discover_status"
    EMBEDDING_STATUS = "embedding_status"
    QUERY_OPTIMIZER_STATUS = "query_optimizer_status"
    SENTIMENT_STATUS = "sentiment_status"
    SENTIMENT_MULTI_STATUS = "sentiment_multi_status"
    SIMILARITY_STATUS = "similarity_status"
    SUMMARIZATION_STATUS = "summarization_status"


class QueryArchiverRetrieverStatus(Enum):
    """Enumeration for query archiver retriever status."""

    PENDING = "PENDING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"


class MessageType(Enum):
    """Enumeration for message types."""

    SUCCESS = "SUCCESS"
    ERROR = "ERROR"
    PENDING = "PENDING"
    INACTIVE = "INACTIVE"
    INFO = "INFO"


class MongoDBCollection(Enum):
    """
    Enumeration representing MongoDB collection names.
    """

    ORGANIZATION_DOMAIN_INFORMATION = "organization_domain_information"
    ORGANIZATION_DATA_SOURCE = "organization_data_source"
    MASTER_DATA_SOURCE = "master_data_source"
    NOTIFICATION = "notification"
    NOTIFICATION_CONFIG = "notification_config"
    NOTIFICATION_EMAIL_LOG = "notification_email_log"
    NOTIFICATION_TEMPLATE = "notification_template"
    USER_INFO_COLLECTION = "user_information"


class FileExtension(Enum):
    """
    Enumeration for file extension
    """

    CSV = ".csv"
    JSON = ".json"
    TEXT = ".txt"
    XLSX = ".xlsx"
    HTML = ".html"
    XML = ".xml"
    YAML = ".yaml"


class Encapsulation(Enum):
    """Enumeration for Encapsulation."""

    DAILY = "daily"
    WEEKLY = "weekly"
    BIWEEKLY = "biweekly"
    MONTHLY = "monthly"
    QUARTERLY = "quarterly"
    ALLATONCE = "allatonce"


class PublisherType(Enum):
    """Enumeration for publisher types."""

    API_SERVER = "API_SERVER"
    QUERY_THROTTLER = "QUERY_THROTTLER"
    HYDRATION = "HYDRATION"
    DATA_UNIFIER = "DATA_UNIFIER"
    CLEANSING = "COMMON_CLEANSING"
    APPEND = "APPEND"
    CATEGORIZATION_TOOL = "CATEGORIZATION_TOOL"
    DATA_ENCAPSULATION = "DATA_ENCAPSULATION"
    DATA_TRANSFORMER = "DATA_TRANSFORMER"
    DISCOVER = "DISCOVER"
    SENTIMENT = "SENTIMENT"
    SENTIMENT_MULTI = "SENTIMENT_MULTI"
    SIMILARITY = "SIMILARITY"
    SUMMARIZATION = "SUMMARIZATION"
    QUERY_ARCHIVER_RETRIEVER = "QUERY_ARCHIVER_RETRIEVER"
    QUERY_MIGRATOR = "QUERY_MIGRATOR"
    QUERY_OPTIMIZER = "QUERY_OPTIMIZER"
    QUERY_DATA_EXPORT = "QUERY_DATA_EXPORT"
    SEQUENCE_COORDINATOR = "SEQUENCE_COORDINATOR"


class QueryOperationType(Enum):
    """Enumeration for query Operation Types"""

    ARCHIVE = "ARCHIVE"
    RETRIEVE = "RETRIEVE"


class CollectionType(Enum):
    """Enumeration for specifying collection types in a database."""

    RAC = "rac"
    RAC_TRANSFORM = "rac_transform"


class SentimentProcessingField(Enum):
    """Enumeration for the Sentiment processing field Name"""

    CLUSTER = "is_cluster_sentiment_processed"
    STORY = "is_story_sentiment_processed"
    THEME = "is_theme_sentiment_processed"


class SummaryProcessingField(Enum):
    """Enumeration for the Summary processing field Name"""

    CLUSTER = "is_cluster_summary_processed"
    STORY = "is_story_summary_processed"
    THEME = "is_theme_summary_processed"


class SummaryContext(Enum):
    """Enumeration for the Summary context"""

    CLUSTER = "cluster"
    STORY = "story"
    THEME = "theme"
    ALL_PROCESSED = "all_processed"


class SummaryContextIdField(Enum):
    """Enumeration for the Summary context ID field Name"""

    CLUSTER = "Unique_Cluster_ID"
    STORY = "Unique_Story_ID"
    THEME = "Themes"


class YoutubeAPIQueryType(Enum):
    """Enumeration for the query_type for YouTubeAPI"""

    SMALL = "SMALL"
    BIG = "BIG"


class MetaContainer:
    """Container class for metadata."""

    def __init__(self):
        """Initialize a new MetaContainer."""
        self.meta_data = None
        self.payload = None

    def set_meta_data(self, meta_data):
        """
        Set the payload for the metadata container.

        Parameters:
        - meta_data: The meta_data to be set.
        """
        if isinstance(meta_data, dict):
            self.meta_data = meta_data
        elif isinstance(meta_data, list):
            self.meta_data = meta_data[0]
        else:
            self.meta_data = {}

    def update_meta_data(self, new_payload):
        """
        Update the existing metadata with new key-value pairs.

        Parameters:
        - new_payload: The payload containing new key-value pairs to be added.
        """
        self.meta_data.update(new_payload)

    def set_payload_info(self, payload):
        """
        Set the payload for the metadata container.

        Parameters:
        - payload: The payload to be set.
        """
        self.payload = payload

    def send_diagnostic(
        self, action_type, status, message, error=None, consumer_type=None
    ):
        """
        Publishes a message to diagnostic queue with the provided payload to toggle diagnostic status and update diagnostic data.

        Parameters:
        - action_type: Action to perform (insert or update).
        - status: The status of the diagnostics message.
        - message (str): The content of the message to be sent.
        """
        payload = self.payload
        meta_data = self.meta_data

        organization_id = payload.get("organization_id")
        query_id = payload.get("query_id")
        data_source_id = payload.get("data_source_id")
        user_id = payload.get("user_id")
        data_source_name = meta_data.get("data_source_name") if meta_data else None

        if status == DiagnosticStatus.FAILED.value:
            logger.error(message)
        else:
            logger.info(message)

        error_traceback = traceback.format_exc()
        if error is not None:
            error = base64.b64encode(pickle.dumps(error)).decode("utf-8")

        if consumer_type is None:
            consumer_type = CONSUMER_TYPE

        payload = create_diagnostics_pubsub_payload(
            action_type,
            {
                "payload": self.payload,
                "error": error,
                "error_traceback": error_traceback,
            },
            consumer_type,
            message,
            query_id,
            status,
            data_source_id,
            user_id,
            data_source_name,
            organization_id,
        )
        publish_pubsub_message(
            PROJECT_ID,
            DIAGNOSTIC_TOPIC_ID,
            payload,
            ordering_key=str(datetime.now().timestamp()),
        )


class OrganizationAccountInfo:
    """
    A class to fetch organization account information including hydration bucket name,
    MongoDB URL, organization db name, and rac_collection_suffix from an organization vault API.

    Attributes:
    - organization_id (str): The ID of the organization.

    Methods:
    - hydration_bucket_name: Retrieve the hydration bucket name associated with the organization.
    - query_archive_bucket_name: Retrieve the query archive bucket name associated with the organization.
    - client_api_mongodb_url: Retrieve the client api MongoDB URL associated with the organization.
    - mongodb_url: Retrieve the MongoDB URL associated with the organization.
    - organization_db_name: Retrieve the organization DB name associated with the organization.
    - rac_collection_suffix: Retrieve the rac collection suffix associated with the organization.
    """

    def __init__(self, organization_id):
        self.organization_id = organization_id
        self._account_info = None

    def _fetch_account_info(self):
        """
        Fetch organization account information if not already fetched.
        """
        if not self._account_info:
            self._account_info = self._get_organization_account_info()

    def _get_organization_account_info(self):
        """
        Retrieve organization account information from the API.

        Returns:
        - dict: The organization account information as a dictionary.

        Raises:
        - Exception: If maximum retries are exceeded without success.
        """
        params = {"organization_id": self.organization_id}
        retry_count = 0

        while retry_count < int(VAULT_API_MAX_RETRIES):
            try:
                response = requests.get(
                    f"{ORGANIZATION_VAULT_BASE_URL}{ORGANIZATION_VAULT_ORG_ENDPOINT}",
                    params=params,
                    timeout=30,
                )
                response.raise_for_status()
                organization_data = response.json()
                return organization_data

            except requests.RequestException as e:
                logger.error(
                    "An error occurred while fetching organization account information: %s %s",
                    e,
                    (
                        e.response.json()
                        if hasattr(e, "response") and hasattr(e.response, "json")
                        else None
                    ),
                )
                retry_count += 1
                backoff_delay = int(VAULT_API_BASE_DELAY) * (
                    2**retry_count
                )  # Exponential back-off formula
                logger.warning(
                    "Retrying to retrieve organization account info again in %s seconds...",
                    backoff_delay,
                )
                time.sleep(backoff_delay)

        raise Exception(
            "Maximum retries reached, Unable to retrieve organization account information."
        )

    @property
    def hydration_bucket_name(self):
        """
        Retrieve the hydration bucket name associated with the organization.
        """
        self._fetch_account_info()
        return self._account_info.get("hydration_bucket_name")

    @property
    def data_export_bucket_name(self):
        """
        Retrieve the data export bucket name associated with the organization.
        """
        self._fetch_account_info()
        return self._account_info.get("data_export_bucket_name")

    @property
    def query_archive_bucket_name(self):
        """
        Retrieve the query archive bucket name associated with the organization.
        """
        self._fetch_account_info()
        return self._account_info.get("query_archive_bucket_name")

    @property
    def client_api_mongodb_url(self):
        """
        Retrieve the client api MongoDB URL associated with the organization.
        """
        self._fetch_account_info()
        return self._account_info.get("client_api_mongodb_url")

    @property
    def mongodb_url(self):
        """
        Retrieve the MongoDB URL associated with the organization.
        """
        self._fetch_account_info()
        return self._account_info.get("mongodb_url")

    @property
    def organization_db_name(self):
        """
        Retrieve the organization MongoDB name associated with the organization.
        """
        self._fetch_account_info()
        return self._account_info.get("organization_db_name")

    @property
    def rac_collection_suffix(self):
        """
        Retrieve the organization rac collection suffix associated with the organization.
        """
        self._fetch_account_info()
        return self._account_info.get("rac_collection_suffix")

    @property
    def result_storage_bucket_name(self):
        """
        Retrieve the organization result storage bucket name associated with the organization.
        """
        self._fetch_account_info()
        return self._account_info.get("result_storage_bucket_name")


class VmConsumers(Enum):
    """
    A class to define the VM consumers
    """

    SUMMARIZATION_VM = "SUMMARIZATION_VM"
    DISCOVER = "DISCOVER"
    SIMILARITY_VM = "SIMILARITY_VM"
    CATEGORIZATION_TOOL = "CATEGORIZATION_TOOL"


class QueryStatus(Enum):

    NOT_STARTED = "not_started"
    PENDING = "pending"
    COMPLETED = "completed"
    FAILED = "failed"
    READY = "READY"
    ARCHIVE = "ARCHIVED"
    DELETE = "DELETED"
    FAIL = "FAILED"
