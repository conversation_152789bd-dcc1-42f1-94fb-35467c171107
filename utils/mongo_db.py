"""
This module provides utility functions to interact with a 
MongoDB instance, including:
"""

import time
from pymongo.database import Database
from pymongo import MongoClient
from pymongo.collection import Collection
from utils.logger import logger

MONGO_DB_CONNECTION_MAX_RETRIES = 5
MONGO_DB_CONNECTION_BASE_DELAY = 1


def get_mongodb_client(url: str) -> MongoClient | None:
    """
    Get a MongoDB client based on the MongoDB URL.

    Parameters:
    - url (str): The MongoDB connection URL.

    Returns:
    - pymongo.MongoClient: A MongoClient instance representing the
        connection to the MongoDB instance.

    Raises:
    - Exception: If the maximum number of retries is reached without successfully
        connecting to the MongoDB instance.
    """
    retry_count = 0

    while retry_count < int(MONGO_DB_CONNECTION_MAX_RETRIES):
        try:
            mongo_client = MongoClient(url)
            # Test the connection by requesting server info
            mongo_client.server_info()
            return mongo_client

        except Exception as e:
            retry_count += 1
            if retry_count < MONGO_DB_CONNECTION_MAX_RETRIES:
                backoff_delay = int(MONGO_DB_CONNECTION_BASE_DELAY) * (
                    2**retry_count
                )  # Exponential back-off formula

                logger.warning(
                    "MongoDB connection attempt '%d' failed: '%s'. Retrying in '%d' seconds...",
                    retry_count,
                    e,
                    backoff_delay,
                )
                time.sleep(backoff_delay)

            else:
                message = f"Maximum {retry_count} retries reached, MongoDB Connection failed. Could not retrieve server information.: {str(e)}"
                raise Exception(message)


def get_mongodb_collection(db: Database, collection_name: str) -> Collection:
    """
    Get a MongoDB collection based on the database instance and collection name.

    Parameters:
    - db (pymongo.database.Database): The MongoDB database instance.
    - collection_name (str): The name of the collection to retrieve.

    Returns:
    - pymongo.collection.Collection: The MongoDB collection.
    """
    return db[collection_name]


def get_mongodb_db(client: MongoClient, db_name: str) -> Database:
    """
    Get a MongoDB database instance.

    Parameters:
    - client (pymongo.MongoClient): The MongoClient instance to use for database connection.
    - db_name (str): The name of the MongoDB database to connect to.

    Returns:
    - pymongo.database.Database: A MongoDB database instance.
    """
    db = client[db_name]
    return db
