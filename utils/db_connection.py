"""
Module to define mongodb connection
"""

import urllib.parse
import pandas as pd
import pymongo
from pymongo.collection import Collection
from sqlalchemy import create_engine, URL, text
from utils.logger import logger


class MongoDBConnection:
    """
    A class used to establish a connection to a MongoDB database.

    Attributes:
    - username (str): The username for authentication.
    - password (str): The password for authentication.
    - hostname (str): The hostname or IP address of the MongoDB server.
    - database_name (str): The name of the database to connect to.
    - collection_name (str): The name of the collection within the database.
    - port (str): The port number of the MongoDB server. If not provided, it defaults to an empty string.
    """

    def __init__(self, meta_data):
        """
        Initialize MongoDBConnection instance with provided metadata.

        Parameters:
        - meta_data (dict): A dictionary containing the necessary connection details.
                          It should contain keys: 'USERNAME', 'PASSWORD', 'HOSTNAME',
                          'DATABASE_NAME', 'COLLECTION_NAME', and optionally 'PORT'.
        """
        self.username = urllib.parse.quote_plus(meta_data["USERNAME"])
        self.password = urllib.parse.quote_plus(meta_data["PASSWORD"])
        self.hostname = urllib.parse.quote_plus(meta_data["HOSTNAME"])
        self.database_name = meta_data["DATABASE_NAME"]
        self.collection_name = meta_data["TABLE_NAME"]
        self.port = urllib.parse.quote_plus(meta_data.get("PORT", ""))

    def connect(self) -> Collection:
        """
        Establishes a connection to the MongoDB server and returns the specified collection.

        Returns:
        - Collection: The specified MongoDB collection.

        Exception Handling:
        - If an error occurs while connecting to the MongoDB server.
        """
        try:
            if self.port:
                connection_url = f"mongodb://{self.username}:{self.password}@{self.hostname}:{self.port}/"
       
            else:
                connection_url = (
                    f"mongodb+srv://{self.username}:{self.password}@{self.hostname}/"
                )
            client = pymongo.MongoClient(connection_url)
            db = client[self.database_name]
       
            collection = db[self.collection_name]
       
            logger.info(
                "Successfully connected to MongoDB at %s:%s", self.hostname, self.port
            )
            return collection
    
        except Exception as e:
            message = f"Error occurred while connecting to MongoDB at {self.hostname}:{self.port}"
            raise Exception(message) from e


class SQLDBConnection:
    """
    A class used to establish a connection to a SQL database and perform CRUD operations.

    Attributes:
    - drivername (str): The name of the SQL database driver.
    - username (str): The username for authentication.
    - password (str): The password for authentication.
    - hostname (str): The hostname or IP address of the SQL database server.
    - port (str): The port number of the SQL database server.
    - database_name (str): The name of the database to connect to.
    - table_name (str): The name of the table within the database.

    Methods:
    - connect(): Establishes a connection to the SQL database server and returns the database engine and table name.
    - insert(): Inserts a record into the table.
    - update(): Updates records in the table.
    - select(): Fetches records from the table.
    - delete(): Deletes records from the table.
    """

    def __init__(self, meta_data):
        """
        Initialize SQLDBConnection instance with provided metadata.

        Parameters:
        - meta_data (dict): A dictionary containing the necessary connection details.
                          It should contain keys: 'DATABASE_TYPE', 'USERNAME', 'PASSWORD',
                          'HOSTNAME', 'PORT', 'DATABASE_NAME', and 'TABLE_NAME'.
        Attributes:
        - drivername (str): The name of the SQL database driver.
        - username (str): The username for authentication.
        - password (str): The password for authentication.
        - hostname (str): The hostname or IP address of the SQL database server.
        - port (str): The port number of the SQL database server.
        - database_name (str): The name of the database to connect to.
        - table_name (str): The name of the table within the database.
        - engine (Engine): The SQLAlchemy engine object representing the database connection.
        """
        self.drivername = meta_data["DATABASE_TYPE"].lower()
        self.username = meta_data["USERNAME"]
        self.password = meta_data["PASSWORD"]
        self.hostname = meta_data["HOSTNAME"]
        self.port = meta_data["PORT"]
        self.database_name = meta_data["DATABASE_NAME"]
        self.table_name = meta_data["TABLE_NAME"]
        self.engine = self.create_engine()
        self.conn = self.engine.connect()

    def connect(self):
        """
        Establishes a connection to the SQL database server and returns the database engine and table name.

        Returns:
        - tuple: A tuple containing the database engine and table name.

        Exception Handling:
        - ValueError: If an unsupported database type is provided.
        - If an error occurs while connecting to the SQL database.
        """
        try:
            engine = self.create_engine()
            logger.info(
                "Successfully connected to %s database at %s:%s",
                self.drivername,
                self.hostname,
                self.port,
            )
            return engine, self.table_name
     
        except Exception as e:
            message = f"Error occurred while connecting to SQL database at {self.hostname}:{self.port}"
            raise Exception(message) from e

    def create_engine(self):
        """Creates a SQLAlchemy engine based on the provided database type."""
        return create_engine(self.create_url())

    def create_url(self):
        """
        Creates a database URL based on the provided database type, username, password, hostname, port, and database name.

        Parameters:
        - self (SQLDBConnection): The instance of the SQLDBConnection class.

        Returns:
        - URL: A SQLAlchemy URL object representing the database connection.

        Exception Handling:
        - ValueError: If an unsupported database type is provided.
        """
        dialect_driver_map = {
            "mysql": "mysql+pymysql",
            "postgres": "postgresql+pg8000",
            "oracle": "oracle+cx_oracle",
        }
     
        if self.drivername not in dialect_driver_map:
            raise ValueError(f"Unsupported database type: {self.drivername}")

        url_object = URL.create(
            dialect_driver_map[self.drivername],
            username=self.username,
            password=self.password,
            host=self.hostname,
            port=self.port,
            database=self.database_name,
        )
        return url_object

    def insert(self, table, data):
        """
        Inserts records into the specified table.

        Parameters:
        - table: The table object where records are to be inserted.
        - data (list[dict]): A list of dictionaries containing the data to be inserted. Each dictionary should match the column names in the table.

        Return:
        - None

        Exception Handling:
        - If an error occurs while inserting the records into the SQL database.
        """
        try:
            df = pd.DataFrame(data)
            df.to_sql(table, self.conn, if_exists="append", index=False)
            logger.info(
                "Successfully inserted records into table '%s' in SQL database", table
            )
       
        except Exception as e:
            message = f"Error occurred while inserting records into '{table.name}' table in SQL database: {e}"
            raise Exception(message) from e

    def update(self, query):
        """
        Updates records in the specified table based on the provided query.

        Parameters:
        - query:  A simple query should be a valid SQL UPDATE statement.

        Returns:
        - None

        Exception Handling:
        - If an error occurs while updating the records in the SQL database.
        """
        try:
            stmt = text(query)
            self.conn.execute(stmt)
            logger.info("Successfully updated records in SQL database")
     
        except Exception as e:
            message = f"Error occurred while updating records in SQL database: {e}"
            raise Exception(message) from e

    def select(self, query):
        """
        Fetches records from the SQL database based on the provided query.

        Parameters:
        - query: A simple query to identify the records to fetch.

        Returns:
        - pandas DataFrame: A DataFrame representing the fetched records.

        Exception Handling:
        - If an error occurs while fetching records from the SQL database.
        """
        try:
            df = pd.read_sql(query, self.conn)
            logger.info("Successfully fetched records from SQL database")
            return df
     
        except Exception as e:
            message = f"Error occurred while fetching records from SQL database: {e}"
            raise Exception(message) from e

    def delete(self, query):
        """
        Deletes records from the specified table based on the provided query.

        Parameters:
        - query: A simple SQL statement representing the query to identify the records to delete.

        Returns:
        - None

        Exception Handling:
        - If an error occurs while deleting records from the SQL database.
        """
        try:
            stmt = text(query)
            self.conn.execute(stmt)
            logger.info("Successfully deleted records from SQL database")

        except Exception as e:
            message = f"Error occurred while deleting records from SQL database: {e}"
            raise Exception(message) from e
