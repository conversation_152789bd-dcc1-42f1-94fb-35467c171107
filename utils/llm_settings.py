"""
Module to set LLM settings
"""

import os

# --------------------- General ----------------------

GEMINI_MODELS = [
    "gemini-flash",
    "gemini-1.5-flash",
    "gemini-1.5-flash-001",
    "gemini-1.5-flash-002",
    "gemini-pro",
    "gemini-1.5-pro",
]

GPT_MODELS = ["gpt-3.5-turbo", "gpt-3.5-turbo-1106"]

# Embedding Models
VERTEX_EMBEDDING_MODELS = ["text-embedding-004"]

SUMMARIES_TO_IGNORE = [
    None,
    "",
    "No summary generated",
    "Please provide the text you would like me to summarize. I need the text to be able to create a concise and coherent summary for you. \n",
]

MAX_RETRIES = 0

# Time (in seconds) to wait before retrying
RESOURCE_EXHAUST_RETRY_TIME = os.getenv("RESOURCE_EXHAUST_RETRY_TIME", 540)
SERVER_ERRORS_RETRY_TIME = os.getenv("SERVER_ERRORS_RETRY_TIME", 400)

# ------------------- Gemini -----------------------

# textbison model parameters for summarization and sentiment prediction
gemini_parameters = {
    "temperature": 0.1,  # determines randomness
    "max_output_tokens": 250,  # determines length of generated output
    "top_p": 0.8,  # determines top p sampling probability
    "top_k": 40,  # determines top k sampling probability as a threshold
    "response_mime_type": "text/plain",
}

# https://ai.google.dev/gemini-api/docs/safety-settings
from google.generativeai.types import (
    HarmCategory,
    HarmBlockThreshold,
)  # for production change these values to block offensive outputs

gemini_safety_parameters = {
    HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_NONE,
    HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_NONE,
    HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_NONE,
    HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_NONE,
}

GEMINI_MAX_TOKEN_COUNT = 1000_000
VERTEX_EMBEDDING_MAX_TOKEN_COUNT = 20_000

# ------------------------------ OPENAI ------------------------------

GPT_MAX_TOKEN_COUNT = 10_000
