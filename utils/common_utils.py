"""
This module contains common methods used in different consumers.
"""

from typing import List, Dict
import re
import threading
from concurrent.futures import ThreadPoolExecutor
from bson import ObjectId
import requests
import numpy as np
import pandas as pd
from pymongo.collection import Collection
from pymongo.operations import UpdateOne
from utils.pubsub_publisher import publish_pubsub_message
from utils.utilities import MongoDBCollection
from utils.const import (
    PROJECT_ID,
    DEFAULT_SENTIMENT,
    SENTIMENT_CLASSIFICATION,
)
from utils.logger import logger


def check_marker_status(
    collection: Collection,
    marker_id: str,
    query_id: str,
    status_column: str,
    status: str,
) -> bool:
    """
    Checks if a document exists in the MongoDB collection that matches the
    encapsulation_marker, query_id, and has the specified status in the status_column.

    Parameters:
    - collection (pymongo.collection.Collection): The MongoDB collection object to search within.
    - marker_id (str): The encapsulation marker id to check for in the document.
    - query_id (str): The identifier of a particular query.
    - status_column (str): The name of the column representing status.
    - status (str): The specific status value to check.

    Returns:
    - bool: True if a matching document is found, otherwise False.

    Exception Handling:
    - Exception: Raised if an error occurs while checking marker status.
    """
    try:
        query_filter = {
            "_id": ObjectId(marker_id),
            "query_id": ObjectId(query_id),
            status_column: status,
        }
        # Check if any document matches the filter
        result = collection.find_one(query_filter, {"_id": 1})
        return result is not None

    except Exception as e:
        message = f"Error checking encapsulation marker status for '{query_id}' query_id and '{marker_id}' marker_id: {e}"
        raise Exception(message) from e


def check_query_status(
    collection: Collection, query_id_str: str, status_column: str, status: str
) -> bool:
    """
    Check if all documents for a particular query_id have a specific status value.

    Parameters:
    - collection (pymongo.collection.Collection): The MongoDB collection.
    - query_id_str (str): The identifier of a particular query.
    - status_column (str): The name of the column representing status.
    - status (str): The specific status value to check.

    Returns:
    - bool: True if all documents have the specified status value, False otherwise.

    Raises:
    - Exception: If an error occurs while checking query status.
    """
    try:
        query_id = ObjectId(query_id_str)

        # Check if all documents for the query_id have the specified status value
        query = {"query_id": query_id, status_column: status}
        completeness = collection.count_documents(query) == collection.count_documents(
            {"query_id": query_id}
        )

        return completeness

    except Exception as e:
        message = f"Error checking query status for query_id: {query_id_str} => {e}"
        raise Exception(message) from e


def convert_array_columns_to_strings(df: pd.DataFrame) -> pd.DataFrame:
    """
    Convert columns with array-like values in a DataFrame to comma-separated strings.

    Parameters:
    - df (pd.DataFrame): The input pandas DataFrame.

    Returns:
    - pd.DataFrame: The updated DataFrame with array-like columns converted to strings.

    Exception Handling:
    - None
    """
    # Identify columns with at least one list-like value
    array_columns = [
        col for col in df.columns if df[col].apply(lambda x: isinstance(x, list)).any()
    ]

    for column in array_columns:
        df[column] = df[column].apply(
            lambda x: (
                ", ".join(map(str, x))
                if isinstance(x, list)
                else None if pd.isnull(x) else x
            )
        )

    return df


def convert_objectid_to_string(val) -> str:
    """
    Convert ObjectId to string if the input is an ObjectId.

    Parameters:
    - value: The value to be converted.

    Returns:
    - str: If the input is an ObjectId, it returns the string representation of the ObjectId.
           Otherwise, it returns the input value unchanged.
    """
    return str(val) if isinstance(val, ObjectId) else val


def create_indexes(collection: Collection, index_specs: List[Dict]) -> None:
    """
    Create multiple indexes in parallel on a MongoDB collection.

    Parameters:
    - collection (pymongo.collection.Collection): The MongoDB collection on which indexes are created.
    - index_specs (list): A list of dictionaries where each dictionary specifies an index.
                        Each dict should contain 'fields' (list of tuples of field names and types)

    Returns:
    - None

    Exception Handling:
    - Exception: Raised if an error occurs while creating index on MongoDB collection.
    """
    try:

        def create_index(index_spec: Dict):
            """
            Helper function to create an index based on the provided specification.

            Parameters:
            - index_spec (Dict): A dictionary containing the index specification.

            Returns:
            - str: The name of the created index.
            """
            fields = index_spec["fields"]
            return collection.create_index(fields)

        # Using ThreadPoolExecutor to create indexes in parallel
        with ThreadPoolExecutor() as executor:
            futures = [
                executor.submit(create_index, index_spec) for index_spec in index_specs
            ]
            # Collect results to ensure all indexes are created
            for future in futures:
                result = future.result()
                if result:
                    logger.info(f"Index '{result}' created successfully")

    except Exception as e:
        message = f"Error while creating index on '{collection.name}' collection: {e}"
        raise Exception(message) from e


def create_projection(mapping: dict, prefix: str = "") -> Dict:
    """
    Creates a MongoDB projection object based on the values of a nested dictionary.
    For each value in the dictionary, it sets the corresponding field in the projection
    to 1, which will include that field in the MongoDB query result.
    A prefix can be added to each field to apply a common structure.

    Parameters:
    - mapping (dict): A dictionary where the values represent field paths in the MongoDB document.
                        These paths can include dot notation for accessing nested fields.
    - prefix (str, optional): A string to prefix to each field path in the projection. If provided, the
                        prefix will be prepended to all field paths.

    Returns:
    - dict: A MongoDB projection object where the field paths from the dictionary values
            (prefixed by the provided prefix) are set to 1, indicating that these fields
            should be included in the query result.

    Exception Handling:
    - None
    """
    projection = {}

    # Loop through each key-value pair in the input dictionary
    for _, value in mapping.items():
        if isinstance(value, dict):
            # If the value is another dictionary, recursively process it
            nested_projection = create_projection(value, prefix)
            projection.update(nested_projection)
        else:
            # If the value is not a dictionary, it's a field path to project
            field_with_prefix = f"{prefix}.{value}" if prefix else value
            projection[field_with_prefix] = 1

    return projection


def delete_indexes(collection: Collection, indexes: List[str]) -> None:
    """
    Deletes multiple indexes from the specified MongoDB collection if they exist and logs the result.

    Parameters:
    - collection (pymongo.collection.Collection): The MongoDB collection object from which the indexes should be deleted.
    - indexes (List[str]): A list of index names to be deleted.

    Returns:
    - None

    Exception Handling:
    - Exception: Raised if an error occurs while deleting index on MongoDB collection.
    """
    try:
        # Retrieve all existing index names
        existing_indexes = collection.index_information().keys()

        for index in indexes:
            if index in existing_indexes:
                collection.drop_index(index)
                logger.info(f"Index '{index}' deleted successfully")
            else:
                logger.warning(f"Index '{index}' does not exist in the collection")

    except Exception as e:
        message = f"Error while deleting index on '{collection.name}' collection: {e}"
        raise Exception(message) from e


def fetch_collection_aggregated_data(
    collection: Collection,
    aggregation_pipeline: list = None,
    offset: int = None,
    limit: int = None,
):
    """
    Fetches records from a MongoDB collection based on the provided pipeline, offset, and limit, and returns them as a Pandas DataFrame.
    Parameters:
    - collection (pymongo.collection.Collection): The MongoDB collection to fetch data from.
    - aggregation_pipeline (list, optional): The MongoDB aggregation pipeline to fetch the data from MongoDB.Defaults to an empty list.
    - offset (int, optional): The number of documents to skip before starting to fetch. Defaults to None (no offset applied).
    - limit (int, optional): The maximum number of documents to fetch. Defaults to None (no limit applied).
    Returns:
    - pandas.DataFrame: DataFrame containing the fetched data.
    Exception Handling:
    - Exception: Raised if an error occurs while fetching data from MongoDB collection.
    """
    try:
        aggregation_pipeline = (
            aggregation_pipeline or []
        )  # Default to fetching all documents if no pipeline is provided
        # Base query with optional projection
        # Apply offset and limit only if they are provided
        aggregation_pipeline.append({"$sort": {"_id": 1}})
        if offset is not None:
            aggregation_pipeline.append({"$skip": offset})
        if limit is not None:
            aggregation_pipeline.append({"$limit": limit})
        cursor = collection.aggregate(aggregation_pipeline)
        # Convert the cursor to a DataFrame
        df = pd.DataFrame(list(cursor))
        logger.info(
            "Successfully fetched %d record(s) from '%s' collection (offset=%s, limit=%s)",
            len(df),
            collection.name,
            offset or "0",
            limit or "None",
        )
        return df

    except Exception as e:
        message = f"Error while fetching data from '{collection.name}' collection: {e}"
        raise Exception(message) from e


def drop_existing_columns(df: pd.DataFrame, columns_to_drop: List[str]) -> None:
    """
    Drops columns from a DataFrame if they exist.

    Parameters:
    - df (pd.DataFrame): The DataFrame from which columns need to be dropped.
    - columns_to_drop (List[str]): List of column names to be dropped.

    Returns:
    - None

    Exception Handling:
    - None
    """
    # Filter the columns to drop only those that exist in the DataFrame
    columns_to_drop = [col for col in columns_to_drop if col in df.columns]
    # Drop the filtered columns from the DataFrame
    df.drop(columns=columns_to_drop, inplace=True)


def fetch_collection_data(
    collection: Collection,
    query: dict = None,
    projection: dict = None,
    offset: int = None,
    limit: int = None,
) -> pd.DataFrame:
    """
    Fetches records from a MongoDB collection based on the provided query, projection, offset, and limit, and returns them as a Pandas DataFrame.

    Parameters:
    - collection (pymongo.collection.Collection): The MongoDB collection to fetch data from.
    - query (dict, optional): The MongoDB query filter. Defaults to an empty dictionary.
    - projection (dict, optional): The MongoDB projection filter to specify the fields to include or exclude. Defaults to None (all fields included).
    - offset (int, optional): The number of documents to skip before starting to fetch. Defaults to None (no offset applied).
    - limit (int, optional): The maximum number of documents to fetch. Defaults to None (no limit applied).

    Returns:
    - pandas.DataFrame: DataFrame containing the fetched data.

    Exception Handling:
    - Exception: Raised if an error occurs while fetching data from MongoDB collection.
    """
    try:
        query = query or {}  # Default to fetching all documents if no query is provided
        cursor = collection.find(query, projection).sort(
            "_id", 1
        )  # Base query with optional projection
        # Apply offset and limit only if they are provided
        if offset is not None:
            cursor = cursor.skip(offset)
        if limit is not None:
            cursor = cursor.limit(limit)
        # Convert the cursor to a DataFrame
        df = pd.DataFrame(cursor)
        logger.info(
            "Successfully fetched %d record(s) from '%s' collection (offset=%s, limit=%s)",
            len(df),
            collection.name,
            offset or "0",
            limit or "None",
        )
        return df

    except Exception as e:
        message = f"Error while fetching data from '{collection.name}' collection: {e}"
        raise Exception(message) from e


def get_organization_info(
    organization_domain_info_collection: Collection, organization_id: str
) -> dict:
    """
    Retrieve organization information from the MongoDB collection.

    Parameters:
    - organization_domain_info_collection (pymongo.collection.Collection): The MongoDB collection object
        containing organization domain information.
    - organization_id (str): The ID of the organization to retrieve information for.

    Returns:
    - dict: A dictionary containing the organization's information.

    Raises:
    - Exception: If an error occurs while retrieving the organization information.
    """
    try:
        query = {"_id": ObjectId(organization_id)}
        organization_info = organization_domain_info_collection.find_one(query)
        if organization_info is not None:
            organization_info["id"] = str(organization_info.pop("_id"))
        return organization_info

    except Exception as e:
        message = (
            f"Error retrieving organization information for ID {organization_id}: {e}"
        )
        logger.error(message)
        raise e


def get_query_config(
    collection: Collection,
    query_id_str: str,
    projection_attributes: dict,
) -> Dict | None:
    """
    Retrieve query configuration document from the MongoDB collection.

    Parameters:
    - collection (pymongo.collection.Collection): The MongoDB query collection.
    - query_id_str (str): The ID of the query to retrieve configuration data for.
    - projection_attributes (dict): The projection attributes to include or exclude from the result.

    Returns:
    - dict or None: The query configuration document if found, None otherwise.

    Raises:
    - Exception: If an error occurs while retrieving the query configuration.
    """
    try:
        query_id = ObjectId(query_id_str)

        # Retrieve document from the collection based on the query_id
        document = collection.aggregate(
            [
                {"$match": {"_id": query_id}},
                {
                    "$lookup": {
                        "from": MongoDBCollection.ORGANIZATION_DATA_SOURCE.value,
                        "localField": "source_id",
                        "foreignField": "_id",
                        "as": "source",
                    }
                },
                {"$unwind": {"path": "$source"}},
                {"$project": projection_attributes},
            ]
        )
        return next(document, None)

    except Exception as e:
        message = (
            f"Error while retrieving config data for query_id: {query_id_str} => {e}"
        )
        raise Exception(message) from e


def set_batch_count(
    collection: Collection, batch_id_str: str, query_id_str: str, batch_count: int
) -> None:
    """
    Set the batch_count value of a document in a MongoDB collection based on batch_id and query_id.

    Parameters:
    - collection (pymongo.collection.Collection): The MongoDB collection.
    - batch_id_str (str): The identifier of a particular batch.
    - query_id_str (str): The identifier of a particular query.
    - batch_count (int): The new value for the batch_count field.

    Raises:
    - Exception: If an error occurs while setting the batch count.
    """
    try:
        batch_id = ObjectId(batch_id_str)
        query_id = ObjectId(query_id_str)

        # Set the batch_count value for the given batch_id and query_id
        result = collection.update_one(
            {"_id": batch_id, "query_id": query_id},
            {"$set": {"batch_count": batch_count}},
        )
        logger.info(
            "batch_count for batch_id: '%s' and query_id: '%s' in '%s' collection updated to '%s' with modified_count: '%s'.",
            batch_id_str,
            query_id_str,
            collection.name,
            batch_count,
            result.modified_count,
        )

    except Exception as e:
        message = f"Error during batch_count update for batch_id: {batch_id_str} and query_id: {query_id_str} => {e}"
        raise Exception(message) from e


def toggle_batch_status(
    collection: Collection,
    batch_id_str: str,
    query_id_str: str,
    status_column: str,
    new_status: str,
) -> None:
    """
    Update the status of a document in a MongoDB collection based on both batch_id and query_id.

    Parameters:
    - collection (pymongo.collection.Collection): The MongoDB collection.
    - batch_id_str (str): The identifier of a particular batch.
    - query_id_str (str): The identifier of a particular query.
    - status_column (str): The name of the column representing status.
    - new_status (str): The new status to set for the updated document.

    Raises:
    - Exception: If an error occurs while toggling batch status.
    """
    try:
        batch_id = ObjectId(batch_id_str)
        query_id = ObjectId(query_id_str)

        # Update the document with the given batch_id and query_id
        result = collection.update_one(
            {"_id": batch_id, "query_id": query_id},
            {"$set": {status_column: new_status}},
        )
        logger.info(
            "Status for row with batch_id: '%s' and query_id: '%s' in '%s' collection updated to '%s' with modified_count: '%s'.",
            batch_id_str,
            query_id_str,
            collection.name,
            new_status,
            result.modified_count,
        )

    except Exception as e:
        message = f"Error during toggling batch status for batch_id: {batch_id_str} and query_id: {query_id_str} to status:{new_status} => {e}"
        raise Exception(message) from e


def toggle_marker_status(
    collection: Collection,
    marker_id: str,
    query_id: str,
    status_column: str,
    new_status: str,
) -> None:
    """
    Update the status of a document in a MongoDB collection based on both marker_id and query_id.

    Parameters:
    - collection (pymongo.collection.Collection): The MongoDB collection.
    - marker_id (str): The identifier of a particular encapsulation marker.
    - query_id (str): The identifier of a particular query.
    - status_column (str): The name of the column representing status.
    - new_status (str): The new status to set for the updated document.

    Raises:
    - Exception: If an error occurs while toggling batch status.
    """
    try:
        # Update the document with the given marker_id and query_id
        result = collection.update_one(
            {"_id": ObjectId(marker_id), "query_id": ObjectId(query_id)},
            {"$set": {status_column: new_status}},
        )
        logger.info(
            "Status for row with encapsulation marker_id: '%s' and query_id: '%s' in '%s' collection updated to '%s' with modified_count: '%s'.",
            marker_id,
            query_id,
            collection.name,
            new_status,
            result.modified_count,
        )

    except Exception as e:
        message = f"Error during toggling encapsulation marker status for {marker_id} marker_id and {query_id} query_id to {new_status} status: {e}"
        raise Exception(message) from e


def update_mongodb_collection(
    collection: Collection, dataframe: pd.DataFrame, encapsulation_marker: str = None
) -> None:
    """
    Updates a MongoDB collection with data from a Pandas DataFrame using a bulk write operation.

    Parameters:
    - collection (pymongo.collection.Collection): The MongoDB collection to update.
    - dataframe (pandas.DataFrame): The DataFrame containing data to update.
    - encapsulation_marker (str, optional): The name of the encapsulation marker.

    Returns:
    - None

    Exception Handling:
    - ValueError: Raised if the DataFrame does not contain an '_id' column.
    - Exception: Raised If an error occurs during the update process.
    """
    try:
        # Check for mandatory '_id' column
        if "_id" not in dataframe.columns:
            raise ValueError(
                "DataFrame must contain an '_id' column for matching documents in MongoDB."
            )

        bulk_updates = []
        for _, row in dataframe.iterrows():
            mongo_id = ObjectId(row["_id"])
            update_data = {
                "$set": {col: row[col] for col in dataframe.columns if col != "_id"}
            }
            bulk_updates.append(UpdateOne({"_id": mongo_id}, update_data))

        # Execute bulk updates
        result = collection.bulk_write(bulk_updates)
        matched_count = result.matched_count
        updated_count = result.modified_count
        logger.info(
            f"Updated {updated_count} documents for '{encapsulation_marker}' encapsulation marker in the '{collection.name}' collection"
        )
        if matched_count != updated_count:
            logger.info(
                "%d documents were not modified (already matched).",
                matched_count - updated_count,
            )

    except Exception as e:
        message = f"An error occurred while updating documents for '{encapsulation_marker}' encapsulation marker in the '{collection.name}' collection: {e}"
        raise Exception(message) from e


def update_offset_and_publish(
    offset: int,
    offset_metadata_key: str,
    total_documents: int,
    loopback_threshold: int,
    payload: Dict,
    batch_count: int,
    query_collection: Collection,
    query_id: str,
    topic_id: str,
) -> int:
    """
    Updates the offset in query metadata based on the number of documents fetched,
    and publishes a Pub/Sub message if necessary.

    Parameters:
    - offset (int): The current offset.
    - offset_metadata_key (str): Specifies the key used for setting the offset in
        the query's metadata.
    - total_documents (int): The total number of documents in collection.
    - loopback_threshold (int): Threshold for the loopback.
    - payload (dict): The data payload to be published.
    - batch_count (int): The number of fetched documents.
    - query_collection (pymongo.collection.Collection): The MongoDB query collection.
    - query_id (str) : Query ID for the query
    - topic_id (str) : The id of the Pub/Sub topic.

    Returns:
    - int: The new offset.

    Exception Handling:
    - None
    """
    # Calculate the new offset
    new_offset = offset + batch_count

    # Update the query metadata with the new offset
    update_query_metadata(
        query_collection,
        query_id,
        offset_metadata_key,
        new_offset,
    )
    # Publish a Pub/Sub message if there are more documents to process
    if loopback_threshold <= new_offset < total_documents:
        publish_pubsub_message(PROJECT_ID, topic_id, payload)
        logger.info(
            f"Published loopback message for offset {new_offset} out of {total_documents}"
        )

    return new_offset


def common_data_unifer(data_df: pd.DataFrame, mapping: dict, collection) -> None:
    """
    Unifies the data with mapping and updates the mapped fied to destination collection
    Args:
        - data_df (pd.DataFrame) : dataframe of the source
        - mapping (dict) : contains the mapping of destination_field and source_field
        - collection : MongoDB collection were we need to update the field
    Raise:
       - Exception: if any error occurred while mapping or updating fields
    """
    try:
        message = f"Field mapping :{mapping}"
        logger.info(message)

        def get_nested_field(row, field_str):
            """
            Function to access nested field for a dataframe

            Args:
            - row (pd.Series): row of the data frame
            - field_str : field with dot seperated

            Returns:
            - value | None : when field is present it will access and return value else None
            """
            fields = field_str.split(".")
            value = row

            # iterate over nested fields and access value of the field
            for field in fields:
                if (
                    isinstance(value, dict) or isinstance(value, pd.core.series.Series)
                ) and field in value:
                    value = value[field]
                else:
                    return None
            return value

        columns = []
        for destination_field, source_field in mapping.items():
            # Mapping values from source to destination field
            data_df[destination_field] = data_df.apply(
                lambda row: get_nested_field(row, source_field), axis=1
            )

            # special type casting for date field to datetime
            if destination_field == "date":
                data_df[destination_field] = pd.to_datetime(data_df[destination_field])
                data_df.replace({np.nan: None}, inplace=True)
            columns.append(destination_field)

        message = f"Destination fields :{columns}"
        logger.info(message)
        records = []

        for _, row in data_df.iterrows():
            record = {}
            for column in columns:
                record[column] = row[column]
            records.append(
                UpdateOne(
                    {"_id": row["_id"]},
                    {"$set": record},
                )
            )

        collection.bulk_write(records)
        message = f"Successfully updated the new fields to {collection.name} collection"
        logger.info(message)

    except Exception as e:
        message = f"Error while updating field in common data unifier: {e}"
        raise Exception(message) from e


def generate_collection_name(query_id: str, query_name: str, suffix: str) -> str:
    """
    Generate a collection name by concatenating the sanitized query_name, query_id, and suffix.

    Parameters:
    - query_id (str): The unique identifier for the query.
    - query_name (str): The name of the query.
    - suffix (str): The suffix to be appended to the collection name.

    Returns:
    - str: The generated collection name.

    Raises:
    - Exception: If an error occurs while generating collection name.
    """
    try:
        # Trim leading and trailing whitespace, convert to lowercase, and remove characters
        # that are not letters, digits, underscores, and whitespaces from query name
        query_name = re.sub(r"[^\w\s]", "", query_name.strip().lower())

        # Replace whitespace characters with underscores
        query_name = "_".join(query_name.split())

        # Concatenate the sanitized query_name with other parts to form the collection name
        collection_name = f"{query_name}_{query_id}_{suffix}"

        return collection_name

    except Exception as e:
        message = (
            f"Error while generating collection name for query_id: {query_id} => {e}"
        )
        raise Exception(message) from e


def update_query_metadata(
    collection: Collection, query_id_str: str, meta_data_key: str, value
) -> None:
    """
    Update the value of a metadata key for a query document in the query collection.

    Parameters:
    - collection: pymongo query collection object.
    - query_id_str (str): The string representation of the ObjectId of the query document.
    - meta_data_key (str): The key of the metadata to update.
    - value: The new value of the metadata key.

    Raises:
    - Exception: If an error occurs while updating query metadata.
    """
    try:
        query_id = ObjectId(query_id_str)
        meta_data_array_query = {"_id": query_id, "meta_data": {"$type": "array"}}
        meta_data_object_query = {"_id": query_id, "meta_data": {"$type": "object"}}

        # Check if meta_data is an array of objects
        if collection.count_documents(meta_data_array_query) > 0:
            # Update the specific metadata key in each object in the array
            result = collection.update_one(
                meta_data_array_query,
                {"$set": {f"meta_data.$[].{meta_data_key}": value}},
            )

        else:
            # Update the metadata key directly if meta_data is an object
            result = collection.update_one(
                meta_data_object_query,
                {"$set": {f"meta_data.{meta_data_key}": value}},
            )

        # Check if the document was found and updated
        if result.matched_count > 0:
            logger.info(
                "Successfully updated meta data key '%s' for query_id: '%s' with modified_count: '%s'.",
                meta_data_key,
                query_id_str,
                result.modified_count,
            )

        else:
            logger.warning(
                "Failed to update metadata key '%s' for query ID '%s'.",
                meta_data_key,
                query_id_str,
            )

    except Exception as e:
        message = (
            f"Error while updating query metadata for query_id: {query_id_str} => {e}"
        )
        raise Exception(message) from e


def update_volume(
    rac_collection: Collection, query_collection: Collection, query_id: str
):
    volume = rac_collection.count_documents({})
    query_collection.update_one(
        {"_id": query_id}, {"$set": {"meta_data.rac_volume": volume}}
    )


def generate_sentiment_analysis(langchain_utility, text_prompt: str) -> str:
    """
    Generate sentiment and reasoning for the given text prompt.

    Parameters:
    - text_prompt (str): The input text prompt.

    Returns:
    - str: The sentiment and reasoning for the given text prompt.

    Exception Handling:
    - Exception: Raised if an error occurs while generating sentiment analysis for the text prompt.
    """
    try:
        # Generate content
        model_response = langchain_utility.invoke_llm(text_prompt)

        # Respond with the content if it exists - otherwise return a default string
        try:
            if model_response.content:
                return model_response.content

            # This case occurs when the model does not return any content due to the max_output_tokens limit or AI violations
            return DEFAULT_SENTIMENT

        except Exception:
            return DEFAULT_SENTIMENT

    except Exception as e:
        message = f"Failed to generate sentiment analysis: {e}"
        raise Exception(message) from e


def clean_sentiment(sentiment: str) -> str:
    """
    Cleans and normalizes sentiment text by removing non-alphabetic characters
    except spaces, trimming leading/trailing whitespace, and converting the text
    to title case.

    Parameters:
    - sentiment (str): The raw sentiment string to be cleaned and normalized.

    Returns:
    - str: The cleaned and title-cased sentiment, or default sentiment if the input is NaN or invalid.

    Exception Handling:
    - None
    """
    if pd.isna(sentiment):  # Check for NaN or None values
        return DEFAULT_SENTIMENT

    # Remove non-alphabetic characters except spaces and strip leading/trailing spaces
    cleaned_sentiment = re.sub(r"[^a-zA-Z\s]", "", sentiment).strip()

    # Check if cleaned sentiment is valid, otherwise return the default sentiment
    if cleaned_sentiment not in SENTIMENT_CLASSIFICATION:
        return DEFAULT_SENTIMENT

    return cleaned_sentiment.title()  # Normalize to title case


def trigger_execution_via_http(pod_ip, consumer, payload):
    """
    Triggers execution of a script in a Kubernetes pod via HTTP in a fire-and-forget manner.
    """

    def _trigger():
        try:
            url = f"http://{pod_ip}:8080/run/{consumer}"
            response = requests.post(url, json=payload, timeout=30)
            response.raise_for_status()

        except requests.exceptions.RequestException as e:
            logger.exception(
                "Error occurred while triggering execution via HTTP: %s", e
            )

    # Run the request in a separate thread
    thread = threading.Thread(target=_trigger)
    thread.daemon = (
        True  # This ensures the thread terminates when the main program exits
    )
    thread.start()
