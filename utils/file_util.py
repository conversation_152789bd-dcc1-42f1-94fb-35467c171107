"""
Module to define file utils
"""

import os
import re
from io import BytesIO, TextIOWrapper
import yaml
from google.cloud import storage, exceptions
import pandas as pd
from utils.utilities import FileExtension
from utils.logger import logger
from utils.const import DELIMETERS, FILE_EXTENSIONS
from pathlib import Path
import gcsfs


class FileUtils:
    "Utils for the handling the file"

    def upload_file(self, file, hydration_bucket, bucket_path):
        """
        Function uploads the file with required parameters

        Parameters:
        - file : file object of the file to upload
        - hydration_bucket : hydration bucket name for the organization
        - bucket_path : bucket path of the file to upload

        Returns:
        - None

        Exception handling:
        - Exception: If unable to upload the file.
        """
        try:
            storage_client = storage.Client()
            bucket = storage_client.bucket(hydration_bucket)

            blob = bucket.blob(bucket_path)
          
            if blob.exists():
                blob.delete()
            blob.upload_from_file(file)
        
        except Exception as e:
            # Log the error
            message = f"Failed to upload file to GCS: {e}"
            # Raise the exception to indicate the failure
            raise Exception(message) from e

    def download_file(self, bucket_name, file_path):
        """
        Download a file from a Google Cloud Storage bucket.

        Parameters:
        - bucket_name (str): The name of the Google Cloud Storage bucket.
        - file_path (str): The path of the file within the bucket.

        Returns:
        - str: Contents of the file.

        Exception handling:
        - Exception: If unable to download the file.
        """
        try:
            storage_client = storage.Client()
        
            # Get the bucket
            bucket = storage_client.bucket(bucket_name)
        
            # Get the blob (file)
            blob = bucket.blob(file_path)
        
            # Download the file content as a bytes
            content = blob.download_as_bytes()
        
            # Decode the content
            return content
        
        except Exception as e:
            # Log the error
            message = f"Failed to download file from GCS: {e}"
            # Raise the exception to indicate the failure
            raise Exception(message) from e

    def check_file_extension(self, file_path):
        """
        Checks if the file extension is among the supported types.

        This function examines the file extension of the provided file location (`file_loc`) and determines
        if it is within a predefined list of accepted file extensions. If the file extension is not supported,
        an error is logged, and the function returns False. If the file extension is supported, the function
        returns True. In case of any unexpected error during the process, an error is logged, and the function
        returns False.

        Parameters:
        - file_path (str): The file path of the file to check.
        """
        file_extension = None
        try:
            file_extension = os.path.splitext(file_path)[1].lower()
        
            if file_extension not in FILE_EXTENSIONS:
                logger.error("Unsupported file type")
                return file_extension
        
            return file_extension
        
        except Exception as e:
            logger.error(f"Error while checking file extension: {e}")
            return file_extension

    def parse_file(self, file_bytes, file_extension: str):
        """
        Parse the file based on the extension

        Paramters:
        - file_bytes: bytes of the file
        - file_extension: extension of the file

        Returns:
        - df (DataFrame) : DataFrame of the parsed file

        Exception Handling:
        - Exception: If unable to parse the file.
        """
        try:
            df = None
            file_buffer = BytesIO(file_bytes)
    
            match file_extension:
                case FileExtension.JSON.value:
                    df = pd.read_json(file_buffer)
                case FileExtension.CSV.value:
                    df = pd.read_csv(file_buffer, header=None)
                case FileExtension.TEXT.value:
                    file_content = TextIOWrapper(file_buffer, encoding="utf-8").read()
                    first_line = file_content.splitlines()[0]
                    max_delimiter = max(
                        DELIMETERS,
                        key=lambda d: (first_line.count(d), DELIMETERS.index(d)),
                    )
                    df = pd.read_csv(
                        file_buffer, delimiter=max_delimiter
                    )  # Example delimiter, adjust as needed
                case FileExtension.XLSX.value:
                    df = pd.read_excel(file_buffer, header=None, engine="openpyxl")
                case FileExtension.HTML.value:
                    dfs = pd.read_html(file_buffer, header=0)
                    df = dfs[0]
                case FileExtension.XML.value:
                    df = pd.read_xml(file_buffer)
                case FileExtension.YAML.value:
                    file_content = TextIOWrapper(file_buffer, encoding="utf-8")
                    data = yaml.safe_load(file_content)
                    df = pd.DataFrame(data)
                case _:
                    message = "Unsupported file format"
                    raise ValueError(message)

            if df is not None and not df.empty:
                # Drop completely empty rows
                df.dropna(how="all", inplace=True)
    
                # Drop completely empty columns
                df.dropna(how="all", axis=1, inplace=True)
    
                if (
                    file_extension == FileExtension.CSV.value
                    or file_extension == FileExtension.XLSX.value
                ):
                    df.columns = df.iloc[0]
                    # Drop the first row since it's now the header
                    df = df[1:].reset_index(drop=True)
    
                df.columns = [col if pd.notna(col) else "Unnamed" for col in df.columns]
                df.columns = [re.sub(r"\W+", "_", col).strip("_") for col in df.columns]
                df.columns = [
                    f"Unnamed {i+1}" if "Unnamed" in col else col
                    for i, col in enumerate(df.columns)
                ]

            return df
    
        except Exception as e:
            # Log the error
            message = f"Failed to parse file: {e}"
            # Raise the exception to indicate the failure
            raise Exception(message) from e

    def delete_file(self, bucket_name, blob_name):
        """
        Deletes a file from the specified Cloud Storage bucket.

        Parameters:
        - bucket_name (str): The name of the Cloud Storage bucket.
        - blob_name (str): The name of the blob to delete within the specified bucket.

        Returns:
        None

        Raises:
        google.cloud.exceptions.NotFound: If the specified bucket or blob does not exist.
        google.auth.exceptions.DefaultCredentialsError: If the Google Cloud SDK credentials cannot be found.
        """
        try:
            storage_client = storage.Client()
            # Get the bucket and blob objects
            bucket = storage_client.bucket(bucket_name)
            blob = bucket.blob(blob_name)

            # Attempt to delete the blob
            blob.delete()

            # Log deletion success
            logger.info(f"Blob {blob_name} deleted.")

        except exceptions.NotFound as e:
            # Log and raise a specific exception for Blob or Bucket not found
            message = f"Error deleting blob {blob_name}: {e}"
            raise Exception(message) from e

    def insert_data_to_gcsfs(full_bucket_path, batch_df, format, custom_serializer=None, add_header=False):
            """
            Insert the data to GCP bucket using the batch provided in the required format.
            Parameters:
            - full_bucket_path (str): Full bucket path for the file to be uploaded
            - batch_df (str): The dataframe for the batch data
            - format (str): The format of the file to be uploaded, json or csv
            - custome_serializer (object): The custom serializer to be used for serializing the data
            - add_header (boolean): The bool value if the header needs to be added in this batch or not
            Returns:
            - None
            Exception Handling:
            - None
            """
            fs = gcsfs.GCSFileSystem()

            with fs.open(full_bucket_path,'w') as f:
                if format == FileExtension.JSON.value:
                    batch_df.to_json(f, orient = "records", default_handler = custom_serializer)
                elif format == FileExtension.CSV.value:
                    batch_df.to_csv(f, index=False, header=add_header)



    def parse_file_by_nrows(self,hydration_bucket_name: str, bucket_path: str, nrows=None, offset=0):
        """
        Reads a file in batches from Google Cloud Storage (GCS) while ensuring the correct headers.
        
        Parameters:
            hydration_bucket_name (str): The name of the GCS bucket.
            bucket_path (str): Path to the file in the GCS bucket.
            nrows (int): Number of rows to read per batch.
            offset (int): Number of rows to skip (for batch processing).
        
        Returns:
            DataFrame: Pandas DataFrame containing the batch of data.
        """
        # Construct full GCS path
        bucket_path = f"gs://{hydration_bucket_name}/{bucket_path}"
        print(f"Parsing file: {bucket_path}")

        try:
            df = None
            file_extension = Path(bucket_path).suffix  # Get file extension
            print(f"File extension detected: {file_extension}")

            read_params = {
                "storage_options": {"anon": False}
            }

            if nrows:
                read_params["nrows"] = nrows
            if offset > 0:
                read_params["skiprows"] = offset
            else:
                read_params["header"] = None
            read_params["storage_options"] = {"anon": False}

            # Match file type and read
            match file_extension:
                case ".json":
                    df = pd.read_json(bucket_path, **read_params)
                case ".csv" | ".txt":
                    if offset == 0:
                        df = pd.read_csv(bucket_path, nrows=nrows, storage_options={"anon": False}, header=None)
                        df.columns = pd.read_csv(bucket_path, nrows=0).columns  
                        df = df[1:].reset_index(drop=True)
                    else:
                        header = pd.read_csv(bucket_path, nrows=0).columns  
                        df = pd.read_csv(bucket_path, skiprows=offset, nrows=nrows, header=None, names=header, storage_options={"anon": False})
                case ".xlsx":
                    if offset == 0:
                        df = pd.read_excel(bucket_path, nrows=nrows, storage_options={"anon": False}, header=None)
                        df.columns = pd.read_excel(bucket_path, nrows=0).columns  
                        df = df[1:].reset_index(drop=True)
                    else:
                        header = pd.read_excel(bucket_path, nrows=0).columns
                        df = pd.read_excel(bucket_path, skiprows=offset, nrows=nrows, header=None, names=header, storage_options={"anon": False})
                case ".html":
                    dfs = pd.read_html(bucket_path, header=0)
                    df = dfs[0]
                    if offset > 0:
                        df = df.iloc[offset:]
                    if nrows:
                        df = df.iloc[:nrows]
                case ".xml":
                    df = pd.read_xml(bucket_path)
                    if offset > 0:
                        df = df.iloc[offset:]
                    if nrows:
                        df = df.iloc[:nrows]
                case ".yaml":
                    import yaml
                    with open(bucket_path, "r", encoding="utf-8") as file:
                        data = yaml.safe_load(file)
                        df = pd.DataFrame(data)
                    if offset > 0:
                        df = df.iloc[offset:]
                    if nrows:
                        df = df.iloc[:nrows]
                case _:
                    raise ValueError(f"Unsupported file format: {file_extension}")
            if df is None or df.empty:
                print(f"No data left after skipping {offset} rows. Stopping processing.")
                return None  # No more data to process

            df.columns = [
                col.decode("utf-8") if isinstance(col, bytes) else str(col)
                for col in df.columns
            ]

            original_columns = df.columns.tolist()
            df.columns = [col if pd.notna(col) else "Unnamed" for col in df.columns]
            df.columns = [re.sub(r"\W+", "_", col).strip("_") for col in df.columns]
            df.columns = [f"Unnamed_{i+1}" if "Unnamed" in col else col for i, col in enumerate(df.columns)]
            print(f"Sanitized column names: {original_columns} -> {df.columns.tolist()}")
            return df
        except Exception as e:
            raise Exception(f"Failed to parse file: {e}") from e
