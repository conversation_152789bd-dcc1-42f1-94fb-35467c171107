"""
This module contains constants used in files of query optimizer consumer.
"""

import os

environment = os.environ

CAPABILITIES_DB_NAME = environment.get("CAPABILITIES_DB_NAME")
CAPABILITIES_QUERY_THROTTLER_COLLECTION_NAME = os.getenv(
    "CAPABILITIES_QUERY_THROTTLER_COLLECTION_NAME"
)
QUERY_THROTTLER_TOPIC_ID = os.getenv("QUERY_THROTTLER_TOPIC_ID")
PROJECT_ID = os.environ.get("PROJECT_ID")
MAX_RETRIES = 5


TOPIC_ID = {
    "YT_COMMENTS_HYDRATION": environment.get("YT_COMMENTS_HYDRATION_TOPIC_ID"),
    "YT_COMMENTS_DATA_UNIFIER": environment.get("YT_COMMENTS_DATA_UNIFIER_TOPIC_ID"),
    "FILE_UPLOAD_HYDRATION": environment.get("FILE_UPLOAD_HYDRATION_TOPIC_ID"),
    "FILE_UPLOAD_DATA_UNIFIER": environment.get("FILE_UPLOAD_DATA_UNIFIER_TOPIC_ID"),
    "DATA_ENCAPSULATION": environment.get("DATA_ENCAPSULATION_TOPIC_ID"),
    "COMMON_CLEANSING": environment.get("COMMON_CLEANSING_TOPIC_ID"),
    "SEQUENCE_COORDINATOR": environment.get("SEQUENCE_COORDINATOR_TOPIC_ID"),
    "DISCOVER": environment.get("DISCOVER_TOPIC_ID"),
    "SIMILARITY_VM": environment.get("SIMILARITY_VM_TOPIC_ID"),
    "SIMILARITY": environment.get("SIMILARITY_TOPIC_ID"),
    "SUMMARIZATION": environment.get("SUMMARIZATION_TOPIC_ID"),
    "SUMMARIZATION_VM": environment.get("SUMMARIZATION_VM_TOPIC_ID"),
    "SENTIMENT_MULTI": environment.get("SENTIMENT_MULTI_TOPIC_ID"),
    "SENTIMENT": environment.get("SENTIMENT_TOPIC_ID"),
    "QUERY_OPTIMIZER": environment.get("QUERY_OPTIMIZER_TOPIC_ID"),
    "DATA_TRANSFORMER": environment.get("DATA_TRANSFORMER_TOPIC_ID"),
    "APPEND": environment.get("APPEND_TOPIC_ID"),
}
