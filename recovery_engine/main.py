"""
This module processes error logs from a MongoDB database and publishes messages
to Google Cloud Pub/Sub.

It includes the following main functionalities:
1. MongoDB Client Initialization: Connects to a MongoDB database using environment
    variables for the URI and retrieves the error logs collection.
2. Error Log Processing: Queries the error logs based on status and trigger time.
    For each log, checks the retry count and updates the status accordingly.
    If the retry count exceeds a maximum threshold, the log status is set to "FAILED".
    Otherwise, it is set to "COMPLETED".
3. Pub/Sub Message Publishing: If the status is "COMPLETED", the log's payload is published
    to the corresponding Google Cloud Pub/Sub topic.
4. Bulk Update: Updates the MongoDB collection with the modified status of the error logs in bulk.
"""

import os
import json
from datetime import datetime
import functions_framework
import pymongo
from google.cloud import pubsub_v1
from google.oauth2 import service_account
from const import TOPIC_ID, CAPABILITIES_DB_NAME
from pymongo.operations import UpdateOne
from utils.logger import logger
from bson import ObjectId
from utils.pubsub_publisher import publish_pubsub_message
from const import (
    MAX_RETRIES,
    TOPIC_ID,
    CAPABILITIES_DB_NAME,
    CAPABILITIES_QUERY_THROTTLER_COLLECTION_NAME,
    PROJECT_ID,
    QUERY_THROTTLER_TOPIC_ID,
)
from utils.utilities import QueryStatus


# Initialize MongoDB and Pub/Sub clients
def initialize_clients():
    """Initialize MongoDB and Pub/Sub clients"""

    platform_mongo_uri = os.getenv("MONGO_URI")
    mongo_client = pymongo.MongoClient(platform_mongo_uri)
    db = mongo_client.get_database(CAPABILITIES_DB_NAME)
    error_collection = db["error_logs"]
    query_throttler_collection = db[CAPABILITIES_QUERY_THROTTLER_COLLECTION_NAME]

    return mongo_client, error_collection, query_throttler_collection


# Cloud Function entry point
def process_errors():
    MAX_RETRIES = 5
    mongo_client, error_collection, query_throttler_collection = (
            initialize_clients()
        )

    # Query for error logs
    error_logs = list(
        error_collection.find(
            {
                "status": {"$nin": ["COMPLETED", "FAILED"]},
                "trigger_time": {"$lt": datetime.now()},
            }
        )
    )  # Adjust query as needed

    print(error_logs)
    updated_records = []
    for log in error_logs:
        # Publish message to Pub/Sub
        project_id = os.getenv("GCP_PROJECT_ID")
        message_data = json.loads(log["payload"])
        consumer_type = log.get("consumer_type")
        payload = log.get("payload")
        query_id_str = message_data["query_id"]
        trigger_count = error_collection.count_documents(
            {
                "query_id": ObjectId(message_data["query_id"]),
                "organization_id": ObjectId(message_data["organization_id"]),
                "consumer_type": consumer_type,
            }
        )
        
        logger.info(f"trigger count for query id {query_id_str} is {trigger_count} ")
        
        if trigger_count >= MAX_RETRIES:
            logger.info(
                    f"Query with id: {query_id_str} has reached maximum retries.")

            updated_records.append(
                UpdateOne({"_id": log["_id"]}, {"$set": {"status": "FAILED"}})
            )
            # Query is failed from recovery engine's side so call query throttler to delete POD
            query_throttler_collection.update_one(
                {"query_id": ObjectId(query_id_str)},
                {"$set": {"query_status": QueryStatus.FAILED.value}},
            )
            publish_pubsub_message(project_id, QUERY_THROTTLER_TOPIC_ID, message_data)

        else:
            logger.info(
                    f"Query with id: {query_id_str} is being retried."
            )

            updated_records.append(
                UpdateOne({"_id": log["_id"]}, {"$set": {"status": "COMPLETED"}})
            )

            pubsub_topic = TOPIC_ID[consumer_type]
            publish_pubsub_message(project_id, pubsub_topic, message_data)
    
    logger.info("%s", updated_records)
    error_collection.bulk_write(updated_records)

    return {"message": "Error logs processed and messages published."}, 200


@functions_framework.http
def main(request):
    """HTTP Cloud Function.
    Args:
        request (flask.Request): The request object.
        <https://flask.palletsprojects.com/en/1.1.x/api/#incoming-request-data>
    Returns:
        The response text, or any set of values that can be turned into a
        Response object using `make_response`
        <https://flask.palletsprojects.com/en/1.1.x/api/#flask.make_response>.
    """
    request_json = request.get_json(silent=True)
    request_args = request.args
    status = process_errors()

    return status
