#!/bin/bash

# Function to retrieve secrets from Secret Manager
retrieve_secret() {
    : """
    Retrieve the latest version of a secret from Google Cloud Secret Manager.
    
    This function securely fetches the latest version of a specified secret from 
    Google Cloud Secret Manager, ensuring sensitive credentials are not hardcoded.
    
    Args:
        $1 (string): The name of the secret to retrieve.
    
    Returns:
        string: The secret value retrieved from Secret Manager.
    """

    gcloud secrets versions access latest --secret="$1"
}

GCP_PROJECT=$(retrieve_secret "k8s-gcp-project")
REGION=$(retrieve_secret "k8s-region")
TAG=$(retrieve_secret "k8s-tag")
_ACCESS_TOKEN=$(retrieve_secret "github-secret")
NAMESPACE=$(retrieve_secret "k8s-namespace")
CLUSTER_NAME=$(retrieve_secret "k8s-cluster-name")

# Function to authenticate with GKE if needed
authenticate_gke() {
    : """
    Authenticate with Google Kubernetes Engine (GKE) if not already authenticated.
    
    This function checks if 'kubectl' can access the cluster nodes. If not, it fetches
    credentials from GCP using 'gcloud container clusters get-credentials'.
    """

    if ! kubectl get nodes > /dev/null 2>&1; then
        gcloud container clusters get-credentials $CLUSTER_NAME --region $REGION --project $GCP_PROJECT
    else
        echo "Already authenticated to GKE cluster."
    fi
}

git config --global url."https://$<EMAIL>/".insteadOf "https://github.com/"
git clone https://github.com/Tellagence-Capabilities/Capabilities.git
git fetch --unshallow

# Define function folders for Kubernetes deployment
declare -A folders=(
    ["vm-consumers"]="vm_consumers"
)

# Build and push Docker image to Container Registry
build_and_push_image() {
    : """
    Build and push a Docker image for the specified Pod to Google Container Registry.
    
    Args:
        $1 (string): The name of the Pod.
    """

    local name=$1
    local path="${folders[$name]}"
    local image_name="gcr.io/$GCP_PROJECT/${name}:$TAG"
 
    gcloud secrets versions access latest --secret="${name}-k8s-env" > "$path/.env"
    
    echo "Building and pushing Docker image for $name..."
    docker build -t "$image_name" "$path"
    docker push "$image_name"
}

# Deploy Pod on Kubernetes with updated image
deploy_k8s_pod() {
    : """
    Deploy a Kubernetes pod.
    
    This function retrieves environment variables, updates the ConfigMap,
    builds and pushes the Docker image, and restarts the deployment.
    
    Args:
        $1 (string): The name of the Pod.
        $2 (string): The path to the Pod's directory.
    """

    local name=$1
    local path=$2
    local yaml_file="$path/pod-definition.yaml"

    # Navigate to the function directory safely
    if [ -d "$path" ]; then 
        cd "$path" || exit 1
        pwd
    else 
        echo "Error: Directory $path not found" 
        exit 1
    fi

    cp -r "$(git rev-parse --show-toplevel)/utils" .

    gcloud secrets versions access latest --secret="service-account" > key.json
    gcloud secrets versions access latest --secret="pod-definition-yaml" > pod-definition.yaml

    cd - || exit 1
    
    build_and_push_image "$name"

    # Check if the Pod exists
    if kubectl get pod "$name" -n "$NAMESPACE" > /dev/null 2>&1; then
        echo "Pod $name exists. Deleting it..."
        kubectl delete pod "$name" -n "$NAMESPACE" --ignore-not-found=true
        echo "Waiting for the pod to terminate..."
        sleep 20
    else
        echo "Pod $name does not exist. Creating a new pod..."
    fi

    # Apply YAML to create/recreate the Pod
    echo "Applying YAML for $name..."
    kubectl apply -f "$yaml_file" -n "$NAMESPACE"

    echo "Waiting for 30 seconds to allow the pod to start..."
    sleep 30
}

# Check if utils folder has changed 
utils_changed=false
if ! git diff --quiet HEAD~1 HEAD -- "utils"; then
    utils_changed=true
fi

# Authenticate to GKE before deployment
authenticate_gke

# Deploy Pods only if changes are detected
for name in "${!folders[@]}"; do
    folder_changed=false
    if ! git diff --quiet HEAD~1 HEAD -- "${folders[$name]}" || [ "$utils_changed" = true ]; then
        folder_changed=true
    fi
    
    if [ "$folder_changed" = true ]; then
        deploy_k8s_pod "$name" "${folders[$name]}"
    else
        echo "Skipping deployment for $name, no changes detected."
    fi
done