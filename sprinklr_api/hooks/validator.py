"""
This module provides the ValidationHook class for validating data 
batches in Google Cloud Storage and MongoDB."""

from datetime import datetime
from typing import Dict
from pymongo.collection import Collection
from google.cloud import storage
from sprinklr_api.const import PAGE_SIZE
from utils.logger import logger


class ValidationHook:
    """
    A class to validate data batches and their properties in
    Google Cloud Storage and MongoDB.
    """

    def __init__(self):
        """
        Constructor that initialize batch_range, query and set it to None.
        - batch_range (str) : create with start and end of the batch
        """
        self.batch_range = None
        self.query_id = None

    def set_payload_info(self, payload: Dict) -> None:
        """
        Sets the params that we get from the payload.

        Paramters:
        - payload (dict) : payload that we get from the pubsub
        """
        self.batch_range = (
            f"{payload['batch']['start_time']}-{payload['batch']['end_time']}"
        )
        self.query_id = payload["query_id"]

    def _list_objects_in_directory(self, bucket_name: str, prefix: str) -> Dict:
        """
        Lists objects in the specified directory within a Google Cloud Storage bucket.

        Parameters:
        - bucket_name (str): The name of the Google Cloud Storage bucket.
        - prefix (str): The prefix or path within the bucket to filter objects.

        Returns:
        - dict: A dictionary mapping batch ranges to lists of page names.
            Each batch range represents pages that are sorted based on
            their page numbers.

        Raises:
        - Exception: If there is an error while listing objects in the bucket.
        """
        try:
            storage_client = storage.Client()
            # List objects in the specified directory
            blobs = storage_client.bucket(bucket_name).list_blobs(prefix=prefix)
            batch_pages = {}
            for blob in blobs:
                if blob.name.count("/") >= 5:
                    batch_range, page_name = blob.name.split("/")[4:6]

                    if batch_range not in batch_pages:
                        batch_pages[batch_range] = [page_name]
                    else:
                        batch_pages[batch_range].append(page_name)

            # Sort the pages within each batch based on the page number
            for batch_range, pages in batch_pages.items():
                batch_pages[batch_range] = sorted(
                    pages, key=lambda x: int("".join(filter(str.isdigit, x)))
                )

            return batch_pages
        except Exception as e:
            logger.error("An error occurred while listing objects: %s", e)
            return {}

    def validate_pages(
        self, bucket_name: str, bucket_path: str, record_count: int, report_name: str
    ) -> True | False | None:
        """
        This method validate pages within the bucket by the record count, based
        on particular report_name.

        Parameters:
        - bucket_name (str): The name of the Google Cloud Storage bucket.
        - bucket_path (str): Path of the GCP bucket.
        - record_count (int) : Record count for the batch.
        - report_name (str): Name of the report in sprinklr payload.

        Return:
        - True | False | None : True when pages count matches with the expected count
            else False and None when the error occurred.
        """
        try:
            pages_dict = self._list_objects_in_directory(bucket_name, bucket_path)
            batch_range = self.batch_range
            query_id = self.query_id
            filtered_pages = list(
                filter(lambda x: report_name in x, pages_dict[batch_range])
            )

            page_count = len(filtered_pages)
            expected_page_count = record_count // int(PAGE_SIZE) + int(
                record_count % int(PAGE_SIZE) > 0
            )

            logger.info(
                "Record count for the batch_range '%s', query_id '%s' and report_name '%s' is %d",
                batch_range,
                query_id,
                report_name,
                record_count,
            )

            logger.info(
                "Page count for the batch_range '%s', query_id '%s' and report_name '%s' is %d",
                batch_range,
                query_id,
                report_name,
                page_count,
            )

            logger.info(
                "Expected page count for the batch_range '%s', query_id '%s' and report_name '%s' is %d",
                batch_range,
                query_id,
                report_name,
                expected_page_count,
            )

            if page_count == expected_page_count:
                logger.info(
                    "Validation passed: Page count matched for the batch_range '%s', query_id '%s' and report_name '%s'",
                    batch_range,
                    query_id,
                    report_name,
                )
                return True

            if page_count > expected_page_count:
                logger.warning(
                    "Validation failed: Page count didn't matched for the batch_range '%s', query_id '%s', report_name '%s' and is greater than expected count",
                    batch_range,
                    query_id,
                    report_name,
                )
            else:
                logger.warning(
                    "Validation failed: Page count didn't matched for the batch_range '%s', query_id '%s', report_name '%s' and is less than expected count",
                    batch_range,
                    query_id,
                    report_name,
                )
            return False

        except Exception as e:
            logger.error(
                "An error occurred while validating the pages for the batch_range '%s', query_id '%s' and report_name '%s' : %s",
                batch_range,
                query_id,
                report_name,
                e,
            )

    def validate_number_of_batches(
        self, collection: Collection, query_id: str, number_of_batches: int
    ) -> bool:
        """
        Validate the number of batches inserted for a query_id in the batch collection.

        Parameters:
        - collection: The MongoDB batch collection.
        - query_id: The query_id to validate.
        - number_of_batches: The expected number of batches.

        Returns:
        - bool: True if the number of batches matches number_of_batches, False otherwise.
        """
        try:
            # Count the number of records for the given query_id
            num_records = collection.count_documents({"query_id": query_id})

            # Compare the number of records with the expected number of batches
            if num_records == number_of_batches:
                logger.info(
                    "Validation passed: The number of batches for query_id %s matches the expected number of batches (%d)",
                    query_id,
                    number_of_batches,
                )
                return True

            logger.info(
                "Validation failed: The number of batches for query_id %s doesn't match the expected number of batches (%d). Actual number of batches: %d",
                query_id,
                number_of_batches,
                num_records,
            )
            return False

        except Exception as e:
            logger.error(
                "An error occurred while validating number of batches: %s", str(e)
            )
            return False

    def validate_min_max_date_range(
        self,
        collection: Collection,
        query_id: str,
        min_start_time: datetime,
        max_end_time: datetime,
    ) -> bool:
        """
        Validate the minimum start time and maximum end time for a query_id in the batch collection for inserted batches.

        Parameters:
        - collection: The MongoDB batch collection.
        - query_id: The query_id to validate.
        - min_start_time: The minimum start time in epoch.
        - max_end_time: The maximum end time in epoch.

        Returns:
        - bool: True if the min start time is equal to the min_start_time and
                max end time is equal to the max_end_time else false, False otherwise.
        """
        try:
            # Aggregation pipeline to find the min_start_time and max_end_time for the given query_id
            pipeline = [
                {"$match": {"query_id": query_id}},
                {
                    "$group": {
                        "_id": None,
                        "min_start_time": {"$min": "$start_time"},
                        "max_end_time": {"$max": "$end_time"},
                    }
                },
            ]

            result = list(collection.aggregate(pipeline))
            if result:
                doc = result[0]  # Get the first document from the result
                min_start_time_db = doc.get("min_start_time")
                max_end_time_db = doc.get("max_end_time")

                # Compare the min_start_time_db and max_end_time_db with the provided values
                if (
                    min_start_time_db == min_start_time
                    and max_end_time_db == max_end_time
                ):
                    logger.info(
                        "Validation passed: The batches date range for query_id %s exactly matches the specified range. Min start time: %d, Max end time: %d",
                        query_id,
                        min_start_time_db,
                        max_end_time_db,
                    )
                    return True

                logger.info(
                    "Validation failed: The batches date range for query_id %s does not exactly match the specified range. Expected min start time: %d, Expected max end time: %d, Actual min start time: %d, Actual max end time: %d",
                    query_id,
                    min_start_time,
                    max_end_time,
                    min_start_time_db,
                    max_end_time_db,
                )
                return False

            # If no documents found for the query_id
            logger.info(
                "Validation failed: No data found for query_id %s in the batch collection.",
                query_id,
            )
            return False

        except Exception as e:
            logger.error(
                "An error occurred while validating the batches date range: %s", str(e)
            )
            return False

    def validate_batch_count(self, batch_count: int, report_dataframes: Dict) -> None:
        """
        Validate the number of records in report dataframes against the provided batch count.

        Parameters:
        - batch_count (int): The expected number of records in the batch.
        - report_dataframes (dict): A dictionary where keys are report names and
            values are dataframes.
        """
        try:
            batch_range = self.batch_range
            query_id = self.query_id
            total_records = sum(len(df) for df in report_dataframes.values())

            if total_records == batch_count:
                logger.info(
                    "Validation passed: Number of records in report dataframes (%d) matches batch count (%d) for batch_range '%s' and query_id '%s'.",
                    total_records,
                    batch_count,
                    batch_range,
                    query_id,
                )

            else:
                logger.info(
                    "Validation failed: Number of records in report dataframes (%d) does not match batch count (%d) for batch_range '%s' and query_id '%s'.",
                    total_records,
                    batch_count,
                    batch_range,
                    query_id,
                )

        except Exception as e:
            logger.error(
                "An error occurred while validating the batch count: %s", str(e)
            )
