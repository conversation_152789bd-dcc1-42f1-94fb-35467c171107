"""
This module contains constants used in files of sprinklr-api consumer.
"""

import os

TIMEOUT = 300
PROJECT_ID = os.getenv("PROJECT_ID")
CLEANSING_TOPIC_ID = os.getenv("CLEANSING_TOPIC_ID")
PAGE_SIZE = os.getenv("PAGE_SIZE")
QUERY_COLLECTION_NAME = os.getenv("QUERY_COLLECTION_NAME")

PROJECT_ID = os.getenv("PROJECT_ID")
HYDRATION_TOPIC_ID = os.getenv("HYDRATION_TOPIC_ID")

RELATIVE_WEEK = os.getenv("RELATIVE_WEEK")
AUTH_TOKEN_URL = os.getenv("AUTH_TOKEN_URL")
TIME_DELAY = os.getenv("TIME_DELAY")

QUERY_PROJECTION_ATTRIBUTES = {
    "meta_data.BATCH_COLLECTION_NAME": 1,
    "meta_data.CLIENT_ID": 1,
    "meta_data.CLIENT_SECRET": 1,
    "meta_data.GRANT_TYPE": 1,
    "meta_data.META_TYPE": 1,
    "meta_data.SPRINKLR_WIDGET_ID": 1,
    "meta_data.START_DATETIME": 1,
    "meta_data.END_DATETIME": 1,
    "source.data_source_name": 1,
}

SPRINKLR_API_END_POINT = os.getenv("SPRINKLR_API_END_POINT")

QUERY_COLLECTION_NAME = os.getenv("QUERY_COLLECTION_NAME")

PROJECT_ID = os.getenv("PROJECT_ID")
HYDRATION_TOPIC_ID = os.getenv("HYDRATION_TOPIC_ID")
RAC_TOPIC_ID = os.getenv("RAC_TOPIC_ID")

PAGE_SIZE = os.getenv("PAGE_SIZE")
MAX_RETRIES = os.getenv("MAX_RETRIES")
BASE_DELAY = os.getenv("BASE_DELAY")
BASE_LOOPBACK_THRESHOLD = os.getenv("BASE_LOOPBACK_THRESHOLD")

QUERY_PROJECTION_ATTRIBUTES = {
    "meta_data.BATCH_COLLECTION_NAME": 1,
    "meta_data.PAYLOAD": 1,
    "user_id": 1,
}


DATA_UNIFIER_TOPIC_ID = os.getenv("DATA_UNIFIER_TOPIC_ID")

QUERY_PROJECTION_ATTRIBUTES = {
    "meta_data.BATCH_COLLECTION_NAME": 1,
    "name": 1,
    "user_id": 1,
}
RAC_COLLECTION_NAME_METADATA_KEY = "RAC_COLLECTION_NAME"
