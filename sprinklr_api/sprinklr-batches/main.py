"""
Module that creates batches week-wise and insert them into the batch collection,
then publish pub-sub messages to 'sprinklr-hydration' consumer.
"""

from typing import Dict, List
import base64
import json
import time
from datetime import datetime, timedelta
from bson import ObjectId
from pymongo.collection import Collection
import requests
import functions_framework
from cloudevents.http import CloudEvent
import pandas as pd
import pytz
from sprinklr_api.hooks.validator import Valida<PERSON>Hook
from sprinklr_api.const import (
    RELATIVE_WEEK,
    AUTH_TOKEN_URL,
    QUERY_COLLECTION_NAME,
    QUERY_PROJECTION_ATTRIBUTES,
    PROJECT_ID,
    HYDRATION_TOPIC_ID,
    TIME_DELAY,
    TIMEOUT,
)
from utils.utilities import (
    BatchStatus,
    DiagnosticActionType,
    DiagnosticStatus,
    MetaContainer,
    OrganizationAccountInfo,
)
from utils.common_utils import get_query_config
from utils.mongo_db import get_mongodb_client, get_mongodb_collection, get_mongodb_db
from utils.pubsub_publisher import (
    publish_pubsub_message,
)
from utils.logger import logger


def create_batches(
    start_datetime: datetime,
    end_datetime: datetime,
    query_id: str,
    widget_id: str,
    is_single_batch: bool = False,
) -> List[Dict]:
    """
    This method creates week-wise batches based on the date range provided
    or creates a single batch based on is_single_batch flag.

    Parameters:
    - start_datetime: start date time range to create batches.
    - end_datetime: end date time range to create batches.
    - query_id: The query_id of a query collection document.
    - widget_id: The widget_id of the sprinklr widget.
    - is_single_batch: Whether to create a single batch or not.

    Returns:
    - week_range (List[dict]) : batches of week range, contains query_id, start_time,
        end_time, status, widget_id, and batch_count.
    """
    week = timedelta(weeks=int(RELATIVE_WEEK))
    microseconds = timedelta(microseconds=1)

    batches = []
    start = start_datetime
    end = end_datetime

    batch_template = {
        "query_id": query_id,
        "start_time": int(start.timestamp() * 1000),
        "end_time": int(end.timestamp() * 1000),
        "hydration_status": BatchStatus.INACTIVE.value,
        "rac_status": BatchStatus.INACTIVE.value,
        "data_unifier_status": BatchStatus.INACTIVE.value,
        "cleansing_status": BatchStatus.INACTIVE.value,
        "widget_id": widget_id,
        "batch_count": 0,
    }

    if is_single_batch:
        batches.append(batch_template)

    else:
        while True:
            end = start + week - microseconds
            end = min(end, end_datetime)

            current_batch = batch_template.copy()
            current_batch["start_time"] = int(start.timestamp() * 1000)
            current_batch["end_time"] = int(end.timestamp() * 1000)

            batches.insert(
                0,
                current_batch,
            )
            start += week

            if start > end_datetime:
                break

    return batches


def convert_objectid_to_string(val) -> str:
    """
    Convert ObjectId to string if the input is an ObjectId.

    Parameters:
    - value: The value to be converted.

    Returns:
    - str: If the input is an ObjectId, it returns the string representation of the ObjectId.
           Otherwise, it returns the input value unchanged.
    """
    return str(val) if isinstance(val, ObjectId) else val


def convert_dict_to_iso_lower(data: Dict) -> Dict:
    """
    Recursively convert datetime objects to ISO format and lowercase keys in a dictionary.

    Parameters:
    - data (dict): The input dictionary.

    Returns:
    - dict: A new dictionary with datetime objects converted to ISO format and keys lowercase.
    """
    return {
        key.lower(): (
            convert_dict_to_iso_lower(value)
            if isinstance(value, dict)
            else value.isoformat() if isinstance(value, datetime) else value
        )
        for key, value in data.items()
    }


def insert_batches(
    collection: Collection, batches_df: pd.DataFrame, query_id: str
) -> List:
    """
    Inserts batches DataFrame into a MongoDB collection and returns the inserted data
    with batch_id's generated by MongoDB for a particular query_id.

    Parameters:
    - collection (pymongo.collection.Collection): The MongoDB batch collection to insert data into.
    - batches_df (pandas.DataFrame): DataFrame containing the batches to insert.
    - query_id: The query_id of a query collection document.

    Returns:
    - list: List of dictionaries representing the inserted batches with _id's generated by MongoDB.
    """
    try:
        batches_data = batches_df.to_dict(orient="records")
        collection.insert_many(batches_data)
        logger.info(
            "Successfully inserted batches_df for query_id: '%s' in '%s' collection.",
            query_id,
            collection.name,
        )

        # Fetch inserted data with generated _id's using query_id
        inserted_batches_df = pd.DataFrame(
            list(collection.find({"query_id": query_id}))
        )

        columns_to_convert = ["_id", "query_id"]

        inserted_batches_df[columns_to_convert] = inserted_batches_df[
            columns_to_convert
        ].apply(lambda x: x.map(convert_objectid_to_string))

        inserted_batches = json.loads(inserted_batches_df.to_json(orient="records"))

        return inserted_batches

    except Exception as e:
        message = f"Error inserting batches_df for query_id: {query_id} in {collection.name} collection => {e}"
        meta_container = MetaContainer()
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.FAILED.value,
            message,
        )
        return None


def update_access_token(
    query_collection: Collection, token_response: Dict, query_id: str
) -> None:
    """
    Update the access_token and refresh_token fields within the meta_data
    of query collection based on the provided query_id.

    Parameters:
    - query_collection (pymongo.collection.Collection): The MongoDB query collection to update.
    - token_response (dict): Dictionary containing the new access_token and refresh_token.
    - query_id: The query_id of the document to update.
    """
    try:
        if "access_token" in token_response and "refresh_token" in token_response:
            result = query_collection.update_one(
                {"_id": query_id},
                {
                    "$set": {
                        "meta_data.$[].ACCESS_TOKEN": token_response["access_token"],
                        "meta_data.$[].REFRESH_TOKEN": token_response["refresh_token"],
                    }
                },
            )

            logger.info(
                "Successfully updated token fields for query_id: '%s' in '%s' collection with modified_count: '%s'.",
                query_id,
                query_collection.name,
                result.modified_count,
            )

        else:
            logger.warning(
                "Access token or refresh token is not available in token_response"
            )

    except Exception as e:
        message = f"An error occurred while updating the token fields for query_id {query_id}: {str(e)}"
        meta_container = MetaContainer()
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.FAILED.value,
            message,
        )


def get_token_from_sprinklr_api(
    client_id: str, client_secret: str, grant_type: str
) -> str:
    """
    This function gets token using sprinklr auth endpoint.

    Returns:
    - Access token for sprinklr API

    Raises:
    - Exception: If an error occurs while getting Sprinklr API token.
    """
    try:
        payload = f"client_id={client_id}&client_secret={client_secret}&grant_type={grant_type}"
        headers = {"Content-Type": "application/x-www-form-urlencoded"}
        response = requests.post(
            AUTH_TOKEN_URL, headers=headers, data=payload, timeout=TIMEOUT
        )
        return response.json()

    except Exception as e:
        raise Exception(f"Error getting Sprinklr API token: {str(e)}") from e


def handle_access_token(
    query_collection: Collection, query_meta_data: Dict, query_id: str
) -> str:
    """
    This function fetches and updates the access token.

    Parameters:
    - query_collection (pymongo.collection.Collection): The MongoDB query collection.
    - query_meta_data (dict): query_meta_data config dict.
    - query_id: The query_id of the document to update.

    Returns:
    - access_token (str): access token for the sprinklr API
    """
    client_id, client_secret, grant_type = (
        query_meta_data.pop("CLIENT_ID"),
        query_meta_data.pop("CLIENT_SECRET"),
        query_meta_data.pop("GRANT_TYPE"),
    )

    token_response = get_token_from_sprinklr_api(client_id, client_secret, grant_type)
    update_access_token(query_collection, token_response, query_id)

    return token_response["access_token"]


@functions_framework.cloud_event
def main(cloud_event: CloudEvent):
    """
    Cloud Function triggered by a CloudEvent.

    - It creates batches week-wise and insert them into the batch collection,
      then publish pub-sub messages to 'sprinklr-hydration' consumer.
        - Parse the pub-sub payload.
        - Get the capabilities and capabilities_config DB's.
        - Get query configuration data such as batch_collection_name, client_id, client_secret,
          grant_type, meta_type, sprinklr_widget_id, start_time, and end_time.
        - Create batches of week range, contains query_id, start_time, end_time,
            status, widget_id, and batch_count.batch id's.
        - Insert batches in batch collection.
        - Iterate over batches.
        - Publish pubsub message to 'sprinklr-hydration' consumer batch-wise.

    Returns:
    - {"Status":200}: For successfully execution.
    """
    try:
        meta_container = MetaContainer()
        validation_hook = ValidationHook()

        payload = json.loads(
            base64.b64decode(cloud_event.data["message"]["data"]).decode("utf-8")
        )
        logger.info("Message received successfully: %s", payload)
        meta_container.set_payload_info(payload)

        organization_id_str = payload["organization_id"]
        query_id_str = payload["query_id"]

        query_id = ObjectId(query_id_str)

        org_account_info = OrganizationAccountInfo(organization_id_str)
        mongodb_url = org_account_info.mongodb_url
        organization_db_name = org_account_info.organization_db_name

        mongo_db_client = get_mongodb_client(mongodb_url)
        organization_db = get_mongodb_db(mongo_db_client, organization_db_name)
        query_collection = get_mongodb_collection(
            organization_db, QUERY_COLLECTION_NAME
        )

        message = f"Capabilities sprinklr batches script started successfully for query_id: {query_id_str}"

        query_config = get_query_config(
            query_collection, query_id_str, QUERY_PROJECTION_ATTRIBUTES
        )

        if not query_config:
            logger.warning("No configuration found for query_id: '%s'", query_id_str)
            return

        data_source_meta_data = {
            "data_source_name": query_config["source"]["data_source_name"],
        }

        meta_container.set_meta_data(data_source_meta_data)
        meta_container.send_diagnostic(
            DiagnosticActionType.INSERT.value, DiagnosticStatus.INACTIVE.value, message
        )

        query_meta_data = query_config["meta_data"][0]
        is_single_batch = len(query_config["meta_data"]) > 1

        start_datetime = query_meta_data["START_DATETIME"].replace(tzinfo=pytz.utc)
        end_datetime = query_meta_data["END_DATETIME"].replace(tzinfo=pytz.utc)

        sprinklr_widget_id = query_meta_data["SPRINKLR_WIDGET_ID"]
        batch_collection_name = query_meta_data["BATCH_COLLECTION_NAME"]
        batch_collection = get_mongodb_collection(
            organization_db, batch_collection_name
        )
        batches = create_batches(
            start_datetime, end_datetime, query_id, sprinklr_widget_id, is_single_batch
        )

        batches_df = pd.DataFrame(batches)
        token = handle_access_token(query_collection, query_meta_data, query_id)
        batches_df["widget_id"] = sprinklr_widget_id
        batches_with_ids = insert_batches(batch_collection, batches_df, query_id)

        if batches_with_ids is not None:
            # Validation hook
            validation_hook.validate_number_of_batches(
                batch_collection,
                query_id,
                len(batches_with_ids),
            )

            validation_hook.validate_min_max_date_range(
                batch_collection,
                query_id,
                int(start_datetime.timestamp() * 1000),
                int(end_datetime.timestamp() * 1000),
            )

            for batch in batches_with_ids:
                payload = {
                    **payload,
                    "access_token": token,
                    "batch": batch,
                    "organization_id": organization_id_str,
                    "query_id": query_id_str,
                    "widget": convert_dict_to_iso_lower(query_meta_data),
                }

                logger.info(
                    "Message payload for transmission '%s'",
                    payload,
                )

                publish_pubsub_message(PROJECT_ID, HYDRATION_TOPIC_ID, payload)
                time.sleep(int(TIME_DELAY))

            message = f"Capabilities Sprinklr batches script completed successfully for query_id: {query_id_str}"
            meta_container.send_diagnostic(
                DiagnosticActionType.UPDATE.value,
                DiagnosticStatus.COMPLETED.value,
                message,
            )

        mongo_db_client.close()
        return {"Status": 200}

    except Exception as e:
        message = f"An error occurred during capabilities sprinklr batches: {e}"
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.FAILED.value,
            message,
            str(type(e)),
        )
