"""
This module have main function that processes data from a CloudEvent payload, performs several 
operations including retrieving configuration from a query collection, fetching data from Google    
Cloud Storage, and inserting the processed data into rac collection of MongoDB database.
"""

import json
import base64
from collections import defaultdict
from bson import ObjectId
import numpy as np
import pandas as pd
import functions_framework
from cloudevents.http import CloudEvent
from google.cloud import storage
from pymongo.collection import Collection
from sprinklr_api.hooks.validator import ValidationHook
from sprinklr_api.const import (
    QUERY_COLLECTION_NAME,
    QUERY_PROJECTION_ATTRIBUTES,
    RAC_COLLECTION_NAME_METADATA_KEY,
    PROJECT_ID,
    DATA_UNIFIER_TOPIC_ID,
)
from utils.pubsub_publisher import publish_pubsub_message
from utils.utilities import (
    BatchStatus,
    BatchStatusColumn,
    DiagnosticActionType,
    DiagnosticStatus,
    MetaContainer,
    OrganizationAccountInfo,
)
from utils.common_utils import (
    check_query_status,
    get_query_config,
    toggle_batch_status,
    generate_collection_name,
    update_query_metadata,
)
from utils.mongo_db import get_mongodb_client, get_mongodb_collection, get_mongodb_db
from utils.logger import logger


def list_objects_in_directory(bucket_name: str, prefix: str):
    """
    Lists objects in the specified directory within a Google Cloud Storage bucket.

    Parameters:
    - bucket_name (str): The name of the Google Cloud Storage bucket.
    - prefix (str): The prefix or path within the bucket to filter objects.

    Returns:
    - dict: A dictionary mapping batch ranges to lists of page names. Each batch range represents
            pages that are sorted based on their page numbers.

    Raises:
    - Exception: If there is an error while listing objects in the bucket.
    """
    try:
        meta_container = MetaContainer()
        storage_client = storage.Client()

        # List objects in the specified directory
        blobs = storage_client.bucket(bucket_name).list_blobs(prefix=prefix)
        batch_pages_mapping = {}

        for blob in blobs:
            if blob.name.count("/") >= 5:
                batch_range, page_name = blob.name.split("/")[4:6]

                if batch_range not in batch_pages_mapping:
                    batch_pages_mapping[batch_range] = [page_name]
                else:
                    batch_pages_mapping[batch_range].append(page_name)

        # Sort the pages within each batch based on the page number
        for batch_range, pages in batch_pages_mapping.items():
            batch_pages_mapping[batch_range] = sorted(
                pages, key=lambda x: int("".join(filter(str.isdigit, x)))
            )

        return batch_pages_mapping

    except Exception as e:
        message = f"An error occurred while listing objects: {e}"
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.FAILED.value,
            message,
        )

        return {}


def read_and_parse_data(
    bucket_name: str, prefix_path: str, batch_range: str, page_name: str
) -> dict:
    """
    Reads and parses JSON data from a specific page within a Google Cloud Storage bucket.

    Parameters:
    - bucket_name (str): The name of the Google Cloud Storage bucket.
    - prefix_path (str): The prefix path with organization_id, user_id, and query_id.
    - batch_range (str): The batch range to identify a subset of data.
    - page_name (str): The name of the specific page within the specified batch range.

    Returns:
    - dict : The parsed JSON data from the specified blob.
    """

    storage_client = storage.Client()
    blob_name = f"{prefix_path}/{batch_range}/{page_name}"
    blob = storage_client.bucket(bucket_name).blob(blob_name)
    content = blob.download_as_text()
    parsed_page_data = json.loads(content)

    return parsed_page_data


def read_and_concatenate_pages(
    bucket_name: str, prefix_path: str, query_id_str: str, batch_range: str, pages: list
) -> dict:
    """
    Reads and concatenates JSON data from multiple pages within a specified batch range
    from a Google Cloud Storage bucket into dataframes for each report name.

    Parameters:
    - bucket_name (str): The name of the Google Cloud Storage bucket.
    - prefix_path (str): The prefix path with organization_id, user_id, and query_id.
    - query_id_str (str): The identifier of a particular query.
    - batch_range (str): The batch range to identify a subset of data.
    - pages (list): A list of page names within the specified batch range.

    Returns:
    - dict: A dictionary where keys are report names and values are concatenated dataframes
            in the specified batch, or empty dict if an error occurs during the process.

    Raises:
    - Exception: If an error occurs during reading data from bucket, an exception is caught and logged.
    """
    try:
        report_dataframes = defaultdict(
            list
        )  # Use defaultdict to automatically initialize list for new report names

        for page_name in pages:
            try:
                # Extract report name from page name
                report_name = page_name.split("_API_Response_Page")[0]

                parsed_page_data = read_and_parse_data(
                    bucket_name, prefix_path, batch_range, page_name
                )
                if parsed_page_data:
                    df = pd.DataFrame(
                        columns=parsed_page_data["data"]["headings"],
                        data=parsed_page_data["data"]["rows"],
                    )
                    report_dataframes[report_name].append(df)

            except Exception as e:
                message = f"Error processing page: {page_name} for batch_range: {batch_range} of query_id: {query_id_str} => {e}"
                logger.error(message)
                return {}

        # Concatenate dataframes for each report name
        result_dataframes = {
            report_name: pd.concat(df_list, ignore_index=True)
            for report_name, df_list in report_dataframes.items()
        }

        return result_dataframes

    except Exception as e:
        message = f"Error reading data from bucket for batch_range: {batch_range} of query_id: {query_id_str} => {e}"
        meta_container = MetaContainer()
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.FAILED.value,
            message,
        )
        return {}


def find_common_columns(dataframes_dict: dict) -> list:
    """
    Find common columns among a dictionary of DataFrames.

    Parameters:
    - dataframes_dict (dict): A dictionary where keys are DataFrame names and values are DataFrames.

    Returns:
    - list: A list of common column names present in all DataFrames.
    """
    # Extract dataframes from the dictionary
    dataframes = list(dataframes_dict.values())

    # Get common columns of the first dataframe
    common_columns = set(dataframes[0].columns)

    # Iterate over remaining dataframes and find common columns
    for df in dataframes[1:]:
        common_columns = common_columns.intersection(df.columns)

    return list(common_columns)


def insert_dataframe_into_collection(
    collection: Collection,
    query_id_str: str,
    batch_range: str,
    df: pd.DataFrame,
) -> bool:
    """
    Insert a pandas DataFrame into a MongoDB collection.

    Parameters:
    - collection (pymongo.collection.Collection): The MongoDB collection.
    - query_id_str (str): The string representation of the query_id.
    - batch_range (str): The Range of the batch.
    - df (pandas.DataFrame): The DataFrame containing the data to be inserted.

    Returns:
    bool: True if data insertion is successful, False otherwise.
    """
    try:
        batch_data = df.to_dict(orient="records")
        collection.insert_many(batch_data)
        logger.info(
            "Data successfully inserted into collection: '%s' for query_id: '%s' and batch_range: '%s'.",
            collection.name,
            query_id_str,
            batch_range,
        )

        return True

    except Exception as e:
        message = f"An error occurred during data insertion for query_id: '{query_id_str}' and batch_range: '{batch_range}' in collection: '{collection.name}' => {str(e)}"
        meta_container = MetaContainer()
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.FAILED.value,
            message,
        )
        return False


def merge_report_dataframes(report_dataframes: dict) -> pd.DataFrame:
    """
    Merge dataframes representing different reports based on common columns.

    Parameters:
    - report_dataframes (dict): A dictionary where keys are report names and values are pandas DataFrames.

    Returns:
    - pandas.DataFrame: A merged dataframe containing data from all input dataframes.
    """
    # Check if there's only one dataframe
    if len(report_dataframes) == 1:
        report_name, dataframe = next(iter(report_dataframes.items()))
        logger.info(
            "Only one dataframe found for report name: '%s'. Skipping merging",
            report_name,
        )
        return dataframe

    # Get common columns across all dataframes
    common_columns = find_common_columns(report_dataframes)
    merged_dataframe = None

    # Iterate over each report name and its corresponding dataframe
    for report_name, dataframe in report_dataframes.items():
        if merged_dataframe is None:
            merged_dataframe = dataframe
        else:
            # Merge dataframes based on common columns
            merged_dataframe = pd.merge(
                merged_dataframe, dataframe, how="outer", on=common_columns
            )

    merged_dataframe.drop_duplicates(inplace=True)
    merged_dataframe.reset_index(drop=True, inplace=True)

    return merged_dataframe


def process_dataframe(
    df: pd.DataFrame, batch_id_str: str, query_id_str: str
) -> pd.DataFrame:
    """
    Add 'batch_id', and 'query_id' fields with the specified values to each row of the DataFrame.

    Parameters:
    - df (pandas.DataFrame): The DataFrame to which the 'batch_id' and 'query_id' fields will be added.
    - batch_id_str (str): The identifier of a particular batch.
    - query_id_str (str): The identifier of a particular query.

    Returns:
    - pandas.DataFrame: The modified DataFrame with the 'batch_id', and 'query_id'  fields added.
    """
    try:
        batch_id = ObjectId(batch_id_str)
        query_id = ObjectId(query_id_str)
        df["batch_id"] = batch_id
        df["query_id"] = query_id

        # Replace null-like values
        df.replace(
            {
                np.nan: None,
                pd.NA: None,
                pd.NaT: None,
            },
            inplace=True,
        )

        return df

    except Exception as e:
        message = f"Error during processing dataframe for batch_id: {batch_id_str} and query_id: {query_id_str} => {e}"
        meta_container = MetaContainer()
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.FAILED.value,
            message,
        )
        return pd.DataFrame()


# Triggered from a message on a Cloud Pub/Sub topic.
@functions_framework.cloud_event
def main(cloud_event: CloudEvent):
    """
    Cloud Function triggered by a CloudEvent.

    This function processes data from a CloudEvent payload, performs several operations
    including retrieving configuration from a query collection, fetching data from Google
    Cloud Storage, and inserting the processed data into rac collection of MongoDB database.

    Additionally, the function updates the status of the processed batch in the batch collection
    and publishes a message to a Pub/Sub topic upon successful data insertion.

    Parameters:
    - cloud_event (google.cloud.functions.Context): CloudEvent data provided by the function framework.

    Returns:
    - {"Status":200}: For successfully execution.
    """
    try:
        meta_container = MetaContainer()
        validation_hook = ValidationHook()

        payload = json.loads(
            base64.b64decode(cloud_event.data["message"]["data"]).decode("utf-8")
        )
        logger.info("Message received successfully: %s", payload)
        meta_container.set_payload_info(payload)
        validation_hook.set_payload_info(payload)

        batch = payload["batch"]

        batch_id_str = batch["_id"]
        batch_count = batch["batch_count"]

        data_source_id = payload["data_source_id"]
        organization_id_str = payload["organization_id"]
        query_id_str = payload["query_id"]

        start_time = batch["start_time"]
        end_time = batch["end_time"]

        batch_range = f"{start_time}-{end_time}"

        org_account_info = OrganizationAccountInfo(organization_id_str)
        hydration_bucket_name = org_account_info.hydration_bucket_name
        mongodb_url = org_account_info.mongodb_url
        organization_db_name = org_account_info.organization_db_name
        rac_collection_suffix = org_account_info.rac_collection_suffix

        mongo_db_client = get_mongodb_client(mongodb_url)
        organization_db = get_mongodb_db(mongo_db_client, organization_db_name)

        query_collection = get_mongodb_collection(
            organization_db, QUERY_COLLECTION_NAME
        )

        query_config = get_query_config(
            query_collection, query_id_str, QUERY_PROJECTION_ATTRIBUTES
        )
        if not query_config:
            logger.warning("No configuration found for query_id: '%s'", query_id_str)
            return

        user_id = query_config["user_id"]
        prefix_path = f"{organization_id_str}/{user_id}/{data_source_id}/{query_id_str}"
        query_meta_data = query_config["meta_data"][0]

        rac_collection_name = generate_collection_name(
            query_id_str, query_config["name"], rac_collection_suffix
        )
        rac_collection = get_mongodb_collection(organization_db, rac_collection_name)

        batch_collection_name = query_meta_data["BATCH_COLLECTION_NAME"]
        batch_collection = get_mongodb_collection(
            organization_db, batch_collection_name
        )

        if check_query_status(
            batch_collection,
            query_id_str,
            BatchStatusColumn.RAC_STATUS.value,
            BatchStatus.INACTIVE.value,
        ):
            message = f"Capabilities sprinklr rac script started successfully for query_id: {query_id_str}"
            meta_container.send_diagnostic(
                DiagnosticActionType.UPDATE.value,
                DiagnosticStatus.PENDING.value,
                message,
            )
            update_query_metadata(
                query_collection,
                query_id_str,
                RAC_COLLECTION_NAME_METADATA_KEY,
                rac_collection_name,
            )

        toggle_batch_status(
            batch_collection,
            batch_id_str,
            query_id_str,
            BatchStatusColumn.RAC_STATUS.value,
            BatchStatus.PENDING.value,
        )

        # List batch_ranges and page_names in the specified directory
        objects = list_objects_in_directory(hydration_bucket_name, prefix_path)
        filtered_objects = (
            {batch_range: objects[batch_range]} if batch_range in objects else {}
        )

        if not batch_range in filtered_objects:
            logger.warning(
                "batch_range: '%s' not found for query_id: '%s'. Skipping processing and insertion.",
                batch_range,
                query_id_str,
            )
            return

        pages = filtered_objects[batch_range]
        print("filtered_objects", filtered_objects)

        # Call read_and_concatenate_pages for the current batch
        report_dataframes = read_and_concatenate_pages(
            hydration_bucket_name, prefix_path, query_id_str, batch_range, pages
        )

        if report_dataframes:
            # Validation hook
            validation_hook.validate_batch_count(batch_count, report_dataframes)

            batch_dataframe = merge_report_dataframes(report_dataframes)
            processed_df = process_dataframe(
                batch_dataframe, batch_id_str, query_id_str
            )

            if not processed_df.empty:
                logger.info(
                    "Successfully processed dataframe with '%d' record counts for batch_range: '%s' of query_id: '%s'",
                    len(processed_df),
                    batch_range,
                    query_id_str,
                )
                success = insert_dataframe_into_collection(
                    rac_collection, query_id_str, batch_range, processed_df
                )
                status = (
                    BatchStatus.COMPLETED.value if success else BatchStatus.FAILED.value
                )

                toggle_batch_status(
                    batch_collection,
                    batch_id_str,
                    query_id_str,
                    BatchStatusColumn.RAC_STATUS.value,
                    status,
                )

                if success:
                    publish_pubsub_message(PROJECT_ID, DATA_UNIFIER_TOPIC_ID, payload)
                    if check_query_status(
                        batch_collection,
                        query_id_str,
                        BatchStatusColumn.RAC_STATUS.value,
                        BatchStatus.COMPLETED.value,
                    ):
                        message = f"Capabilities sprinklr rac script completed successfully for query_id: {query_id_str}"
                        meta_container.send_diagnostic(
                            DiagnosticActionType.UPDATE.value,
                            DiagnosticStatus.COMPLETED.value,
                            message,
                        )

            else:
                logger.warning(
                    "Warning: Processed batch_df for batch_range: '%s' of query_id: '%s' is empty. Skipping insertion.",
                    batch_range,
                    query_id_str,
                )

        else:
            logger.warning(
                "No data found for batch_range: '%s' of query_id: '%s'. Skipping processing and insertion.",
                batch_range,
                query_id_str,
            )

        mongo_db_client.close()
        return {"Status": 200}

    except Exception as e:
        message = f"An error occurred during capabilities sprinklr rac: {e}"
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.FAILED.value,
            message,
            str(type(e)),
        )
