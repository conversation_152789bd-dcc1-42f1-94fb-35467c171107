"""
This module contains a Cloud Function that processes incoming CloudEvents for data unification.
"""

import json
import base64
from pymongo.collection import ObjectId
import functions_framework
import pandas as pd
from cloudevents.http import CloudEvent
from sprinklr_api.const import PROJECT_ID, CLEANSING_TOPIC_ID
from utils.mongo_db import (
    get_mongodb_collection,
    get_mongodb_db,
    get_mongodb_client,
)
from utils.utilities import (
    BatchStatus,
    BatchStatusColumn,
    DiagnosticActionType,
    DiagnosticStatus,
    MetaContainer,
    OrganizationAccountInfo,
)
from utils.common_utils import (
    check_query_status,
    get_query_config,
    common_data_unifer,
    toggle_batch_status,
)
from utils.logger import logger
from utils.pubsub_publisher import publish_pubsub_message


@functions_framework.cloud_event
def main(cloud_event: CloudEvent):
    """
    This cloud function unifies data for file upload consumer.
    - It uses the organization vault to get the organization and query_id to get RAC collection.
    - Fetches the data for the batch.with batch_id
    - It uses the UNIFIER_META and calls the common_data_unifier method to map the
        fields specified for a batch

    Args:
    - cloud_event (CloudEvent): The CloudEvent object representing the incoming event.

    Returns:
    - Dict with status and message
        - status: 200 if success or 400 if any error occurs
        - message: message of success or error

    Raises:
    - Exception: If any error occurs during the execution process.

    """
    try:
        meta_container = MetaContainer()

        # parse the payload and extract query_id and organization_id from payload
        payload = json.loads(
            base64.b64decode(cloud_event.data["message"]["data"]).decode("utf-8")
        )
        logger.info("Message received successfully: %s", payload)
        meta_container.set_payload_info(payload)

        query_id, organization_id, batch_id = (
            payload["query_id"],
            payload["organization_id"],
            payload["batch"]["_id"],
        )

        # get organization details from organization_id
        organization = OrganizationAccountInfo(organization_id)

        # set the database connection and get the query collection
        client = get_mongodb_client(organization.mongodb_url)
        db = get_mongodb_db(client, organization.organization_db_name)
        query_collection = get_mongodb_collection(db, "query")

        # get query details and check if not None
        query = get_query_config(
            query_collection, query_id, {"_id": 1, "name": 1, "meta_data": 1}
        )

        if query is not None:
            # set the meta data
            meta_container.set_meta_data(query["meta_data"][0])

            # set the query collection name
            collection_name = meta_container.meta_data["RAC_COLLECTION_NAME"]
            batch_collection_name = meta_container.meta_data["BATCH_COLLECTION_NAME"]
            logger.info("collection name %s", collection_name)
            collection = get_mongodb_collection(db, collection_name)
            batch_collection = get_mongodb_collection(db, batch_collection_name)

            # load data in DataFrame and call the common_data_unifer with UNIFIER_META mapping
            unifier_meta = meta_container.meta_data["UNIFIER_META"]

            message = f"Processing for batch {batch_id} and query {query_id}"
            logger.info(message)

            if check_query_status(
                batch_collection,
                query_id,
                BatchStatusColumn.DATA_UNIFIER_STATUS.value,
                BatchStatus.INACTIVE.value,
            ):
                message = f"Capabilities sprinklr data unifier script started successfully for batch_id: {batch_id} and query_d: {query_id}"
                meta_container.send_diagnostic(
                    DiagnosticActionType.UPDATE.value,
                    DiagnosticStatus.PENDING.value,
                    message,
                )

            # status toggle for the batch to PENDING
            toggle_batch_status(
                batch_collection,
                batch_id,
                query_id,
                BatchStatusColumn.DATA_UNIFIER_STATUS.value,
                BatchStatus.PENDING.value,
            )

            # Fetch the data for batch and unify with mapping
            df = pd.DataFrame(collection.find({"batch_id": ObjectId(batch_id)}))
            common_data_unifer(df, unifier_meta, collection)

            # status toggle for the batch to complete
            toggle_batch_status(
                batch_collection,
                batch_id,
                query_id,
                BatchStatusColumn.DATA_UNIFIER_STATUS.value,
                BatchStatus.COMPLETED.value,
            )
            message = f"Succesfully updated field mapping on {collection_name}"
            logger.info(message)
            publish_pubsub_message(PROJECT_ID, CLEANSING_TOPIC_ID, payload)

            if check_query_status(
                batch_collection,
                query_id,
                BatchStatusColumn.DATA_UNIFIER_STATUS.value,
                BatchStatus.COMPLETED.value,
            ):
                message = f"Capabilities sprinklr data unifier script completed successfully for batch_id: {batch_id} and query_d: {query_id}"
                meta_container.send_diagnostic(
                    DiagnosticActionType.UPDATE.value,
                    DiagnosticStatus.COMPLETED.value,
                    message,
                )

            return {"status": 200, "message": message}
        else:
            message = f"Query details not found for id {query_id}"
            logger.warning(message)
            return {"status": 400, "message": message}

    except Exception as e:
        message = f"An error occured during capabilities sprinklr data unifier while updating the field mapping:{e}"
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.FAILED.value,
            message,
            str(type(e)),
        )
        return {"status": 400, "message": message}
