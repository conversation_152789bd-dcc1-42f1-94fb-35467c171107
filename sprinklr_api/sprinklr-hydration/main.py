"""
Module which pulls the data from sprinklr for the desired timezone, 
and uploads it to the GCP bucket.
"""

import json
import base64
import time
import functions_framework
from cloudevents.http import CloudEvent
from google.cloud import storage
import requests
from sprinklr_api.hooks.validator import Val<PERSON><PERSON><PERSON>ook
from sprinklr_api.const import (
    SPRINKLR_API_END_POINT,
    QUERY_COLLECTION_NAME,
    PAGE_SIZE,
    PROJECT_ID,
    HYDRATION_TOPIC_ID,
    RAC_TOPIC_ID,
    MAX_RETRIES,
    BASE_DELAY,
    BASE_LOOPBACK_THRESHOLD,
    QUERY_PROJECTION_ATTRIBUTES,
    TIMEOUT,
)
from utils.common_utils import (
    check_query_status,
    get_query_config,
    set_batch_count,
    toggle_batch_status,
)
from utils.mongo_db import get_mongodb_client, get_mongodb_collection, get_mongodb_db
from utils.pubsub_publisher import publish_pubsub_message
from utils.utilities import (
    BatchStatus,
    BatchStatusColumn,
    DiagnosticActionType,
    DiagnosticStatus,
    MetaContainer,
    OrganizationAccountInfo,
)
from utils.logger import logger


def upload_to_gcs(
    bucket_name: str,
    file_content,
    destination_blob_name: str,
    max_retries: int = 3,
    retry_delay: int = 1,
) -> None:
    """
    Uploads a file to a Google Cloud Storage bucket with retry logic.

    Paramaters:
    - bucket_name (str): Name of the GCP bucket.
    - file_content : Content of the file to upload.
    - destination_blob_name (str): Destination blob name where file will be uploaded.
    - max_retries (int, optional): Maximum number of retries.
    - retry_delay (int): Delay for a retry.
    """
    # Create a client
    storage_client = storage.Client()

    for retry_count in range(max_retries + 1):  # +1 to include the initial attempt
        try:
            # Get the bucket
            bucket = storage_client.bucket(bucket_name)

            # Create a blob (object) in the bucket
            blob = bucket.blob(destination_blob_name)

            # Upload the local file to the blob
            with blob.open("w") as f:
                f.write(file_content)

            logger.info(
                "File written to gs://%s/%s",
                bucket_name,
                destination_blob_name,
            )
            return  # If successful, exit the loop

        except Exception as e:
            logger.error(
                "Error during upload attempt %s/%s: %s", retry_count, max_retries, e
            )

            if retry_count < max_retries:
                logger.warning("Retrying in %s seconds...", retry_delay)
                time.sleep(retry_delay)
            else:
                message = f"Max retries reached. Unable to upload file: {str(e)}"
                raise Exception(
                    message
                ) from e  # If max retries reached, raise the exception


def get_data_from_sprinklr_api(bearer_headers: dict, sprinklr_payload: dict) -> dict:
    """
    This method calls the sprinklr_API to get the sprinklr_data for a particular widget payload
    - SPRINKLR_API_END_POINT is The sprinklr reporting API endpoint

    Parameters:
    - bearer_headers (dict) : headers with authentication token
    - sprinklr_payload (dict) : sprinklr payload of the data_files with updated startTime, endTime, page, and pageSize.

    Returns:
    - data (dict) : Json data from sprinklr API
    """
    retries = 0
    data = None
    while retries < int(MAX_RETRIES):
        try:
            response = requests.post(
                url=SPRINKLR_API_END_POINT,
                headers=bearer_headers,
                json=sprinklr_payload,
                timeout=TIMEOUT,
            )
            response.raise_for_status()
            data = response.json()
            return data

        except requests.RequestException as e:
            logger.warning("Request failed: %s", e)
            retries += 1
            delay = int(BASE_DELAY) * (2**retries)  # Exponential back-off formula
            logger.warning("Retrying in %s seconds...", delay)
            time.sleep(delay)

    raise Exception("Max retries reached. Unable to fetch records.")


def fetch_and_upload_sprinklr_data(
    batch: dict,
    token: str,
    sprinklr_payload: dict,
    bucket_name: str,
    bucket_path: str,
    loopback_threshold: int,
    page: int,
    record_count: int,
    payload: dict,
) -> tuple[int, int, bool]:
    """
    This method fetch the sprinklr data batch wise.

    Parameters:
    - batch (dict) : Batch of week.
    - token (str) : Access token for sprinklr API.
    - sprinklr_payload (dict): Sprinklr API payload.
    - bucket_name (str): Name of the GCP bucket.
    - bucket_path (str): Path of the GCP bucket.
    - loopback_threshold (int): Threshold for the loopback.
    - page (int): Page from where to fetch the data.
    - record_count (int): Record count from where to start.
    - payload (dict): Pub sub message payload.

    Returns:
    - record_count (int) : Number of records present in the batch for particular payload.
    - batch_record_count (int): Number of records in the current batch.
    - is_loopback_required (bool): Indicates whether loopback is required.
    """
    sprinklr_payload["startTime"] = batch["start_time"]
    sprinklr_payload["endTime"] = batch["end_time"]
    sprinklr_payload["pageSize"] = int(PAGE_SIZE)
    report_name = sprinklr_payload["report"]

    logger.info("Fetching data for report name: '%s'", report_name)
    batch_record_count = payload.get("batch", {}).get("batch_count", 0)
    per_page_count = 0

    while True:
        sprinklr_payload["page"] = page
        bearer_headers = {"authorization": f"Bearer {token}"}
        data = get_data_from_sprinklr_api(bearer_headers, sprinklr_payload)

        if data:
            if "rows" not in data["data"].keys():
                break
            per_page_count = len(data["data"]["rows"])
            record_count += per_page_count
            batch_record_count += per_page_count
            filepath = f"{bucket_path}/{report_name}_API_Response_Page_{page}"
            upload_to_gcs(bucket_name, json.dumps(data), filepath)
        page += 1

        if batch_record_count >= loopback_threshold:
            payload.setdefault("hydration_loopback_checkpoint", {})[
                "current_page"
            ] = page
            payload.setdefault("hydration_loopback_checkpoint", {})[
                "current_record_count"
            ] = record_count
            return record_count, batch_record_count, True

    return record_count, batch_record_count, False


# Triggered from a message on a Cloud Pub/Sub topic.
@functions_framework.cloud_event
def main(cloud_event: CloudEvent):
    """
    Cloud Function triggered by a CloudEvent.

    - It pulls the data from sprinklr for the desired timezone, and uploads it to the GCP bucket.
    - Steps:
        - Get and parse the pubsub payload.
        - Get the token from pubsub payload.
        - Get the organization_db using organization vault.
        - Get query configuration data such as payload, batch_collection_name,
            data_source_id and user_id.
        - Toggles status of batch to 'PENDING' for hydration.
        - Fetch and upload sprinklr data to GCP bucket.
        - If the batch record count is greater than or equal to the loopback threshold:
            - Publish a message to the hydration consumer for loopback for the remaining records.
        - Else:
            - Toggles status of batch to 'COMPLETED' for hydration if all records
                have been fetched successfully.
            - Sets the batch_count value in the batch collection.
            - Publish pubsub message to 'RAC' consumer.

    Parameters:
    - cloud_event (google.cloud.functions.Context): CloudEvent data provided by the
        function framework.

    Returns:
    - {"Status":200}: For successfully execution.
    """
    try:
        meta_container = MetaContainer()
        validation_hook = ValidationHook()

        payload = json.loads(
            base64.b64decode(cloud_event.data["message"]["data"]).decode("utf-8")
        )

        logger.info("Message received successfully: %s", payload)
        meta_container.set_payload_info(payload)
        validation_hook.set_payload_info(payload)

        batch = payload["batch"]
        token = payload["access_token"]

        batch_id = batch["_id"]

        start_time = batch["start_time"]
        end_time = batch["end_time"]

        initial_batch_record_count = batch["batch_count"]
        data_source_id = payload["data_source_id"]
        organization_id_str = payload["organization_id"]
        query_id_str = payload["query_id"]
        batch_range = f"{start_time}-{end_time}"

        current_meta_index = payload.get("hydration_loopback_checkpoint", {}).get(
            "current_meta_index", 0
        )
        current_page = payload.get("hydration_loopback_checkpoint", {}).get(
            "current_page", 0
        )
        current_record_count = payload.get("hydration_loopback_checkpoint", {}).get(
            "current_record_count", 0
        )

        org_account_info = OrganizationAccountInfo(organization_id_str)
        hydration_bucket_name = org_account_info.hydration_bucket_name
        mongodb_url = org_account_info.mongodb_url
        organization_db_name = org_account_info.organization_db_name

        mongo_db_client = get_mongodb_client(mongodb_url)
        organization_db = get_mongodb_db(mongo_db_client, organization_db_name)
        query_collection = get_mongodb_collection(
            organization_db, QUERY_COLLECTION_NAME
        )

        query_config = get_query_config(
            query_collection, query_id_str, QUERY_PROJECTION_ATTRIBUTES
        )

        if not query_config:
            logger.warning("No configuration found for query_id: '%s'", query_id_str)
            return

        user_id = query_config["user_id"]
        bucket_path = f"{organization_id_str}/{user_id}/{data_source_id}/{query_id_str}/{batch_range}"
        query_meta_data = query_config.get("meta_data", [])
        batch_collection_name = query_meta_data[0]["BATCH_COLLECTION_NAME"]
        batch_collection = get_mongodb_collection(
            organization_db, batch_collection_name
        )

        if check_query_status(
            batch_collection,
            query_id_str,
            BatchStatusColumn.HYDRATION_STATUS.value,
            BatchStatus.INACTIVE.value,
        ):
            message = f"Capabilities sprinklr hydration script started successfully for query_id: {query_id_str}"
            meta_container.send_diagnostic(
                DiagnosticActionType.UPDATE.value,
                DiagnosticStatus.PENDING.value,
                message,
            )

        toggle_batch_status(
            batch_collection,
            batch_id,
            query_id_str,
            BatchStatusColumn.HYDRATION_STATUS.value,
            BatchStatus.PENDING.value,
        )

        # Looping though query_meta_data starting from the current_meta_index to fetch sprinklr data for each payload
        for meta_index, meta_data in enumerate(
            query_meta_data[current_meta_index:], start=current_meta_index
        ):
            # Determine the initial page and initial record count to start processing
            initial_page = current_page if meta_index == current_meta_index else 0
            initial_record_count = (
                current_record_count if meta_index == current_meta_index else 0
            )
            loopback_threshold = initial_batch_record_count + int(
                BASE_LOOPBACK_THRESHOLD
            )

            sprinklr_payload = json.loads(meta_data["PAYLOAD"])
            report_name = sprinklr_payload["report"]
            record_count, batch_record_count, is_loopback_required = (
                fetch_and_upload_sprinklr_data(
                    batch,
                    token,
                    sprinklr_payload,
                    hydration_bucket_name,
                    bucket_path,
                    loopback_threshold,
                    initial_page,
                    initial_record_count,
                    payload,
                )
            )
            payload["batch"]["batch_count"] = batch_record_count

            # Validation hook
            validation_hook.validate_pages(
                hydration_bucket_name,
                bucket_path,
                record_count,
                report_name,
            )
            if is_loopback_required:
                # Update the payload with the current_meta_index
                payload.setdefault("hydration_loopback_checkpoint", {})[
                    "current_meta_index"
                ] = meta_index

                publish_pubsub_message(PROJECT_ID, HYDRATION_TOPIC_ID, payload)
                logger.info(
                    "Published loopback message with current_meta_index: '%d', current_page: '%d', current_record_count: '%d', and batch_record_count: '%d' for '%s' report ",
                    payload["hydration_loopback_checkpoint"]["current_meta_index"],
                    payload["hydration_loopback_checkpoint"]["current_page"],
                    payload["hydration_loopback_checkpoint"]["current_record_count"],
                    batch_record_count,
                    report_name,
                )
                return {"Status": 200}

        logger.info(
            "Fetched all the records for the batch_range: '%s' of query_id: '%s'",
            batch_range,
            query_id_str,
        )

        toggle_batch_status(
            batch_collection,
            batch_id,
            query_id_str,
            BatchStatusColumn.HYDRATION_STATUS.value,
            BatchStatus.COMPLETED.value,
        )

        set_batch_count(
            batch_collection,
            batch_id,
            query_id_str,
            payload["batch"]["batch_count"],
        )
        payload.pop("hydration_loopback_checkpoint", None)
        publish_pubsub_message(PROJECT_ID, RAC_TOPIC_ID, payload)

        if check_query_status(
            batch_collection,
            query_id_str,
            BatchStatusColumn.HYDRATION_STATUS.value,
            BatchStatus.COMPLETED.value,
        ):
            message = f"Capabilities sprinklr hydration script completed successfully for query_id: {query_id_str}"
            meta_container.send_diagnostic(
                DiagnosticActionType.UPDATE.value,
                DiagnosticStatus.COMPLETED.value,
                message,
            )

        mongo_db_client.close()
        return {"Status": 200}

    except Exception as e:
        message = f"An error occurred during capabilities sprinklr hydration: {e}"
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.FAILED.value,
            message,
            str(type(e)),
        )
