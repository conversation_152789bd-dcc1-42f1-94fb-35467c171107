import time
import json
import base64
import faiss
import pandas as pd
import numpy as np
import functions_framework
from pymongo.operations import UpdateOne
from bson import ObjectId
from similarity.const import (
    SEQUENCE_COORDINATOR_TOPIC_ID,
    PROJECT_ID,
    PROJECTION,
    QUERY_COLLECTION,
    ENCAPSULATION_MARKER_COLLECTION_NAME_METADATA_KEY,
    SIM<PERSON>ARITY_THRESHOLD,
    DIMENTION,
    SEED
)
from utils.mongo_db import get_mongodb_client, get_mongodb_collection, get_mongodb_db
from utils.common_utils import get_query_config
from utils.pubsub_publisher import publish_pubsub_message
from utils.utilities import (
    CONSUMER_TYPE,
    EncapsulationMarkerStatusColumn,
    MetaContainer,
    DiagnosticActionType,
    DiagnosticStatus,
    OrganizationAccountInfo,
    EncapsulationMarkerStatus,
)
from utils.logger import logger


# Triggered from a message on a Cloud Pub/Sub topic.
@functions_framework.cloud_event
def main(cloud_event):
    """
    This main function where execution start and steps that this function performs are
    - Parsing the payload from pubsub
    - Establishes the database connection
    - Set diagnostics and similarity status in encapsulation marker collection to pending
    - Get the query , RAC and encapsulation marker collection object
    - Fetch the records for the encapsulation_marker and where embedding exists True
    - Set the embedding list and the dimension required of the index
    - Create the similarity index with list and dimension
    - Form the cluster with the similarity index
    - Update the similarity status in encapsulation marker collection to completed
    - Check if all the similarity status in encapsulation marker collection are completed then mark diagnostic to complete
    - Publish message to capabilities sequence coordinator topic id

    Parameters:
    - cloud_event (CloudEvent): The CloudEvent object representing the incoming event.

    Returns:
    - Dict with status and message
        - status: 200 if success or 400 if any error occurs
        - message: message of success or error

    Exception handling:
    - If any error occurs during the execution process log the error and send the message to diagnostic
    """
    mongodb_client = None
    try:
        meta_container = MetaContainer()

        # parse the payload and extract query_id and organization_id from payload
        payload = json.loads(
            base64.b64decode(cloud_event.data["message"]["data"]).decode("utf-8")
        )
        payload["publisher_type"] = CONSUMER_TYPE

        logger.info("Message received successfully: %s", payload)
        meta_container.set_payload_info(payload)

        query_id, organization_id, encapsulation_marker = (
            payload["query_id"],
            payload["organization_id"],
            payload["encapsulation_marker"],
        )

        message = f"Capabilities Similarity script started successfully for query_id: {query_id}"
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.PENDING.value,
            message,
        )

        start_time = time.perf_counter()

        # Fetch organization account information
        org_info = OrganizationAccountInfo(organization_id)
        mongodb_connection_string = org_info.mongodb_url
        db_name = org_info.organization_db_name

        # Establish MongoDB connection
        mongodb_client = get_mongodb_client(mongodb_connection_string)
        db = get_mongodb_db(mongodb_client, db_name)

        query_collection = get_mongodb_collection(db, QUERY_COLLECTION)
        query_config = get_query_config(query_collection, query_id, PROJECTION)

        if query_config is not None:
            # Retrieve collection name
            meta_container.set_meta_data(query_config["meta_data"])
            rac_collection_name = meta_container.meta_data["RAC_COLLECTION_NAME"]
            encapsulation_marker_collection_name = meta_container.meta_data[
                ENCAPSULATION_MARKER_COLLECTION_NAME_METADATA_KEY
            ]
            rac_collection = get_mongodb_collection(db, rac_collection_name)

            encapsulation_marker_collection = get_mongodb_collection(
                db, encapsulation_marker_collection_name
            )

            encapsulation_marker_collection.update_one(
                {
                    "query_id": ObjectId(query_id),
                    "encapsulation_marker": encapsulation_marker,
                },
                {
                    "$set": {
                        EncapsulationMarkerStatusColumn.SIMILARITY_STATUS.value: EncapsulationMarkerStatus.PENDING.value
                    }
                },
            )

            cursor = rac_collection.find(
                {
                    "encapsulation_marker": encapsulation_marker,
                    "embedding": {"$exists": True},
                },
                {"embedding": 1},
            )
            # Read the JSON file into a pandas DataFrame
            df = pd.DataFrame(cursor)
            embeddings = df["embedding"].to_list()
            vectors = np.array(embeddings)

            # Assuming vectors are already normalized
            # TODO: Add dimension in config file
            dimension = DIMENTION
            num_vectors = len(vectors)
            np.random.seed(SEED)

            # Create a FAISS index
            index = faiss.IndexFlatIP(
                dimension
            )  # Using inner product for cosine similarity on normalized vectors # check other parameters for this index
            index.add(vectors)  # Add vectors to the index

            # Array to hold cluster IDs
            cluster_ids = np.full(num_vectors, -1, dtype=int)

            # Current cluster ID
            current_cluster_id = 0

            for i in range(num_vectors):
                if (
                    cluster_ids[i] == -1
                ):  # If this vector is not yet assigned to a cluster
                    # Search for neighbors with at least 70% similarity across the entire dataset
                    distances, indices = index.search(
                        vectors[i : i + 1], num_vectors
                    )  # we set k-limit to num_vectors - converting ANN to KNN
                    # Optimization ToDo - check to see if the index can only consider nonassigned datapoints

                    # Filter results to only include those above the similarity threshold
                    valid_indices = indices[0][
                        distances[0] >= float(SIMILARITY_THRESHOLD)
                    ]

                    # Further filter out already clustered points
                    valid_indices = [
                        idx for idx in valid_indices if cluster_ids[idx] == -1
                    ]

                    if valid_indices:
                        # Assign the current cluster ID to all valid indices
                        cluster_ids[valid_indices] = current_cluster_id

                        # Increment the cluster ID for the next cluster
                        current_cluster_id += 1

            records = []

            # Log the total number of clusters formed
            logger.info("Total clusters formed: %s", current_cluster_id)
            df["Cluster_ID_faiss"] = cluster_ids
            df = df[["_id", "Cluster_ID_faiss"]]
            values = df.values

            for _id, cluster_id in values:
                records.append(
                    UpdateOne(
                        {"_id": ObjectId(_id)}, {"$set": {"cluster_id": cluster_id}}
                    )
                )

            rac_collection.bulk_write(records)
            end_time = time.perf_counter()
            total_time = end_time - start_time
            message = "Time taken for the similarity for  encapsulation_marker %s which has records % s is : %s"
            logger.info(message, encapsulation_marker, len(df), total_time)
        
        else:
            logger.warning("Query config not found")

        encapsulation_marker_collection.update_one(
            {
                "query_id": ObjectId(query_id),
                "encapsulation_marker": encapsulation_marker,
            },
            {
                "$set": {
                    EncapsulationMarkerStatusColumn.SIMILARITY_STATUS.value: EncapsulationMarkerStatus.COMPLETED.value
                }
            },
        )

        is_all_encapsulation_markers_completed = (
            encapsulation_marker_collection.count_documents(
                {
                    "query_id": ObjectId(query_id),
                    EncapsulationMarkerStatusColumn.SIMILARITY_STATUS.value: {
                        "$ne": EncapsulationMarkerStatus.COMPLETED.value
                    },
                }
            )
            == 0
        )
  
        if is_all_encapsulation_markers_completed:
            message = f"Capabilities Similarity script completed successfully for query_id: {query_id}"
            meta_container.send_diagnostic(
                DiagnosticActionType.UPDATE.value,
                DiagnosticStatus.COMPLETED.value,
                message,
            )

        publish_pubsub_message(PROJECT_ID, SEQUENCE_COORDINATOR_TOPIC_ID, payload)

        return {"status": 200}

    except Exception as e:
        message = f"An error occured while running similarity : {e}"
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.FAILED.value,
            message,
            str(type(e)),
        )
  
    finally:
        if mongodb_client is not None:
            mongodb_client.close()
