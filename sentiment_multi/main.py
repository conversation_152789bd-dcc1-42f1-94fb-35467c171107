"""
This module processes incoming cloud events for sentiment analysis by handling the
entire flow from event decoding to updating the database and sending messages
based on processing status.
"""

import base64
import json
import math
import os
import re
import time
from typing import List
from bson import ObjectId
import dask
from dask import delayed
import functions_framework
from google.api_core.exceptions import InternalServerError, ResourceExhausted
import pandas as pd
from pymongo import UpdateOne
from pymongo.collection import Collection
from const import (
    DEFAULT_SENTIMENT,
    DEFAULT_EXPLANATION,
    QUERY_COLLECTION_NAME,
    MAX_PARSE_RETRIES,
    MAX_UNIQUE_CONTEXT_IDS,
    PROJECT_ID,
    PARSE_RETRY_DELAY,
    SENTIMENT_CLASSIFICATION,
    SENTIMENT_PROMPT_CHUNK_SIZE,
    SENTIMENT_MULTI_TOPIC_ID,
    SEQUENCE_COORDINATOR_TOPIC_ID,
    LOOPBACK_THRESHOLD,
    RAW_TEXT_COL,
    ENCAPSULATION_MARKER_COLLECTION_NAME_METADATA_KEY,
    <PERSON><PERSON><PERSON>_NAME_METADATA_KEY,
    RAC_COLLECTION_NAME_METADATA_KEY,
    QUERY_PROJECTION_ATTRIBUTES,
    SENTIMENT_BATCH_PROMPT,
    SENTIMENT_BATCH_PROMPT_NO_CONTEXT,
    ORG_CREDENTIALS_COLLECTION,
)
from utils.langchain_utils import LangChainUtility
from utils.llm_settings import SUMMARIES_TO_IGNORE
from utils.common_utils import (
    check_marker_status,
    check_query_status,
    get_query_config,
    toggle_marker_status,
    generate_sentiment_analysis,
    clean_sentiment,
)
from utils.logger import logger
from utils.mongo_db import get_mongodb_client, get_mongodb_collection, get_mongodb_db
from utils.pubsub_publisher import publish_pubsub_message
from utils.utilities import (
    CONSUMER_TYPE,
    DiagnosticActionType,
    DiagnosticStatus,
    EncapsulationMarkerStatus,
    EncapsulationMarkerStatusColumn,
    SentimentProcessingField,
    SummaryContext,
    SummaryContextIdField,
    MetaContainer,
    OrganizationAccountInfo,
)


def fetch_collection_data(
    collection: Collection,
    query_filter: dict,
    encapsulation_marker: str,
    context: str,
    max_unique_context_ids: int = int(MAX_UNIQUE_CONTEXT_IDS),
    max_records: int = int(LOOPBACK_THRESHOLD),
) -> pd.DataFrame | None:
    """
    Fetch records from a MongoDB collection, grouping by `unique_context_id`.
    Limits the number of unique context IDs to `max_unique_context_ids` and
    distributes the total number of records (`max_records`) across these IDs.
    Excludes context IDs where all records are processed.

    Parameters:
    - collection (pymongo.collection.Collection): The MongoDB collection object.
    - query_filter (dict): The query filter to fetch documents from collection.
    - encapsulation_marker (str): Filter records based on this marker.
    - context (str): The processing context which can be one of "CLUSTER", "STORY" or "THEME".
    - max_unique_context_ids (int): Maximum number of unique context IDs to fetch.
    - max_records (int): Maximum number of records to fetch in total, distributed across context IDs.

    Returns:
    - pd.DataFrame: A DataFrame containing the fetched records.

    Exception Handling:
    - Exception: If an error occurs during the fetch process, the error message is sent
        to the diagnostic engine.
    """
    try:
        # Get context ID and summary col fields
        context_id_field = SummaryContextIdField[context].value
        summary_col = f"{SummaryContext[context].value}_summary"

        # Pipeline for finding unique context_IDs count
        count_pipeline = [
            {"$match": query_filter},
            {
                "$sort": {
                    context_id_field: -1  # Sort by context_id field in descending order
                }
            },
            {"$limit": max_records},  # Limit the number of records before counting
            {
                "$group": {"_id": f"${context_id_field}"}
            },  # Group by the context_id field
            {"$limit": max_unique_context_ids},  # Limit the number of unique groups
            {"$count": "unique_context_ids_count"},
        ]

        # Execute the aggregation pipeline
        unique_context_count_result = list(collection.aggregate(count_pipeline))

        # Extract the count
        unique_context_ids_count = (
            unique_context_count_result[0]["unique_context_ids_count"]
            if unique_context_count_result
            else 1
        )

        # Determine the maximum number of records per context ID
        records_per_context_id = max_records // unique_context_ids_count

        pipeline = [
            {"$match": query_filter},
            {
                "$sort": {
                    context_id_field: -1  # Sort by context_id_field in descending order
                }
            },
            {"$limit": max_records},  # Limit the number of records before grouping
            {
                "$group": {
                    "_id": f"${context_id_field}",  # Group by unique context ID
                    "records": {
                        "$push": {
                            "_id": "$_id",
                            f"{RAW_TEXT_COL}": f"${RAW_TEXT_COL}",
                            f"{context_id_field}": f"${context_id_field}",
                            f"{summary_col}": f"${summary_col}",
                        }
                    },
                }
            },
            {
                "$limit": max_unique_context_ids  # Limit the number of unique context IDs
            },
            {
                "$addFields": {
                    "records": {
                        "$slice": [
                            "$records",
                            records_per_context_id,
                        ]  # Limit records per unique context IDs
                    }
                }
            },
        ]

        records = list(collection.aggregate(pipeline))

        # Convert the list of records to a DataFrame
        records = pd.json_normalize(records, "records")

        logger.info(
            "Successfully fetched '%d' records for '%s' encapsulation marker from '%s' collection",
            len(records),
            encapsulation_marker,
            collection.name,
        )

        return records

    except Exception as e:
        message = f"An error occurred while fetching data for '{encapsulation_marker}' encapsulation marker from '{collection.name}' collection: {e}"
        meta_container = MetaContainer()
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.FAILED.value,
            message,
        )
        return None


def find_overall_processing_context(
    collection: Collection, encapsulation_marker: str
) -> str:
    """
    Determines the overall processing context based on the sentiment processing status
    in a MongoDB collection.

    This function checks documents in the specified collection to determine which processing
    context to return. It evaluates the sentiment processing status for clusters, stories,
    and themes in the documents based on the provided encapsulation marker. The priority is
    checked in the following order:
        - If any document has `is_cluster_sentiment_processed` missing, the context is "CLUSTER".
        - If all clusters are processed but any document has `is_story_sentiment_processed` missing,
        the context is "STORY".
        - If all clusters and stories are processed but any document has `is_theme_sentiment_processed`
        missing, the context is "THEME".
        - If all fields are processed, the context is "ALL_PROCESSED".

    Parameters:
    - collection (pymongo.collection.Collection): The MongoDB collection to query.
    - encapsulation_marker (str): The value of the `encapsulation_marker` field used
        to filter documents.

    Returns:
    - str: The processing context which can be one of "CLUSTER", "STORY", "THEME",
        or "ALL_PROCESSED".

    Exception Handling:
    - None
    """
    # Check for any document where the `is_cluster_sentiment_processed` field is missing
    cluster_check = collection.find_one(
        {
            "encapsulation_marker": encapsulation_marker,
            SentimentProcessingField.CLUSTER.value: {"$exists": False},
        },
        projection={"_id": 1},
    )

    if cluster_check:
        return SummaryContext.CLUSTER.value.upper()

    # TODO: Story and theme sentiment processing are currently disabled, as only cluster sentiment is needed.
    # Re-enable if additional sentiment processing becomes necessary in the future.
    # Check for any document where the `is_story_sentiment_processed` field is missing

    # story_check = collection.find_one(
    #     {
    #         "encapsulation_marker": encapsulation_marker,
    #         SentimentProcessingField.STORY.value: {"$exists": False},
    #     },
    #     projection={"_id": 1},
    # )
    # if story_check:
    #     return SummaryContext.STORY.value.upper()

    # Check for any document where the `is_theme_sentiment_processed` field is missing
    # theme_check = collection.find_one(
    #     {
    #         "encapsulation_marker": encapsulation_marker,
    #         SentimentProcessingField.THEME.value: {"$exists": False},
    #     },
    #     projection={"_id": 1},
    # )
    # if theme_check:
    #     return SummaryContext.THEME.value.upper()

    # If all documents have cluster, story, and theme sentiment processed fields present
    return SummaryContext.ALL_PROCESSED.value.upper()


def extract_and_parse_json(text: str) -> List:
    """
    Extracts a JSON array from a given text string and parses it.

    Parameters:
    - text (str): The input text containing random text and a JSON array.

    Returns:
    - list: Parsed JSON data if extraction and parsing are successful.

    Exception Handling:
    -  Exception: Raised error message if parsing fails.
    """
    # Regular expression pattern to capture JSON array
    pattern = r"\[\s*(?:\{[^{}]*\}(?:\s*,\s*\{[^{}]*\})*)\s*\]"

    # Search for the JSON portion in the string
    match = re.search(pattern, text)

    if match:
        json_string = match.group(0)
        try:
            # Parse the JSON string
            data = json.loads(json_string)
            return data
        except json.JSONDecodeError as e:
            raise Exception(f"JSON decoding failed: {e}") from e

    else:
        return []


def generate_prompt_for_chunk(
    chunk_df: pd.DataFrame, summary_col: str, separator: str
) -> str:
    """
    Generates a prompt for a chunk of data for sentiment analysis.

    Parameters:
    - chunk_df (pd.DataFrame): DataFrame containing a chunk of records.
    - summary_col (str): Name of the column containing summaries.
    - separator (str): Separator string to distinguish between records.

    Returns:
    - str: The generated prompt for the chunk.

    Exception Handling:
    - None
    """
    # Use the first summary as the shared context
    summary_text = chunk_df.get(summary_col, pd.Series([None])).iloc[0]

    # Create text entries for each row in the chunk
    text_entries = []
    for record_number, (_, row) in enumerate(chunk_df.iterrows(), start=1):
        text = row[RAW_TEXT_COL]
        text_entry = (
            f"TEXT {record_number}:\n{text}"  # Use sequential numbering for the prompt
        )
        text_entries.append(text_entry)

    # Combine text entries with the specified separator
    combined_texts = f"\n{separator}\n".join(text_entries)

    # Determine the appropriate prompt based on summary availability
    if pd.isna(summary_text) or summary_text in SUMMARIES_TO_IGNORE:
        prompt = SENTIMENT_BATCH_PROMPT_NO_CONTEXT.format(combined_texts=combined_texts)
    else:
        prompt = SENTIMENT_BATCH_PROMPT.format(
            summary=summary_text, combined_texts=combined_texts
        )

    return prompt


def update_dataframe_with_sentiment(
    df: pd.DataFrame,
    parsed_response: list,
    sentiment_col: str,
    sentiment_explanation_col: str,
    is_sentiment_processed_col: str,
    start_index: int,
) -> None:
    """
    Updates the original DataFrame with sentiment and explanation results in bulk.

    Parameters:
    - df (pd.DataFrame): Original DataFrame to be updated.
    - parsed_response (list): Parsed response from the model API.
    - sentiment_col (str): The sentiment column in which we need to store sentiment.
    - sentiment_explanation_col (str): The sentiment explanation column in which we need
        to store sentiment explanation.
    - is_sentiment_processed_col (str): The sentiment processed column in which we need
        to store sentiment processing status.
    - start_index (int): The starting index in the DataFrame for the current chunk.

    Returns:
    - None

    Exception Handling:
    - Exception: Raised if Mismatch between parsed response length and expected update range.
    """
    # Convert parsed_response to a DataFrame
    response_df = pd.DataFrame(parsed_response)

    # Ensure 'record_number' is sorted to match the original DataFrame index order
    response_df.sort_values(by="record_number", inplace=True)

    # Create index range for updating the original DataFrame
    update_index = df.index[start_index : start_index + len(response_df)]

    # Check if lengths match before updating
    if len(response_df) != len(update_index):
        raise Exception(
            "Mismatch between parsed response length and expected update range."
        )

    # Apply the cleaning function to sentiment column
    response_df["sentiment"] = response_df["sentiment"].apply(clean_sentiment)

    # Mark the sentiment processing status for particular context
    response_df[is_sentiment_processed_col] = response_df["sentiment"].isin(
        SENTIMENT_CLASSIFICATION
    )

    # Update multiple columns at once in the original DataFrame
    df.loc[
        update_index,
        [sentiment_col, sentiment_explanation_col, is_sentiment_processed_col],
    ] = response_df[["sentiment", "explanation", is_sentiment_processed_col]].values


def process_sentiment_in_chunks(
    df: pd.DataFrame,
    chunk_size: int,
    context: str,
    langchain_utility: LangChainUtility,
    sentiment_col: str,
    sentiment_explanation_col: str,
    separator: str = "---",
) -> pd.DataFrame:
    """
    Processes sentiment analysis in chunks and stores the results in the DataFrame.

    Parameters:
    - df (pd.DataFrame): DataFrame containing the summaries and texts for multiple records.
    - chunk_size (int): Number of records per chunk.
    - context (str): The processing context which can be one of "CLUSTER", "STORY" or "THEME".
    - sentiment_col (str): The sentiment column in which we need to store sentiment.
    - sentiment_explanation_col (str): The sentiment explanation column in which we need
        to store sentiment explanation.
    - separator (str, optional): Separator string to distinguish between records. Default is '---'.

    Returns:
    - pd.DataFrame: DataFrame with additional columns for sentiment and explanation.

    Exception Handling:
    - Exception: Logs an error occurs while processing sentiment after retries.
    """
    # Get summary, and is sentiment processed columns based on the context
    summary_col = f"{SummaryContext[context].value}_summary"
    is_sentiment_processed_col = SentimentProcessingField[context].value

    # Initialize the new columns
    df[sentiment_col] = None
    df[sentiment_explanation_col] = None
    df[is_sentiment_processed_col] = None

    # Split DataFrame into chunks
    num_chunks = (len(df) + chunk_size - 1) // chunk_size

    for chunk_index in range(num_chunks):
        start_index = chunk_index * chunk_size
        chunk_df = df[start_index : (chunk_index + 1) * chunk_size]

        # Generate prompt for the current chunk
        prompt = generate_prompt_for_chunk(chunk_df, summary_col, separator)

        # Processing sentiment with retry logic
        retry_count = 0
        while retry_count < int(MAX_PARSE_RETRIES):
            try:
                # Call model API and parse the response
                response = generate_sentiment_analysis(langchain_utility, prompt)
                parsed_response = extract_and_parse_json(response)

                # Update the DataFrame with the sentiment analysis results
                if parsed_response:
                    update_dataframe_with_sentiment(
                        df,
                        parsed_response,
                        sentiment_col,
                        sentiment_explanation_col,
                        is_sentiment_processed_col,
                        start_index,
                    )
                break

            except (InternalServerError, ResourceExhausted) as e:
                break

            except Exception as e:
                retry_count += 1

                if retry_count < int(MAX_PARSE_RETRIES):
                    logger.warning(
                        "Model response parsing attempt '%d' failed for sentiment: '%s'. Retrying in '%d' seconds...",
                        retry_count,
                        e,
                        int(PARSE_RETRY_DELAY),
                    )
                    time.sleep(int(PARSE_RETRY_DELAY))

                else:
                    message = f"Maximum {retry_count} retries reached, Failed to parse model response: {str(e)}"
                    logger.error(message)

    return df


def process_data_with_dask(
    langchain_utility: LangChainUtility, summaries_df: pd.DataFrame, context: str
) -> pd.DataFrame:
    """
    Process summaries data using Dask with specified sentiment function.
    - Optimize processing by partitioning the data into chunks based on unique context IDs.

    Parameters:
    - summaries_df (pd.DataFrame): Input Pandas DataFrame containing summaries data
        for theme, stories and clusters.
    - context (str): The processing context which can be one of "CLUSTER",
        "STORY" or "THEME".

    Returns:
    - pd.DataFrame: Processed Pandas DataFrame containing the final results after
        computation using dask.

    Exception Handling:
    - None
    """
    # Get sentiment, and sentiment explanation columns based on context
    sentiment_col = f"{SummaryContext[context].value}_sentiment"
    sentiment_explanation_col = f"{sentiment_col}_reasoning"

    # Group the DataFrame by unique context ID
    grouped = list(summaries_df.groupby(SummaryContextIdField[context].value))

    # Determine the number of unique IDs
    num_unique_ids = len(grouped)
    desired_partitions = os.cpu_count()  # The target number of partitions

    partitions = []

    if num_unique_ids >= desired_partitions:
        # Case 1: Unique IDs more than desired partitions, each gets its own partition
        # Convert the grouped DataFrame to delayed objects
        for _, group in grouped:
            partitions.append(
                delayed(process_sentiment_in_chunks)(
                    group,
                    int(SENTIMENT_PROMPT_CHUNK_SIZE),
                    context,
                    langchain_utility,
                    sentiment_col,
                    sentiment_explanation_col,
                )
            )

    else:
        # Case 2: Unique IDs fewer than desired partitions
        total_records = len(summaries_df)
        partitions_count = []

        # Calculate the number of partitions each unique ID should have based on its proportion
        for uid, group in grouped:
            num_records = len(group)
            proportion = num_records / total_records
            num_partitions = max(
                1, round(proportion * desired_partitions)
            )  # Ensure at least 1 partition
            partitions_count.append((uid, group, num_partitions))

        # Create partitions for each unique ID
        for uid, group, num_partitions in partitions_count:
            num_records = len(group)
            partition_size = math.ceil(num_records / num_partitions)

            for i in range(num_partitions):
                start_index = i * partition_size
                end_index = min((i + 1) * partition_size, num_records)
                partition_records = group.iloc[
                    start_index:end_index
                ].copy()  # Create a copy to avoid modification warnings

                if not partition_records.empty:  # Check if the partition is not empty
                    partitions.append(
                        delayed(process_sentiment_in_chunks)(
                            partition_records,
                            int(SENTIMENT_PROMPT_CHUNK_SIZE),
                            context,
                            langchain_utility,
                            sentiment_col,
                            sentiment_explanation_col,
                        )
                    )

    # Trigger computation
    results = dask.compute(*partitions, scheduler="threads")

    # Combine results back into a single DataFrame and reset the index
    summaries_df = pd.concat(results).reset_index(drop=True)

    # List of columns to check and their respective default values
    columns_to_check = {
        sentiment_col: DEFAULT_SENTIMENT,
        sentiment_explanation_col: DEFAULT_EXPLANATION,
    }

    # Iterate through each column and replace null values with the default string
    for col, default_string in columns_to_check.items():
        if col in summaries_df.columns:
            summaries_df[col] = summaries_df[col].fillna(default_string)

    # Drop columns that are not needed for the update operation
    columns_to_drop = [
        RAW_TEXT_COL,
        SummaryContextIdField[context].value,
        f"{SummaryContext[context].value}_summary",
    ]

    # Filter the columns to drop only those that exist in the DataFrame
    columns_to_drop_existing = [
        col for col in columns_to_drop if col in summaries_df.columns
    ]
    summaries_df.drop(columns=columns_to_drop_existing, inplace=True)
    logger.info(
        "Successfully processed DataFrame using Dask. Length: %d, Partitions: %d",
        len(summaries_df),
        len(partitions),
    )

    return summaries_df


def update_mongodb_collection(
    collection: Collection, dataframe: pd.DataFrame, encapsulation_marker: str
) -> bool:
    """
    Updates a MongoDB collection with data from a Pandas DataFrame using a bulk write operation.

    Parameters:
    - collection (pymongo.collection.Collection): The MongoDB collection to update.
    - dataframe (pandas.DataFrame): The DataFrame containing data to update.
    - encapsulation_marker (str): The name of the encapsulation marker.

    Returns:
    - bool: True if the collection update was successful, False otherwise.

    Exception Handling:
    - ValueError: Raised if the DataFrame does not contain an '_id' column.
    - Exception: If an error occurs during the update process, the error message
        is sent to the diagnostic engine.
    """
    try:
        # Check for mandatory '_id' column
        if "_id" not in dataframe.columns:
            raise ValueError(
                "DataFrame must contain an '_id' column for matching documents in MongoDB."
            )

        bulk_updates = []

        for _, row in dataframe.iterrows():
            mongo_id = ObjectId(row["_id"])
            update_data = {
                "$set": {col: row[col] for col in dataframe.columns if col != "_id"}
            }
            bulk_updates.append(UpdateOne({"_id": mongo_id}, update_data))

        # Execute bulk updates
        result = collection.bulk_write(bulk_updates)
        matched_count = result.matched_count
        updated_count = result.modified_count

        logger.info(
            "Updated %d documents for '%s' encapsulation marker in the '%s' collection",
            updated_count,
            encapsulation_marker,
            collection.name,
        )

        logger.info(
            "%d documents were not modified (already matched).",
            matched_count - updated_count,
        )
        return True

    except Exception as e:
        message = f"An error occurred while updating documents for '{encapsulation_marker}' encapsulation marker in the '{collection.name}' collection: {e}"

        meta_container = MetaContainer()
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.FAILED.value,
            message,
        )
        return False


@functions_framework.cloud_event
def main(cloud_event):
    """
    This function is the entry point that processes incoming cloud events, extracts relevant data,
    and initiates sentiment analysis in batches based on the provided payload.

    Steps:
    - Decode the payload of the cloud event to extract relevant data.
    - Retrieve organization, query, and encapsulation information from the payload.
    - Retrieve MongoDB connection details based on the organization ID from the organization vault.
    - Retrieve the capabilities database and LLM configuration collection.
    - Fetch query configuration from the organization database.
    - Get context such as cluster, story, or theme for which we need to process.
    - Send diagnostic with `PENDING` status to the diagnostic engine.
    - Set the configuration for the LangChain utility using model name and llm config collection.
    - Toggle encapsulation marker status to 'PENDING'.
    - Fetch summaries data(records which are not processed) associated with the encapsulation marker for
        particular context.
    - Parallelize sentiment analysis on the encapsulation marker data for particular context using Dask.
    - Update the 'RAC' collection with sentiment analysis data.
    - Update encapsulation marker status based on the success or failure.
    - If there are inactive documents for a particular encapsulation marker for a particular context:
        - Publish a loopback message to a Sentiment Multi Pub/Sub topic.
    - Else:
        - All documents for a particular encapsulation marker are processed successfully for all contexts:
            cluster, story and themes, send message to sequence coordinator.
        - If all encapsulation markers are completed for a particular query, send diagnostic with `COMPLETED` status.
    - Log any errors encountered during the process for troubleshooting purposes.

    Parameters:
    - cloud_event (google.cloud.functions.Context): CloudEvent data provided by the function framework.

    Returns:
    - {"Status":200}: For successfully execution.

    Exception Handling:
    - Exception: If an error occurs during the sentiment analysis process, the error message is sent
        to the diagnostic engine.
    """
    mongodb_client = None
    try:
        meta_container = MetaContainer()
        langchain_utility = LangChainUtility()

        payload = json.loads(
            base64.b64decode(cloud_event.data["message"]["data"]).decode("utf-8")
        )
        logger.info("Message received successfully: %s", payload)
        payload["publisher_type"] = CONSUMER_TYPE
        meta_container.set_payload_info(payload)

        encapsulation = payload["encapsulation"]

        organization_id = payload["organization_id"]
        query_id = payload["query_id"]
        encapsulation_marker = encapsulation["encapsulation_marker"]
        encapsulation_marker_id = encapsulation["_id"]

        org_account_info = OrganizationAccountInfo(organization_id)
        mongodb_url = org_account_info.mongodb_url
        organization_db_name = org_account_info.organization_db_name

        mongodb_client = get_mongodb_client(mongodb_url)
        organization_db = get_mongodb_db(mongodb_client, organization_db_name)
        query_collection = get_mongodb_collection(
            organization_db, QUERY_COLLECTION_NAME
        )

        query_config = get_query_config(
            query_collection, query_id, QUERY_PROJECTION_ATTRIBUTES
        )
        if not query_config:
            logger.warning("No configuration found for query_id: '%s'", query_id)
            return

        query_meta_data = query_config["meta_data"]
        query_meta_data = (
            query_meta_data[0] if isinstance(query_meta_data, list) else query_meta_data
        )

        encapsulation_marker_collection_name = query_meta_data[
            ENCAPSULATION_MARKER_COLLECTION_NAME_METADATA_KEY
        ]

        rac_collection_name = query_meta_data[RAC_COLLECTION_NAME_METADATA_KEY]
        encapsulation_marker_collection = get_mongodb_collection(
            organization_db, encapsulation_marker_collection_name
        )

        rac_collection = get_mongodb_collection(organization_db, rac_collection_name)

        # Check if the encapsulation marker with the given query ID has completed the sentiment.
        is_marker_completed = check_marker_status(
            encapsulation_marker_collection,
            encapsulation_marker_id,
            query_id,
            EncapsulationMarkerStatusColumn.SENTIMENT_MULTI_STATUS.value,
            EncapsulationMarkerStatus.COMPLETED.value,
        )

        # Get context such as cluster, story, or theme for which we need to process
        context = find_overall_processing_context(rac_collection, encapsulation_marker)
        if is_marker_completed or context == SummaryContext.ALL_PROCESSED.value.upper():
            logger.warning(
                "'%s' encapsulation Marker with query ID '%s' and encapsulation marker ID '%s' is already completed.",
                encapsulation_marker,
                query_id,
                encapsulation_marker_id,
            )

            return "Success", 200

        message = f"Capabilities sentiment multi script started successfully for query_id: {query_id}"
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.PENDING.value,
            message,
        )

        llm_config_collection = get_mongodb_collection(
            organization_db, ORG_CREDENTIALS_COLLECTION
        )

        # Set configuration for the langchain utility
        langchain_utility.set_configuration(
            query_meta_data[MODEL_NAME_METADATA_KEY], llm_config_collection
        )

        # Filter to find documents where sentiment is not processed
        query_filter = {
            "encapsulation_marker": encapsulation_marker,
            SentimentProcessingField[context].value: {"$exists": False},
        }

        # Update the encapsulation marker status to 'PENDING' for sentiment analysis
        toggle_marker_status(
            encapsulation_marker_collection,
            encapsulation_marker_id,
            query_id,
            EncapsulationMarkerStatusColumn.SENTIMENT_MULTI_STATUS.value,
            EncapsulationMarkerStatus.PENDING.value,
        )

        summaries_df = fetch_collection_data(
            rac_collection, query_filter, encapsulation_marker, context
        )

        if summaries_df is not None:
            summaries_df = process_data_with_dask(
                langchain_utility, summaries_df, context
            )
            success = update_mongodb_collection(
                rac_collection, summaries_df, encapsulation_marker
            )

            if success:
                # Retrieve the context for a specific encapsulation marker for which the sentiment is not processed
                context = find_overall_processing_context(
                    rac_collection, encapsulation_marker
                )

                if context != SummaryContext.ALL_PROCESSED.value.upper():
                    publish_pubsub_message(
                        PROJECT_ID, SENTIMENT_MULTI_TOPIC_ID, payload
                    )
                    logger.info(
                        "Published loopback message for inactive documents with '%s encapsulation marker' for '%s' context",
                        encapsulation_marker,
                        context,
                    )

                else:
                    toggle_marker_status(
                        encapsulation_marker_collection,
                        encapsulation_marker_id,
                        query_id,
                        EncapsulationMarkerStatusColumn.SENTIMENT_MULTI_STATUS.value,
                        EncapsulationMarkerStatus.COMPLETED.value,
                    )
                    publish_pubsub_message(
                        PROJECT_ID, SEQUENCE_COORDINATOR_TOPIC_ID, payload
                    )

                    if check_query_status(
                        encapsulation_marker_collection,
                        query_id,
                        EncapsulationMarkerStatusColumn.SENTIMENT_MULTI_STATUS.value,
                        EncapsulationMarkerStatus.COMPLETED.value,
                    ):
                        message = f"Capabilities sentiment multi script completed successfully for query_id: {query_id}"
                        meta_container.send_diagnostic(
                            DiagnosticActionType.UPDATE.value,
                            DiagnosticStatus.COMPLETED.value,
                            message,
                        )

            else:
                toggle_marker_status(
                    encapsulation_marker_collection,
                    encapsulation_marker_id,
                    query_id,
                    EncapsulationMarkerStatusColumn.SENTIMENT_MULTI_STATUS.value,
                    EncapsulationMarkerStatus.FAILED.value,
                )

        mongodb_client.close()
        return "Success", 200

    except Exception as e:
        message = f"An error occurred during capabilities sentiment multi: {e}"
        meta_container.send_diagnostic(
            DiagnosticActionType.UPDATE.value,
            DiagnosticStatus.FAILED.value,
            message,
        )

        toggle_marker_status(
            encapsulation_marker_collection,
            encapsulation_marker_id,
            query_id,
            EncapsulationMarkerStatusColumn.SENTIMENT_MULTI_STATUS.value,
            EncapsulationMarkerStatus.FAILED.value,
        )

    finally:
        if mongodb_client is not None:
            mongodb_client.close()
