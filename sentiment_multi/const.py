"""
This module contains constants used in files of sentiment multi consumer.
"""

import os

ORG_CREDENTIALS_COLLECTION = os.getenv("ORG_CREDENTIALS_COLLECTION")
QUERY_COLLECTION_NAME = os.getenv("QUERY_COLLECTION_NAME")

PROJECT_ID = os.getenv("PROJECT_ID")
MAX_PARSE_RETRIES = os.getenv("MAX_PARSE_RETRIES")
MAX_UNIQUE_CONTEXT_IDS = os.getenv("MAX_UNIQUE_CONTEXT_IDS")
PARSE_RETRY_DELAY = os.getenv("PARSE_RETRY_DELAY")
SENTIMENT_PROMPT_CHUNK_SIZE = os.getenv("SENTIMENT_PROMPT_CHUNK_SIZE")
SENTIMENT_MULTI_TOPIC_ID = os.getenv("SENTIMENT_MULTI_TOPIC_ID")
SEQUENCE_COORDINATOR_TOPIC_ID = os.getenv("SEQUENCE_COORDINATOR_TOPIC_ID")
LOOPBACK_THRESHOLD = os.getenv("LOOPBACK_THRESHOLD")

RAW_TEXT_COL = "BODY1"
ENCAPSULATION_MARKER_COLLECTION_NAME_METADATA_KEY = (
    "ENCAPSULATION_MARKER_COLLECTION_NAME"
)
RAC_COLLECTION_NAME_METADATA_KEY = "RAC_COLLECTION_NAME"
MODEL_NAME_METADATA_KEY = "MODEL_NAME"
QUERY_PROJECTION_ATTRIBUTES = {
    f"meta_data.{ENCAPSULATION_MARKER_COLLECTION_NAME_METADATA_KEY}": 1,
    f"meta_data.{MODEL_NAME_METADATA_KEY}": 1,
    f"meta_data.{RAC_COLLECTION_NAME_METADATA_KEY}": 1,
}

DEFAULT_EXPLANATION = "No explanation generated"
DEFAULT_SENTIMENT = "No sentiment generated"

# Define the allowed sentiment classifications
SENTIMENT_CLASSIFICATION = ["Negative", "Neutral", "Positive"]

# ------------------------------------- Sentiment Generation Prompts ----------------------------------------------------------

SENTIMENT_BATCH_PROMPT = """
    Classify each text under the given context into one of these sentiment categories: 
    Neutral, Positive, Negative. Provide an explanation for the chosen sentiment category 
    for each text. Ensure that the explanation does not contain any double quotes. 
    The explanations should be written clearly and concisely without using double quotes 
    or any additional punctuation that might conflict with JSON formatting. Return the 
    results as an array of objects, where each object contains the record_number, 
    the sentiment, and the explanation.

CONTEXT: ```{summary}```

TEXTS: ```{combined_texts}```

OUTPUT: """

SENTIMENT_BATCH_PROMPT_NO_CONTEXT = """
    Classify each text into one of these sentiment categories: Neutral, Positive, Negative. 
    Provide an explanation for the chosen sentiment category for each text. Ensure that 
    the explanation does not contain any double quotes. The explanations should be written 
    clearly and concisely without using double quotes or any additional punctuation that 
    might conflict with JSON formatting. Return the results as an array of objects, where 
    each object contains the record_number, the sentiment, and the explanation.

TEXTS: ```{combined_texts}```

OUTPUT: """
